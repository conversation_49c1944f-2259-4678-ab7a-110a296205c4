<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <groupId>yunti.apps</groupId>
    <artifactId>cloud-demand-app</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <parent>
        <groupId>yunti</groupId>
        <artifactId>yunti-spring-boot-parent</artifactId>
        <version>2.0.5.3-SNAPSHOT</version>
    </parent>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>tech.tablesaw</groupId>
            <artifactId>tablesaw-core</artifactId>
            <version>0.43.1</version>
        </dependency>

        <dependency>
            <groupId>yunti</groupId>
            <artifactId>yunti-spring-boot-core</artifactId>
        </dependency>

        <dependency>
            <groupId>yunti</groupId>
            <artifactId>yunti-spring-boot-dao</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>3.6.1</version>
        </dependency>


        <!--        <dependency>-->
        <!--            <groupId>yunti</groupId>-->
        <!--            <artifactId>yunti-spring-boot-zeebe</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>yunti</groupId>
            <artifactId>yunti-spring-boot-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--http接口框架 1.1 支持外接日志 -->
        <dependency>
            <groupId>easily</groupId>
            <artifactId>cs-easily-tp</artifactId>
            <version>2.0.17</version>
        </dependency>

        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>task-run</groupId>
            <artifactId>task-run-exporter</artifactId>
            <version>1.0.12</version>
        </dependency>

        <dependency>
            <groupId>yunti</groupId>
            <artifactId>yunti-teg-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.9</version>
        </dependency>

        <dependency>
            <groupId>com.pugwoo</groupId>
            <artifactId>nimble-orm</artifactId>
            <version>1.7.3</version>
        </dependency>
        <dependency>
            <groupId>com.pugwoo</groupId>
            <artifactId>redis-helper</artifactId>
            <version>1.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.pugwoo</groupId>
            <artifactId>woo-utils</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <artifactId>cloud-api</artifactId>
            <groupId>erp.base</groupId>
            <version>2.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.10.0</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.10.0</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.22</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/de.siegmar/fastcsv -->
        <dependency>
            <groupId>de.siegmar</groupId>
            <artifactId>fastcsv</artifactId>
            <version>2.2.1</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.11</version>
        </dependency>

        <!-- jwt，用于转发云霄请求 -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <artifactId>fiber-support</artifactId>
            <groupId>erp.base</groupId>
            <version>1.0.0</version>
        </dependency>

        <!-- Apache Calcite 核心库 -->
        <dependency>
            <groupId>org.apache.calcite</groupId>
            <artifactId>calcite-core</artifactId>
            <version>1.37.0</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.0</version>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <groupId>org.springframework.boot</groupId>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <skipSource>true</skipSource>
                </configuration>
                <groupId>org.apache.maven.plugins</groupId>
            </plugin>
        </plugins>
    </build>

</project>
