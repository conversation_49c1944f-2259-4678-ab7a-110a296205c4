yunti:
  app-name: "cloud-demand-app"
  web:
    port: 80
    context-path: "/cloud-demand-app"
  rainbow:
    enabled: true
    app-id: "c8269224-03c9-4d55-a081-589c74e09f5e"
    main-group: "${stage:DEV}.${yunti.app-name}"
  security:
    ignore-paths:
      skip:
        - "/ppl13week/queryAllInstanceType"
        - "/ppl13week/queryAllCityName"
        - "/ppl13week/queryAllZoneName"
        - "/ppl13week/queryProcessingVersionMap"
        - "/wx/push"

management:
  endpoints:
    web:
      exposure:
        include: "*"
  health:
    db:
      enabled: false
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      app: "${yunti.app-name}"

tp:
  client:
    wx:
      enable: true
      token: "3Ptvowu8mvGBLQt6Hyd1lpw2"
      receive-id: ""
      encoding-aes-key: "yuIefS9fqHuftUVqaAb0jMSxSOEQrSVu4B4p63UpOhH"

spring:
  thymeleaf:
    templateResolverOrder: 1