<html>
<head>
  <title>cloud-demand-app</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/blueimp-md5/2.18.0/js/md5.min.js"></script>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    $(document).ready(function () {
      $.ajax({
        url: "/cloud-demand-app/ops/list",
        method: "GET",
        success: function (data) {
          let list = data.map((path) => {
            return `
            <li>
                ${path}
                <button class="invokeBtn" data-path="${path}">Invoke</button>
            </li>`;
          });
          $("#interfaceList").html(list);

          // 点击按钮事件
          $(".invokeBtn").click(function () {
            let path = $(this).data("path");
            let password = prompt("Please enter the password:");
            // 对用户输入的密码进行MD5加密
            let md5Password = md5("~qQ12-+"+password+"!@#Qwez`^vbZdcff7dsf098xvcbfd");
            // 验证MD5密码是否正确
            if (md5Password !== "5d400639ddc9247ce26546753428ef24") {
              alert("Invalid password");
              return;
            }
            // 根据查找到的方法（路径）打开新页面
            let baseUrl = window.location.origin + "/cloud-demand-app/ops/";
            if (path.startsWith("/")) {
              path = path.substring(1);
            }
            window.open(baseUrl + path, "_blank");
          });
        }
      });
    });
  </script>
</head>
<body>
<h1 style="padding:20px; margin:20px auto; width:100%">cloud-demand-app 已就绪！</h1>
<button onClick="copy()">复制Cookie</button>
</body>
<script>
  function copy() {
    const text = document.cookie;
    // text是复制文本
    // 创建input元素
    const el = document.createElement('input')
    // 给input元素赋值需要复制的文本
    el.setAttribute('value', text)
    // 将input元素插入页面
    document.body.appendChild(el)
    // 选中input元素的文本
    el.select()
    // 复制内容到剪贴板
    document.execCommand('copy')
    // 删除input元素
    document.body.removeChild(el)
  }
</script>


<h3>ops</h3>
<ul id="interfaceList"></ul>
</html>
