SELECT if(${has_year_month}, formatDateTime(begin_buy_date, '%Y%m') as `year_month`, null) as any_year_month,
       if(${has_product}, '${productCategory}', null)                                      as any_product,
       if(${has_industry_dept}, industry_dept, null)                                       as any_industry_dept,
       if(${has_demand_type}, '新增', null)                                                as any_demand_type,
       if(${has_customer_short_name}, customer_short_name, null)                           as any_customer_short_name,
       if(${has_customer_name}, customer_name, null)                                       as any_customer_name,
       if(${has_customer_uin}, customer_uin, null)                                         as any_customer_uin,
       if(${has_instance_type}, UPPER(instance_type), null)                                as any_instance_type,
       if(${has_city}, region_name, null)                                                  as any_city,
       null                                                                                as bill_month_avg_a,
       null                                                                                as b_monthly_usage,
       null                                                                                as d_monthly_usage,
       null                                                                                as e_monthly_usage,
       null                                                                                as e1_monthly_usage,
       null                                                                                as e2_monthly_usage,
       null                                                                                as total_sch_resource_size,
       null                                                                                as total_pre_resource_size,
       sum(case
               when '${productCategory}' = 'CVM' then total_core
               when '${productCategory}' = 'GPU' then gpu_num
               else 0 end)                                                                 as amount
FROM std_crp.dws_crp_ppl_item_version_newest_cf ${where}
GROUP BY any_year_month,
         any_product,
         any_industry_dept,
         any_demand_type,
         any_customer_short_name,
         any_customer_name,
         any_customer_uin,
         any_instance_type,
         any_city
having amount != 0
order by
    any_year_month,
    any_product,
    any_industry_dept,
    any_demand_type,
    any_customer_short_name,
    any_customer_name,
    any_customer_uin,
    any_instance_type,
    any_city
