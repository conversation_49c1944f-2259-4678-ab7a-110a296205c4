select instance_type,
       if(${has_year_month}, formatDateTime(begin_buy_date, '%Y%m'), null) as year_month,
       if(${has_industry_dept}, industry_dept, null)                        as industry_dept,
       if(${has_customer_short_name}, customer_short_name, null)            as customer_short_name,
       sum(case
               when '${productCategory}' = 'CVM' then total_core
               when '${productCategory}' = 'GPU' then toInt32(gpu_num)
           else 0 end)                                            as amount
from std_crp.dws_crp_ppl_item_version_newest_cf ${where}
group by year_month, industry_dept, customer_short_name, instance_type
having amount != 0