SELECT
    current.product,
    current.plan_product_name,
    current.data_month,
    current.statics_category,
    current.b_a,
    current.c_a,
    current.d_a,
    current.e1_a,
    current.e2_a,
    current.e_a,
    (current.b_a + current.c_a + current.d_a + current.e_a) AS bcde_a,
    current.scheculer_return_cpu,
    current.pre_return_cpu,
    current.prepay_bill_a,
    current.postpay_bill_a,
    current.total_bill_a,
    current.b_monthly_usage,
    current.d_monthly_usage,
    current.e1_monthly_usage,
    current.e2_monthly_usage,
    current.e_monthly_usage,
    current.incentive_cost,
    previous.data_month AS previous_month,
    previous.b_a AS previous_b_a,
    previous.c_a AS previous_c_a,
    previous.d_a AS previous_d_a,
    previous.e1_a AS previous_e1_a,
    previous.e2_a AS previous_e2_a,
    previous.e_a AS previous_e_a,
    (previous.b_a + previous.c_a + previous.d_a + previous.e_a) AS previous_bcde_a,
    previous.scheculer_return_cpu AS previous_scheculer_return_cpu,
    previous.pre_return_cpu AS previous_pre_return_cpu,
    previous.prepay_bill_a AS previous_prepay_bill_a,
    previous.postpay_bill_a AS previous_postpay_bill_a,
    previous.total_bill_a AS previous_total_bill_a,
    previous.b_monthly_usage as previous_b_monthly_usage,
    previous.d_monthly_usage as previous_d_monthly_usage,
    previous.e1_monthly_usage as previous_e1_monthly_usage,
    previous.e2_monthly_usage as previous_e2_monthly_usage,
    previous.e_monthly_usage as previous_e_monthly_usage,
    previous.incentive_cost as previous_incentive_cost

FROM
    real_service_cost_report_prod_type current
LEFT JOIN
    real_service_cost_report_prod_type previous
ON
    current.statics_category = previous.statics_category
    and current.product = previous.product
    and current.plan_product_name = previous.plan_product_name
    AND (
    ((current.data_month % 100 != 1 AND current.data_month = previous.data_month + 1)
    OR (current.data_month % 100 = 1 AND previous.data_month = (current.data_month - 89)))
    )