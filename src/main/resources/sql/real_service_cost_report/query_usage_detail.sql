SELECT if(${has_year_month}, `year_month`, null)                 as any_year_month,
       if(${has_product}, product, null)                         as any_product,
       if(${has_industry_dept}, industry_dept, null)             as any_industry_dept,
       if(${has_demand_type}, demand_type, null)                 as any_demand_type,
       if(${has_customer_short_name}, customer_short_name, null) as any_customer_short_name,
       if(${has_customer_name}, customer_name, null)             as any_customer_name,
       if(${has_customer_uin}, customer_uin, null)               as any_customer_uin,
       if(${has_instance_type}, UPPER(instance_type), null)      as any_instance_type,
       if(${has_city}, city, null)                               as any_city,
       sum(bill_month_avg_a)                                     as bill_month_avg_a,
       sum(b_monthly_usage)                                      as b_monthly_usage,
       sum(d_monthly_usage)                                      as d_monthly_usage,
       sum(e_monthly_usage)                                      as e_monthly_usage,
       sum(e1_monthly_usage)                                     as e1_monthly_usage,
       sum(e2_monthly_usage)                                     as e2_monthly_usage,
       sum(total_sch_resource_size)                              as total_sch_resource_size,
       sum(total_pre_resource_size)                              as total_pre_resource_size,
       null                                                      as amount
FROM std_crp.report_real_service_usage_detail_df ${where}
GROUP BY any_year_month,
         any_product,
         any_industry_dept,
         any_demand_type,
         any_customer_short_name,
         any_customer_name,
         any_customer_uin,
         any_instance_type,
         any_city
order by any_year_month,
         any_product,
         any_industry_dept,
         any_demand_type,
         any_customer_short_name,
         any_customer_name,
         any_customer_uin,
         any_instance_type,
         any_city
