select
    industry_dept1 as industry_dept,
    any(customhouse_title1) as customhouse_title,
    any(region1) as region,
    region_name1 as region_name,
    instance_type1 as gins_family,
    sum(new_core) as new_diff, -- 增量
    sum(ret_core) as ret_diff, --   退回
    sum(cur_core) as last_day_num -- 当前量
from (
    select year,month,
    any(month_start_date) as month_start_date1, any(month_end_date) as month_end_date1,
    uin, any(industry_dept) as industry_dept1, zone_name,
    any(customhouse_title) as customhouse_title1, any(region) as region1, any(region_name) as region_name1,
    instance_model, any(instance_type) as instance_type1,

    sum(case when stat_time=month_end_date and change_bill_service_core_from_last_month>0 then change_bill_service_core_from_last_month else 0 end) as new_core,
    sum(case when stat_time=month_end_date and change_bill_service_core_from_last_month<0 then -change_bill_service_core_from_last_month else 0 end) as ret_core,
    sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as cur_core

    from dwd_txy_scale_df
    where 1=1

    ${CONDITION}

    and stat_time between :inputDateStart and :inputDateEnd

    group by uin,zone_name,instance_model,year,month
)
group by region_name1,instance_type1,industry_dept1