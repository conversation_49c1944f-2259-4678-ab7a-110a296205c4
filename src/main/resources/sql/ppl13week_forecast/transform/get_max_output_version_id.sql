

with task_ids as (select id
                  from ppl_forecast_predict_task
                  where category = '方案406：常规项目-机型族-地域-月度ARIMAX'
                    and is_enable = 1),
     max_output_version_id as (select max(id)
                               from ppl_forecast_predict_task_output_version
                               where output_version_type = 'SPLIT_FOR_ZIYAN'
                                 and task_id in (select * from task_ids)
                               group by task_id)
select a.*
from ppl_forecast_predict_task_output_version a
         left join ppl_forecast_predict_task b on a.task_id = b.id
where a.id in (select * from max_output_version_id)
    and year(input_date_end) =  ? and month(input_date_end) = ?