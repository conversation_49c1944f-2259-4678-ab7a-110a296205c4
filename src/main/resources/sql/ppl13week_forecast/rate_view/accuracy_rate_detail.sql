with detail as (select concat(toString(year),
                              if(month < 10, concat('0', toString(month)), toString(month)))       as year_month,
                       seq_type,
                       gins_family,
                       region_name,
                       predict_index,
                       real_core_num,
                       predict_core_num                                                            as predict_core_num,
                       has_predict,
                       cur_core_num,
                       sum(if(has_predict and predict_index = 1, real_core_num, 0))
                                                                                                      over (partition by year, month, seq_type,predict_index)                 as partition_sum_real_core_num1,
                        sum(if(has_predict and predict_index = 2, real_core_num, 0))
                            over (partition by year, month, seq_type,predict_index)                 as partition_sum_real_core_num2,
                        sum(if(has_predict and predict_index = 3, real_core_num, 0))
                            over (partition by year, month, seq_type, predict_index)                as partition_sum_real_core_num3,
                        sum(if(has_predict and predict_index = 4, real_core_num, 0))
                            over (partition by year, month, seq_type, predict_index)                as partition_sum_real_core_num4,
                        sum(if(has_predict and predict_index = 5, real_core_num, 0))
                            over (partition by year, month, seq_type, predict_index)                as partition_sum_real_core_num5,
                        sum(if(has_predict and predict_index = 6, real_core_num, 0))
                            over (partition by year, month, seq_type, predict_index)                as partition_sum_real_core_num6,
                        sum(real_core_num) over (partition by year, month, seq_type, predict_index) as partition_sum_real_core_num,
                        if(has_predict and predict_index = 1, real_core_num, 0)                     as real_core_num1,
                       if(has_predict and predict_index = 2, real_core_num, 0)                     as real_core_num2,
                       if(has_predict and predict_index = 3, real_core_num, 0)                     as real_core_num3,
                       if(has_predict and predict_index = 4, real_core_num, 0)                     as real_core_num4,
                       if(has_predict and predict_index = 5, real_core_num, 0)                     as real_core_num5,
                       if(has_predict and predict_index = 6, real_core_num, 0)                     as real_core_num6
                from dwd_crp_longtail_forecast_item_df ${WHERE})
   , rate_deatail as (select year_month,
                             seq_type,
                             gins_family,
                             region_name,
                             toFloat64(sum(if(predict_index = 1, predict_core_num, 0)))                                  as predict_core_num1,
                             toFloat64(sum(if(predict_index = 2, predict_core_num, 0)))                                  as predict_core_num2,
                             toFloat64(sum(if(predict_index = 3, predict_core_num, 0)))                                  as predict_core_num3,
                             toFloat64(sum(if(predict_index = 4, predict_core_num, 0)))                                  as predict_core_num4,
                             toFloat64(sum(if(predict_index = 5, predict_core_num, 0)))                                  as predict_core_num5,
                             toFloat64(sum(if(predict_index = 6, predict_core_num, 0)))                                  as predict_core_num6,
                             -- 1-3 都要有预测才计算 532
                             toFloat64(sum(if(predict_index in (1, 2, 3) and not has_predict, 0,
                                                if(predict_index = 1, predict_core_num, 0) * 0.2 +
                                                if(predict_index = 2, predict_core_num, 0) * 0.3 +
                                                if(predict_index = 3, predict_core_num, 0) * 0.5
                                 )))                                                                   as predict_core_num532,

                             toFloat64(sum(if(predict_index in (1, 2) and not has_predict, 0,
                                                if(predict_index = 1, predict_core_num, 0) * 0.5 +
                                                if(predict_index = 2, predict_core_num, 0) * 0.5
                                 )))                                                                   as predict_core_num55,

                             if(predict_core_num1 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num1 <= real_core_num, predict_core_num1, real_core_num) /
                                if(predict_core_num1 >= real_core_num, predict_core_num1, real_core_num))     as min_max_1,
                             if(predict_core_num2 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num2 <= real_core_num, predict_core_num2, real_core_num) /
                                if(predict_core_num2 >= real_core_num, predict_core_num2, real_core_num))     as min_max_2,
                             if(predict_core_num3 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num3 <= real_core_num, predict_core_num3, real_core_num) /
                                if(predict_core_num3 >= real_core_num, predict_core_num3, real_core_num))     as min_max_3,
                             if(predict_core_num4 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num4 <= real_core_num, predict_core_num4, real_core_num) /
                                if(predict_core_num4 >= real_core_num, predict_core_num4, real_core_num))     as min_max_4,
                             if(predict_core_num5 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num5 <= real_core_num, predict_core_num5, real_core_num) /
                                if(predict_core_num5 >= real_core_num, predict_core_num5, real_core_num))     as min_max_5,
                             if(predict_core_num6 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num6 <= real_core_num, predict_core_num6, real_core_num) /
                                if(predict_core_num6 >= real_core_num, predict_core_num6, real_core_num))     as min_max_6,

                             if(predict_core_num532 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num532 <= real_core_num, predict_core_num532, real_core_num) /
                                if(predict_core_num532 >= real_core_num, predict_core_num532, real_core_num)) as min_max_532,

                             if(predict_core_num55 < 0.001 and real_core_num < 0.0001, 0,
                                if(predict_core_num55 <= real_core_num, predict_core_num55, real_core_num) /
                                if(predict_core_num55 >= real_core_num, predict_core_num55, real_core_num)) as min_max_55,

                             toFloat64(max(real_core_num))                                                               as real_core_num,
                             toFloat64(max(cur_core_num))                                                                as cur_core_num,
                             if(real_core_num < 0.001 and any(partition_sum_real_core_num) < 0.001, 0,
                                real_core_num / any(partition_sum_real_core_num))                             as weight,

                             if(max(real_core_num1) < 0.001 and max(partition_sum_real_core_num1) < 0.001, 0,
                                toFloat64(max(real_core_num1)) / toFloat64(max(partition_sum_real_core_num1))) *
                             toFloat64(min_max_1)                                                                        as weightRate1,
                             if(max(real_core_num2) < 0.001 and max(partition_sum_real_core_num2) < 0.001, 0,
                                toFloat64(max(real_core_num2)) / toFloat64(max(partition_sum_real_core_num2))) *
                             toFloat64(min_max_2)                                                                        as weightRate2,
                             if(max(real_core_num3) < 0.001 and max(partition_sum_real_core_num3) < 0.001, 0,
                                toFloat64(max(real_core_num3)) / toFloat64(max(partition_sum_real_core_num3))) *
                             toFloat64(min_max_3)                                                                        as weightRate3,
                             if(max(real_core_num4) < 0.001 and max(partition_sum_real_core_num4) < 0.001, 0,
                                toFloat64(max(real_core_num4)) / toFloat64(max(partition_sum_real_core_num4))) *
                             toFloat64(min_max_4)                                                                        as weightRate4,
                             if(max(real_core_num5) < 0.001 and max(partition_sum_real_core_num5) < 0.001, 0,
                                toFloat64(max(real_core_num5)) / toFloat64(max(partition_sum_real_core_num5))) *
                             toFloat64(min_max_5)                                                                        as weightRate5,
                             if(max(real_core_num6) < 0.001 and max(partition_sum_real_core_num6) < 0.001, 0,
                                toFloat64(max(real_core_num6)) / toFloat64(max(partition_sum_real_core_num6))) *
                             toFloat64(min_max_6)                                                                        as weightRate6,
                             if(max(real_core_num3) < 0.001 and max(partition_sum_real_core_num3) < 0.001, 0,
                                toFloat64(max(real_core_num3)) / toFloat64(max(partition_sum_real_core_num3))) *
                             toFloat64(min_max_532)                                                                      as weightRate532,

                             if(max(real_core_num2) < 0.001 and max(partition_sum_real_core_num2) < 0.001, 0,
                                toFloat64(max(real_core_num2)) / toFloat64(max(partition_sum_real_core_num2))) *
                             toFloat64(min_max_55)                                                                      as weightRate55
                      from detail
                      group by year_month, seq_type, gins_family, region_name)

select *
from rate_deatail
