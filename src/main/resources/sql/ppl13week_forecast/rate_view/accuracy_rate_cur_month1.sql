

with   params as (select ? as predict_type),
    detail as (select year,
    month,
    seq_type,
    gins_family,
    region_name,
    toDecimal64(real_core_num * ?,6) as real_core_num,
    case
    when '532' = (select predict_type from params) then predict_core_num_532
    when '55' = (select predict_type from params) then
    toDecimal64(  predict_core_num * 0.5 + predict_core_num_1 * 0.5,6)
    else predict_core_num end                                as predict_core_num,
    sum(real_core_num) over (partition by year, month, seq_type ${PARTITION} ) as partition_sum_real_core_num
from dwd_crp_longtail_forecast_item_for_mrp_df
    ${WHERE}
)
select concat(toString(year),
              if(month < 10, concat('0', toString(month)), toString(month))) as year_month,
       seq_type,
       region_name,
       gins_family,
       sum(real_core_num)                as total_real_core_num,
       sum(predict_core_num)             as total_predict_core_num,
       sum(if(real_core_num < 0.001 and predict_core_num < 0.001, 0,
              if(real_core_num <= predict_core_num, real_core_num, predict_core_num) /
              if(real_core_num >= predict_core_num, real_core_num, predict_core_num))
           * if(partition_sum_real_core_num < 0.001, 0, real_core_num / partition_sum_real_core_num)
           )                             as rate
from detail
-- 这里不要加条件
group by year, month, seq_type, ${REGION_NAME} as region_name, ${GINS_FAMILY} as gins_family