with detail as (select stat_time,
                       holiday_year,
                       holiday_week,
                       seq_type,
                       gins_family,
                       region_name,

    any(real_core_num)                                                                 as any_real_core_num,
    count(distinct real_core_num)                                                      as distinct_real_core_num,

    any(cur_core_num)                                                                  as any_cur_core_num,
    count(distinct cur_core_num)                                                       as distinct_cur_core_num,

    toDecimal64(sum(if(predict_index in (1, 2, 3, 4), predict_core_num, 0)), 6)        as predict_core_sum1,
    toDecimal64(sum(if(predict_index in (5, 6, 7, 8), predict_core_num, 0)), 6)        as predict_core_sum2,
    toDecimal64(sum(if(predict_index in (9, 10, 11, 12, 13), predict_core_num, 0)), 6) as predict_core_sum3,

    sum(if(predict_index in (1, 2, 3, 4), if(has_predict, 1, 0), 0))                   as m1_flag_num,
    sum(if(predict_index in (5, 6, 7, 8), if(has_predict, 1, 0), 0))                   as m2_flag_num,
    sum(if(predict_index in (9, 10, 11, 12, 13), if(has_predict, 1, 0), 0))            as m3_flag_num
from dwd_crp_longtail_forecast_item_df
 /*${WHERE}*/
-- where category = '方案310：机型收敛-包年包月加弹性-行业加内领-周ARIMAX'
group by stat_time, holiday_year,
    holiday_week,
    seq_type,
    gins_family,
    region_name)
        ,
    detail_with_total as (select stat_time,
    holiday_year,
    holiday_week,
    seq_type,
    gins_family,
    region_name,

    any_real_core_num                                                                                    as real_core_num,

    any_cur_core_num                                                                                     as cur_core_num,


    if(m1_flag_num = 4, predict_core_sum1 / m1_flag_num, 0)                                              as predict_core_num1,
    if(m2_flag_num = 4, predict_core_sum2 / m2_flag_num, 0)                                              as predict_core_num2,
    if(m3_flag_num = 5, predict_core_sum3 / m3_flag_num, 0)                                              as predict_core_num3,
    toDecimal64(if(m3_flag_num = 5 and m2_flag_num = 4 and m1_flag_num = 4,
    predict_core_sum3 / m3_flag_num * 0.5 + predict_core_sum2 / m2_flag_num * 0.5
    + predict_core_sum1 / m1_flag_num * 0.5, 0), 6)                                   as predict_core_num532,
    toDecimal64(if(m3_flag_num = 5 and m2_flag_num = 4 and m1_flag_num = 4,
    predict_core_sum2 / m2_flag_num * 0.5 + predict_core_sum1 / m1_flag_num * 0.5, 0), 6) as predict_core_num55,


    m1_flag_num,
    m2_flag_num,
    m3_flag_num,


    sum(if(m1_flag_num = 4, real_core_num, 0))
    over (partition by stat_time,holiday_year,holiday_week, seq_type /*${PARTITION}*/)               as partition_sum_real_core_num1,
    sum(if(m2_flag_num = 4, real_core_num, 0))
    over (partition by stat_time,holiday_year,holiday_week, seq_type /*${PARTITION}*/)               as partition_sum_real_core_num2,
    sum(if(m3_flag_num = 5, real_core_num, 0))
    over (partition by stat_time,holiday_year,holiday_week, seq_type /*${PARTITION}*/)               as partition_sum_real_core_num3,

    if(partition_sum_real_core_num1 < 0.001, 0,
    real_core_num / partition_sum_real_core_num1)                                                     as weight,


    if(predict_core_num1 < 0.001 and real_core_num < 0.0001, 0,
    if(predict_core_num1 <= real_core_num, predict_core_num1, real_core_num) /
    if(predict_core_num1 >= real_core_num, predict_core_num1, real_core_num))                         as min_max_1,
    if(predict_core_num2 < 0.001 and real_core_num < 0.0001, 0,
    if(predict_core_num2 <= real_core_num, predict_core_num2, real_core_num) /
    if(predict_core_num2 >= real_core_num, predict_core_num2, real_core_num))                         as min_max_2,
    if(predict_core_num3 < 0.001 and real_core_num < 0.0001, 0,
    if(predict_core_num3 <= real_core_num, predict_core_num3, real_core_num) /
    if(predict_core_num3 >= real_core_num, predict_core_num3, real_core_num))                         as min_max_3,
    if(predict_core_num532 < 0.001 and real_core_num < 0.0001, 0,
    if(predict_core_num532 <= real_core_num, predict_core_num532, real_core_num) /
    if(predict_core_num532 >= real_core_num, predict_core_num532, real_core_num))                     as min_max_532,
    if(predict_core_num55 < 0.001 and real_core_num < 0.0001, 0,
    if(predict_core_num55 <= real_core_num, predict_core_num55, real_core_num) /
    if(predict_core_num55 >= real_core_num, predict_core_num55, real_core_num))                       as min_max_55,

    if(partition_sum_real_core_num1 < 0.001, 0,
    real_core_num / partition_sum_real_core_num1) * min_max_1                                         as weightRate1,
    if(partition_sum_real_core_num2 < 0.001, 0,
    real_core_num / partition_sum_real_core_num2) * min_max_2                                         as weightRate2,
    if(partition_sum_real_core_num3 < 0.001, 0,
    real_core_num / partition_sum_real_core_num3) * min_max_3                                         as weightRate3,
    if(partition_sum_real_core_num3 < 0.001, 0,
    real_core_num / partition_sum_real_core_num3) * min_max_532                                       as weightRate532,
    if(partition_sum_real_core_num2 < 0.001, 0,
    real_core_num / partition_sum_real_core_num2) * min_max_55                                        as weightRate55

from detail)

select stat_time as `year_month`,
--        holiday_year,
--        holiday_week,
       seq_type,
       gins_family,
       region_name,
       real_core_num,
       cur_core_num,
       predict_core_num1,
       predict_core_num2,
       predict_core_num3,
       0         as predict_core_num4,
       0         as predict_core_num5,
       0         as predict_core_num6,
       predict_core_num532,
       predict_core_num55,
       m1_flag_num,
       m2_flag_num,
       m3_flag_num,
       partition_sum_real_core_num1,
       partition_sum_real_core_num2,
       partition_sum_real_core_num3,
       weight,
       min_max_1,
       min_max_2,
       min_max_3,
       0         as min_max_4,
       0         as min_max_5,
       0         as min_max_6,
       min_max_532,
       min_max_55,
       weightRate1,
       weightRate2,
       weightRate3,
       0         as weightRate4,
       0         as weightRate5,
       0         as weightRate6,
       weightRate532,
       weightRate55
from detail_with_total