

-- 预测只展示最新的数据，不进行平均值计算

with filter_detail as (
    select *
    from dwd_crp_longtail_forecast_item_df
    /*${WHERE}*/
)
select stat_time        as `year_month`,
       seq_type,
       region_name,
       gins_family,
       0                as rate,
       0                as total_real_core_num,
       sum(predict_core_num) as total_predict_core_num
from filter_detail
-- 注意这里不可以加where 了， 下面的region_name 会作用到 where
group by stat_time, seq_type, ${REGION_NAME} as region_name, ${GINS_FAMILY} as gins_family