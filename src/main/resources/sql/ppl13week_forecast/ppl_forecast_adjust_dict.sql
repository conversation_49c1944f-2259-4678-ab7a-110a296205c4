SELECT
    arrayStringConcat(groupUniqArray(concat(customhouse_title,'@@@',dwd_crp_longtail_forecast_item_df.region_name)), '|||') AS customhouse_title_to_region_name,
    arrayStringConcat(arrayDistinct(groupArray(region_name)), '|||') AS region_name,
    arrayStringConcat(arrayDistinct(groupArray(gins_family)), '|||') AS gins_family,
    '' AS zone_name,
    arrayStringConcat(arrayDistinct(groupArray(seq_type)), '|||') AS type
FROM dwd_crp_longtail_forecast_item_df
WHERE task_id IN (?)
GROUP BY task_id