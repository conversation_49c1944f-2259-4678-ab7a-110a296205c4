select
    toYear(toDate(use_time)) as year,
    toMonth(toDate(use_time)) as month,
    splitByChar('.', instance_model)[1] as gins_family,
    area_name as region,
    area_name as region_name1,
    any(customhouse_title) as customhouse_title,

    toDate(
        concat(toString(toYear(toDate(use_time))), '-', case when toMonth(toDate(use_time))<10 then '0' else '' end, toString(toMonth(toDate(use_time))), '-01')
        ) as first_day,

    subtractDays(addMonths(toDate(
        concat(toString(toYear(toDate(use_time))), '-', case when toMonth(toDate(use_time))<10 then '0' else '' end, toString(toMonth(toDate(use_time))), '-01')
        ), 1), 1) as last_day,

    sum(applied_core_amount) as new_diff, -- 新增核心数
    0 as ret_diff -- 退回核心数

from dwd_yunti_cvm_demand_forecast_item_df
where stat_date = :predictDate1 -- 使用切片的日期
  and toDate(use_time) < :predictDate -- 时间范围

    ${CONDITION}

group by toYear(toDate(use_time)),toMonth(toDate(use_time)),area_name,gins_family
having new_diff>0

-- 后续如果有退回再扩展在这里，union后再包一层