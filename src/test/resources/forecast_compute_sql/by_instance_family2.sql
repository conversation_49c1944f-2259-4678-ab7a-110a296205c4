-- 中长尾 - 新增需求 - 机型维度
select (case when `期初时间`='2023-01-26' then '2023-01-31' else `期初时间` end) as date,
    ginsfamily,
    round(sum(abs(`diff期初计费用量`)), 2) as value
   -- sum(abs(`diff期初计费用量`)) as value
from yongliang_2022_month_with_diff
where uin GLOBAL not in (select uin
    from (select uin, sum(`期初计费用量`) as num
    from yongliang_2022_month
    where cpu_or_gpu = 'CPU'
  and biztype = 'cvm'
  and approle not in ('EKS', 'EMR', 'CDH', 'LH')
  and `期初时间` = '2023-01-26'
    group by uin) t
    where num > 1000)
  and cpu_or_gpu = 'CPU'
  and biztype = 'cvm'
  and approle not in ('EKS', 'EMR', 'CDH', 'LH')
  and `海关关境`='境内'
  and `diff期初计费用量`>0 -- 只要新增的
group by date,ginsfamily
order by date,ginsfamily