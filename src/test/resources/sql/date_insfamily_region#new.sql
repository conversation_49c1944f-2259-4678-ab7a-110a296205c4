select `期初时间`    as date,
--        count(distinct uin) uins,
--     round(sum (if(`diff期初计费用量` < 0, `diff期初计费用量`, 0)) as `退回数`, 2) as nret,
--     round(sum (if(`diff期初计费用量` > 0, `diff期初计费用量`, 0)) as `新增数`, 2) as nnew,
                round(
                abs(
                sum(if(`期末计费用量` - `期初计费用量` < 0, `期末计费用量` - `期初计费用量`, 0))) as `退回数`,
                      2)      as value,
--        round(sum(if(`期末计费用量` - `期初计费用量` > 0, `期末计费用量` - `期初计费用量`, 0)) as `新增数`,
--              2)      as value,
       ginsfamily,
       b.region_name as r_name
--     from cloud_demand.yongliang_2022_month_with_diff
from cloud_demand.yongliang_2022_week a
    left join cloud_demand.static_zone b on a.`可用区` = b.zone_name
where cpu_or_gpu = 'CPU'
  and biztype = 'cvm'
  and approle GLOBAL in ('正常售卖', 'CDH', '预扣包')
  and ginstype not like 'RS%'
  and customhouse_title = '境内'
--     and  uin not in ($0) and uin not in ($1)
  and a.uin GLOBAL not in (select distinct uin
    from yongliang_2022_month_with_diff
    where `用户简称` in ('拼多多', '小红书', '美团点评', '快手'))
group by date, ginsfamily, b.region_name