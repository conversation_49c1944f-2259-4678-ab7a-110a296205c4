select date, ginsfamily, round(sum (abs(nnew)), 2) as value
from (
    select `期初时间` as date,
    count (distinct uin) uins,
--     round(sum (if(`diff期初计费用量` < 0, `diff期初计费用量`, 0)) as `退回数`, 2) as nret,
--     round(sum (if(`diff期初计费用量` > 0, `diff期初计费用量`, 0)) as `新增数`, 2) as nnew,
    round(sum (if(`期末计费用量` - `期初计费用量` < 0, `期末计费用量` - `期初计费用量`, 0)) as `退回数`, 2) as nret,
    round(sum (if(`期末计费用量` - `期初计费用量` > 0, `期末计费用量` - `期初计费用量`, 0)) as `新增数`, 2) as nnew,
    ginsfamily,
    `海关关境`,
    `可用区`
--     from cloud_demand.yongliang_2022_month_with_diff
    from cloud_demand.yongliang_2022_week
    where cpu_or_gpu = 'CPU'
    and biztype = 'cvm'
    and `期初时间` >= '2021-03-01'
    and approle not in ('EKS', 'EMR', 'CDH', 'LH')
    and  uin  in ($0) and uin not in ($1)
    and `期初时间` != '2023-01-26'
    group by date, ginsfamily, `海关关境`, `可用区`)

where `海关关境` = '境内'
group by date, `ginsfamily`
order by date