package cloud.demand.app.modules.flow;

import cloud.demand.app.modules.flow.constant.FlowNodeDefaultReturnValueEnum;
import cloud.demand.app.modules.flow.dto.FlowNodeRecordWithFlowInfoDTO;
import cloud.demand.app.modules.flow.req.ApprovalReq;
import cloud.demand.app.modules.flow.req.FlowNodeValueSetReq;
import cloud.demand.app.modules.flow.req.FlowPushReq;
import cloud.demand.app.modules.flow.req.FlowStartReq;
import cloud.demand.app.modules.flow.service.FlowService;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class FlowTest {

    @Autowired
    private FlowService flowService;

    private final String bizId_1 = "test-order-id-001";

    private final String user_dot = "dotyou";

    @Test
    public void starNewFlow() {
        FlowStartReq req = new FlowStartReq();
        req.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        req.setFlowName(OrderFlowEnum.ORDER_MAIN.getName());
        req.setFlowRemark("流程模块测试，创建订单主流程");
        req.setBizId(bizId_1);
        req.setFlowInitiator(user_dot);
        FlowNodeRecordWithFlowInfoDTO res = flowService.starNewFlow(req);
        log.error(JSON.toJson(res));
    }

    @Test
    public void setCurrentFlowNodeReturnValue() {
        FlowNodeValueSetReq req = new FlowNodeValueSetReq();
        req.setBizId(bizId_1);
        req.setOperateUser(user_dot);
        req.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        req.setNodeCode("node_order_propose");
        req.setNodeReturn(FlowNodeDefaultReturnValueEnum.PASS.getCode());
        req.setOperateRemark("测试流程");
        FlowNodeRecordWithFlowInfoDTO res = flowService.setCurrentFlowNodeReturnValue(req);
        log.error(JSON.toJson(res));
    }

    @Test
    public void pushToNextNode() {
        FlowPushReq req = new FlowPushReq();
        req.setBizId(bizId_1);
        req.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        FlowNodeRecordWithFlowInfoDTO res = flowService.pushToNextNode(req);
        log.error(JSON.toJson(res));
    }

    @Test
    public void approvalRefuse() {
        ApprovalReq req = new ApprovalReq();
        req.setBizId(bizId_1);
        req.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        req.setApprovalRemark("不同意此订单，请修改订单");
        req.setApprovalUser(user_dot);
        req.setApprovalResult(false);
        req.setNodeCode("node_order_approval");
        FlowNodeRecordWithFlowInfoDTO res = flowService.approval(req);
        log.error(JSON.toJson(res));
    }

    @Test
    public void approvalPassAndPushFlow() {
        ApprovalReq req = new ApprovalReq();
        req.setBizId(bizId_1);
        req.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        req.setApprovalRemark("同意");
        req.setApprovalUser(user_dot);
        req.setApprovalResult(true);
        req.setNodeCode("node_order_close");
        FlowNodeRecordWithFlowInfoDTO res = flowService.approval(req);
        log.error(JSON.toJson(res));
        pushToNextNode();
    }

}
