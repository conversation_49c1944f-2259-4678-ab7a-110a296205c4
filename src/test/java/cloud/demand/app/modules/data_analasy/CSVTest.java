package cloud.demand.app.modules.data_analasy;


import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import de.siegmar.fastcsv.reader.CsvReader;
import de.siegmar.fastcsv.reader.CsvRow;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.InputStream;
import java.io.Reader;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.apache.commons.lang3.time.StopWatch;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class CSVTest {


    @Resource
    private DBHelper demandDBHelper;



    @SneakyThrows
    @Test
    public void testCSV(){

        URL resource = Thread.currentThread().getContextClassLoader()
                .getResource("test.csv");
        File myObj = new File(resource.getFile());
        Reader reader = new FileReader(myObj);

        StopWatch stopwatch = StopWatch.createStarted();
        CsvReader build = CsvReader.builder().build(reader);
        build.stream().forEach((o)->{
            String field = o.getField(1);
            if (field.equals("1")) {
                System.out.println(1);
            }
        });
        System.out.println("deal time: " + stopwatch.getTime());

        stopwatch.reset();
        System.out.println("count" + CsvReader.builder().build(reader).stream().count());
        System.out.println("deal time: " + stopwatch.getTime());


//        try (CsvReader csvReader = CsvReader.builder().build(path)) {
//            csvReader.forEach(System.out::println);
//        }

    }
}
