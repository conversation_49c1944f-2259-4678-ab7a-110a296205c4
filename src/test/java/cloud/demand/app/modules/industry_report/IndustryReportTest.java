package cloud.demand.app.modules.industry_report;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_report.entity.CloudDemandInnerBizUinConfigDO;
import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoDO;
import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoLatestDO;
import cloud.demand.app.modules.industry_report.service.IndustryReportDictService;
import cloud.demand.app.modules.industry_report.service.IndustryReportQueryService;
import cloud.demand.app.modules.industry_report.service.impl.IndustryReportGenDataServiceImpl;
import cloud.demand.app.modules.industry_report.service.util.MonthUtil;
import cloud.demand.app.modules.mrpv2.utils.MyTimeUtils;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil;
import cloud.demand.app.modules.tencent_cloud_utils.CommonUtil;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;
import yunti.boot.client.JsonrpcClient;

@SpringBootTest
@Slf4j
public class IndustryReportTest {

    @Resource
    IndustryReportGenDataServiceImpl genDataService;
    @Resource
    private IndustryReportDictService industryReportDictService;
    @Resource
    private IndustryReportQueryService queryService;
    @Resource
    private DBHelper planDBHelper;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper yuntiDBHelper;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private DictService dictService;

    @Test
    public void testFlatMap() {
        List<String> c5 = dictService.getCsigDeviceTypeByInstanceType(Lang.list("C5", "C3"));
        System.out.println(c5);

    }


    @Test
    public void testGenData() {
//        genDataService.genBillingScaleData("2023-02");
//        genDataService.genBillingScaleData2("2023-02");
//        genDataService.syncIndustryInfo();
        //  获取内部业务uin集合
        List<CloudDemandInnerBizUinConfigDO> list = yuntiDBHelper.getAll(CloudDemandInnerBizUinConfigDO.class);
        List<String> allInnerBizUin = list.stream().
                flatMap(o -> Arrays.stream(o.getUin().trim().split(";"))).collect(Collectors.toList());
        System.out.println(allInnerBizUin.size());
        System.out.println(allInnerBizUin);
    }

    @Test
    public void testQueryDetail() {
//        queryService.queryDeviceApply().stream().forEach(System.out::println);
    }

    @Autowired
    JsonrpcClient jsonrpcClient;

    @Test
    public void testJsonRpcInvoke() {

        String url = "http://localhost/cloud-demand-app/industry-report/queryIndustryReportOverview?api_key=no";
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        ImmutableMap<String, Object> params = ImmutableMap.of("params",
                ImmutableMap.of("beginYearMonth", DateUtils.parse("2023-01-01"),
                        "endYearMonth", DateUtils.parse("2023-03-01")));
        HttpEntity<ImmutableMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);
        try {
            RestTemplate restTemplate = new RestTemplate();
            String res = restTemplate.postForObject(url, requestEntity, String.class);
            System.out.println(res);
        } catch (Exception e) {
            throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", e.toString(), url);
        }

    }

    @Test
    public void test() {
        String sql = "select distinct appid from monthly_azu_ginsfamily where stattime = '2023-02-01' limit 10000";
        List<Long> all = planDBHelper.getRaw(Long.class, sql);
        WhereSQL whereSQL = new WhereSQL();
        String curMonthFirstDay = DateUtils.format(new Date(), "yyyy-MM") + "-01";
        String lastMonthFirstDay = DateUtils.formatDate(
                DateUtils.addTime(DateUtils.parse(curMonthFirstDay), Calendar.MONTH, -1));

        whereSQL.and("stat_time >= ?", lastMonthFirstDay);
        whereSQL.and("appid in (?)", all);

        long startTime = System.currentTimeMillis();
        List<IndustryReportAppidInfoDO> rst
                = demandDBHelper.getAll(IndustryReportAppidInfoDO.class, whereSQL.getSQL(), whereSQL.getParams());
        long endTime = System.currentTimeMillis();
        System.out.println("耗时：" + (endTime - startTime) + "ms");

    }

    @Test
    public void queryUinAccountInfoTest() {
        AccountUtil.AccountInfoDTO accountInfoDTO =
                AccountUtil.queryUinAccountInfo("************", CommonUtil.defaultSecret(),
                        AccountUtil.FULL_ACCOUNT_INFO_KEY_LIST);
        log.info("{}", JSON.toJson(accountInfoDTO));
        assert accountInfoDTO.getDecodeDataMap() != null;
        System.out.println(accountInfoDTO.getField("organization_info.income_organization_name", String.class));
        System.out.println(accountInfoDTO.getField("customer_info.sname", String.class));
        System.out.println(accountInfoDTO.getField("customer_info.name", String.class));
        System.out.println(accountInfoDTO.getField("register_industry.industry_name", String.class));
        //  判断是否内部账号
        String innerInfo = accountInfoDTO.getField("inner_info", String.class);
        if (StringTools.isBlank(innerInfo)) {
            System.out.println("外部");
        } else {
            String innerInfoUin = accountInfoDTO.getField("inner_info.uin", String.class);
            if (Objects.equals(innerInfoUin, "************")) {
                System.out.println("内部");
            }
            System.out.println("外部");
        }
        System.out.println();
    }

    @Test
    public void testChannelMark() {
        demandDBHelper.getAll(IndustryReportAppidInfoLatestDO.class, "where cid = '0ff85f36ee2e6ebf6faef2bdb918ed85'");
    }

    public static void main(String[] args) {

        ArrayList<String> returnType = Lang.list("常规项目", "短租退回", "退役退回");
        WhereSQL returnCondition = new WhereSQL();
        if (returnType.contains("2023春保")) {
            returnCondition.or("project = '2023春节保障'");
            returnCondition.or("return_tag = '项目退回-FY23春保退回'");
        }
        if (returnType.contains("短租退回")) {
            returnCondition.or("project = '短租项目'");
            returnCondition.or("return_tag = '短租设备退回'");
        }
        if (returnType.contains("裁撤退回")) {
            returnCondition.or("return_tag = '机房裁撤设备退回'");
        }
        if (returnType.contains("退役退回")) {
            returnCondition.or("return_tag = '满退役条件设备退回'");
        }
        //  上面的全集再取反 = 常规项目
        if (returnType.contains("常规项目")) {
//            WhereSQL condition = new WhereSQL();
//            condition.or("project = '2023春节保障'");
//            condition.or("return_tag = '项目退回-FY23春保退回'");
//            condition.or("project = '短租项目'");
//            condition.or("return_tag = '短租设备退回'");
//            condition.or("return_tag = '机房裁撤设备退回'");
//            condition.or("return_tag = '满退役条件设备退回'");
            returnCondition.and("project <> '2023春节保障' and return_tag <> '项目退回-FY23春保退回'");
            returnCondition.and("project <> '短租项目' and return_tag <> '短租设备退回'");
            returnCondition.and("return_tag <> '机房裁撤设备退回'");
            returnCondition.and("return_tag <> '满退役条件设备退回'");
//            returnCondition.or(condition.not());
        }
        System.out.println(returnCondition.getSQL());

    }

    @Test
    public void testCategory() {
        Set<String> set = industryReportDictService.getDeptByCategory(Lang.list("内部业务"));
        System.out.println(set);
        System.out.println(set.size());

    }

    @Test
    public void testMonthToQ() {
        String[] times =
                new String[]{"2023-02", "2024-04", "2023-03", "2024-03", "2023-04", "2024-06", "2023-05", "2024-05",
                        "2023-06", "2023-07", "2023-08", "2023-09", "2023-10", "2023-11", "2024-02", "2023-01",
                        "2023-12", "2024-01"};
        for (String time : times) {
            System.out.println(time + ": " + MonthUtil.yearMonthToQ(time));
        }

        List<String> qs = MyTimeUtils.listQ("2023-Q1", "2024-Q2");
        System.out.println(qs);
    }

    @Test
    public void testJsonDate() {
        System.out.println(JSON.toJson(new Date()));
    }
}
