package cloud.demand.app.modules.sop;

import cloud.demand.app.modules.sop.domain.YuntiPageReq;
import cloud.demand.app.modules.sop.domain.YuntiVersionReq;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandHedgingDetailResList;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandVersionCodeRes;
import cloud.demand.app.modules.sop.http.service.YuntiApiHttpService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@Slf4j
public class YuntiApiTest {
    @Resource
    private YuntiApiHttpService apiHttpService;

    @Test
    public void test(){
        YuntiPageReq req = new YuntiPageReq();
        req.setStart(0);
        req.setSize(1);
        YuntiCvmDemandVersionCodeRes versionList = apiHttpService.getVersionList(req);
        YuntiCvmDemandVersionCodeRes.Item item = versionList.getData().get(0);
        YuntiVersionReq req1 = new YuntiVersionReq();
        req1.setVersionId(item.getVersionCode());
        List<YuntiCvmDemandHedgingDetailResList> hedgingDetail = apiHttpService.getHedgingDetail(req1);
        System.out.println(hedgingDetail.size());
        System.out.println(hedgingDetail.get(0));
    }
}
