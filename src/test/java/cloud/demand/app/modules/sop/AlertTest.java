package cloud.demand.app.modules.sop;


import cloud.demand.app.modules.sop.alert.domain.SopCheckReq;
import cloud.demand.app.modules.sop.alert.service.SopAlterService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@Slf4j
public class AlertTest {
    @Resource
    private SopAlterService alterService;

    @Test
    public void test(){
        String version = "1697490020";
        SopCheckReq req = new SopCheckReq();
        req.setVersion(version);
        alterService.checkSopCvmDemand(req);
        alterService.checkSopCvmReturn(req);
    }
}
