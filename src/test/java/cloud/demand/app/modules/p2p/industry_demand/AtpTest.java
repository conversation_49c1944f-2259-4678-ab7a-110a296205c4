package cloud.demand.app.modules.p2p.industry_demand;

import cloud.demand.app.modules.p2p.industry_demand.dto.atp.IndustryQueryReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.atp.IndustryQueryResp;
import cloud.demand.app.modules.p2p.industry_demand.entity.AtpIndustrySyncVersionDO;
import cloud.demand.app.modules.p2p.industry_demand.service.AtpIndustryDataService;
import com.pugwoo.wooutils.json.JSON;
import java.time.LocalDate;
import java.time.YearMonth;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class AtpTest {

    @Resource
    private AtpIndustryDataService atpIndustryDataService;

    @Test
    public void syncSurplusInventory() {
        atpIndustryDataService.syncSurplusInventory();
    }

    @Test
    public void queryForIndustry() {
        IndustryQueryReq req = new IndustryQueryReq();
        req.setRegion("na-siliconvalley");
        req.setInstanceType("SA4");
        req.setYearMonth(YearMonth.parse("2025-06"));
        req.setDemandCore(7000);
        IndustryQueryResp res = atpIndustryDataService.queryForIndustry(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void getCurrentSyncVersion() {
        AtpIndustrySyncVersionDO rs = atpIndustryDataService.getCurrentSyncVersion();
        System.out.println(JSON.toJson(rs));
    }

    @Test
    public void queryIndustryAtpConsultant() {
        Object rs = atpIndustryDataService.queryIndustryAtpConsultant("智慧行业一部");
        System.out.println(JSON.toJson(rs));
    }

}
