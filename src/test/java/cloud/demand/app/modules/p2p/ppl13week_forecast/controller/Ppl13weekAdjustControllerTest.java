package cloud.demand.app.modules.p2p.ppl13week_forecast.controller;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekTransformReq;
import com.pugwoo.wooutils.collect.ListUtils;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class Ppl13weekAdjustControllerTest {

    @Resource
    Ppl13weekAdjustController ppl13weekAdjustController;


    @Test
    public void createTransForm() {

        QueryPpl13weekTransformReq req = new QueryPpl13weekTransformReq();
        req.setOutputVersionIds(ListUtils.of(2201L));
        req.setVersionCode("V_20250521");
        req.setStartYearMonth("2025-07");
        req.setEndYearMonth("2025-08");
        req.setIsAppend(false);
        req.setDesc("CBS 下发");
        ppl13weekAdjustController.createTransForm(req);

    }
}