package cloud.demand.app.modules.p2p.ppl13week_forecast;

import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class TestPpl13WeekPredictService {

    public static void main(String[] args) {

        //测试各种边界条件
        String instanceModel1 = "";
        String instanceModel2 = null;
        String instanceModel3 = "-";
        String instanceModel4 = "D2.2XLARGE32";
        String instanceModel5 = "D2.2XLARGE32-p2w";
        check(instanceModel1);
        check(instanceModel2);
        check(instanceModel3);
        check(instanceModel4);
        check(instanceModel5);
    }

    private static void check(String instanceModel) {
        if (instanceModel != null && instanceModel.contains("-")) {
            instanceModel = instanceModel.substring(0, instanceModel.indexOf("-"));
        }
        System.out.println(instanceModel);
    }


}
