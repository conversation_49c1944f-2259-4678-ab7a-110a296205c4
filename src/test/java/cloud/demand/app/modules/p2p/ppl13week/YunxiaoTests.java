package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.p2p.industry_demand.dto.dict.RegionDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.dict.ZoneDTO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.ppl13week.dto.apply2.ApplyPplItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreatePreDeductOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateStockSupplyPlanReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateStockSupplyPlanRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.DestroyPreDeductOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.EditOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GetPreDeductOrderDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PreDeductData;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PreDeductRenewalReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryInstanceTypeReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryOrderListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryPreDeductOrderListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryStockSupplyResultRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QuotaQueryReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QuotaQueryResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.RenewalPreDeductResp;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoPayModeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoReasonTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplApplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplApplyServiceImpl;
import cloud.demand.app.modules.tencent_cloud_utils.YunXiaoUtil;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.BaseDTO;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.PageDataDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class YunxiaoTests {

    @Autowired
    private YunxiaoAPIService yunxiaoAPIService;
    @Resource
    private PplApplyService pplApplyService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private IndustryDemandDictService industryDemandDictService;

    @Test
    public void testSync() {
        pplApplyService.reverseSyncYunxiaoOrder(false);
    }

    @Test
    public void testSyncForce() {
        pplApplyService.reverseSyncYunxiaoOrder(true);
    }

    @Test
    public void test() {
        long start = System.currentTimeMillis();
        List<String> nickxie = pplDictService.queryIndustryDept("yonayou");
        long end = System.currentTimeMillis();
        System.out.println(nickxie);
        System.out.println("cost:" + (end - start) + "ms");
    }

    @Test
    public void testGetList() {
        QueryOrderListReq req = new QueryOrderListReq();
        req.setPageNumber(1);
        req.setPageSize(100);
        req.setStatus(ListUtils.newList(YunxiaoOrderStatusEnum.CREATED.getCode(),
                YunxiaoOrderStatusEnum.CANCELED.getCode()));
        PageDataDTO<OrderDTO> page = yunxiaoAPIService.queryOrderList(req);
        System.out.println(JSON.toJsonFormatted(page));
    }

    @Test
    public void testQueryOrderDetail() {
        String orderId = "order-6582478611";
        OrderDetailDTO orderDetailDTO = yunxiaoAPIService.queryOrderDetail(orderId);
        System.out.println(JSON.toJsonFormatted(orderDetailDTO));
    }

    @Test
    public void testEditAndSubmit() {
        // 先创建一条预约单
        String orderId = "order-6363709ba1";
        // 然后反查回来
        OrderDetailDTO orderDetailDTO = yunxiaoAPIService.queryOrderDetail(orderId);
        EditOrderReq req = orderDetailDTO.toEditOrderReq();

        // 然后修改
        req.setExpectTime("2022-11-20");

        // 最后提交
        BaseDTO baseDTO = yunxiaoAPIService.editAndSubmit(req);
        System.out.println(JSON.toJsonFormatted(baseDTO));
    }

    @Test
    public void testCreateAndSubmit() {
        CreateOrderReq req = new CreateOrderReq();
        req.setCreator("nickxie");
        req.setArchitect("psionli");
        req.setUin("493883885");
        req.setRegion("ap-guangzhou");
        req.setReason(YunxiaoReasonTypeEnum.UPGRADE.getCode());
        req.setReason("云运管PPL联调需要，创建了这条预约单，仅测试用");
        req.setExpectTime("2022-11-21");
        req.setLatestExpectTime("2022-11-29");
        req.setOrderType(YunxiaoOrderTypeEnum.Normal.getCode());

        List<CreateOrderReq.OrderDetails> orderDetails = new ArrayList<>();
        req.setOrderDetails(orderDetails);

        CreateOrderReq.OrderDetails d1 = new CreateOrderReq.OrderDetails();
        orderDetails.add(d1);
        d1.setZone("ap-guangzhou-3");
        d1.setInstanceType("S5.8XLARGE64");
        d1.setSysDiskType(YunxiaoDiskTypeEnum.CLOUD_PREMIUM.getCode());
        d1.setSysDiskSize(128);
        d1.setDataDiskType(YunxiaoDiskTypeEnum.CLOUD_PREMIUM.getCode());
        d1.setDataDiskSize(1024);
        d1.setDataDiskCount(5);
        d1.setApplyCount(1000);
        d1.setPayMode(YunxiaoPayModeEnum.PREPAID.getCode());

        CreateOrderReq.OrderDetails d2 = new CreateOrderReq.OrderDetails();
        orderDetails.add(d2);
        d2.setZone("ap-guangzhou-4");
        d2.setInstanceType("S5.8XLARGE128");
        d2.setSysDiskType(YunxiaoDiskTypeEnum.CLOUD_PREMIUM.getCode());
        d2.setSysDiskSize(256);
        d2.setDataDiskType(YunxiaoDiskTypeEnum.CLOUD_PREMIUM.getCode());
        d2.setDataDiskSize(512);
        d2.setDataDiskCount(8);
        d2.setApplyCount(2000);
        d2.setPayMode(YunxiaoPayModeEnum.PREPAID.getCode());

        yunxiaoAPIService.createAndSubmit(req);
    }

    @Test
    public void testCreateStockSupplyPlan() {
        CreateStockSupplyPlanReq req = new CreateStockSupplyPlanReq();
        req.setCreator("nickxie");
        req.setDescription("这个一个测试的对冲");

        List<CreateStockSupplyPlanReq.Item> items = new ArrayList<>();
        req.setItems(items);

        CreateStockSupplyPlanReq.Item item1 = new CreateStockSupplyPlanReq.Item();
        item1.setUin("493883885");
        item1.setRegion("");
        item1.setZone("ap-shanghai-4");
        item1.setInstanceType("S5.2XLARGE16");
        item1.setOptionalInstanceTypes("SA2.2XLARGE16;S6.2XLARGE16");
        item1.setCount(100); // 100台
        item1.setDemandDate("2022-12-24");
        item1.setDemandType("NEW");
        item1.setPriority(1);
        item1.setLabel("PN202206033344-1");

        items.add(item1);

        CreateStockSupplyPlanReq.Item item2 = new CreateStockSupplyPlanReq.Item();
        item2.setUin("493883885");
        item2.setRegion("ap-guangzhou");
        item2.setZone("ap-guangzhou-4");
        item2.setInstanceType("S5.2XLARGE16");
        item2.setOptionalInstanceTypes("SA2.2XLARGE16;S6.2XLARGE16");
        item2.setCount(200); // 100台
        item2.setDemandDate("2022-12-25");
        item2.setDemandType("NEW");
        item2.setPriority(2);
        item2.setLabel("PN202206033344-2");

        items.add(item2);

        CreateStockSupplyPlanRsp resp = yunxiaoAPIService.createStockSupplyPlan(req);

        QueryStockSupplyResultRsp retData = yunxiaoAPIService.submitStockSupplyResult(
                resp.getData().getPlanId());
        System.out.println(retData);
        System.out.println(JSON.toJsonFormatted(resp));
    }

    @Test
    public void testCreatePlan() throws Exception {
        String req = IOUtils.readAllAndClose(new FileInputStream("d:/req.txt"), "utf-8");
        String resp = YunXiaoUtil.postRaw("/beacon/resource-match-plan/create", JSON.parse(req));
        System.out.println(resp);
    }

    // plan-63b3ebcfd1

    @Test
    public void testSubmit() {
        Map<String, String> req = new HashMap<>();
        req.put("planId", "plan-63b3ebcfd1");

        String resp = YunXiaoUtil.postRaw("/beacon/resource-match-plan/submit", req);
        System.out.println(resp);
    }

    @Test
    public void testQueryMain() {
        String json = YunXiaoUtil.postRaw("/beacon/galaxy/main-zone", new HashMap<>());
        Map<String, Object> map = JSON.parseToMap(json);
        Set<String> result = Lang.set();
        if (map != null) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("data");
            if (ListUtils.isNotEmpty(list)) {
                for (Map<String, Object> entry : list) {
                    String instanceFamily = ((String) entry.get("instanceFamily"));
                    if (StringTools.isNotBlank(instanceFamily)) {
                        result.add(instanceFamily);
                    }
                }
            }
        }
        System.out.println(result);
    }

    @Test
    public void testQueryPlan() throws Exception {
        QueryStockSupplyResultRsp resp = yunxiaoAPIService.queryStockSupplyResult("plan-63b3ebcfd1");

        FileOutputStream out = new FileOutputStream("d:/resp.csv");
        out.write("label,matchType,matchCount,matchInstanceType,region,zone,hostType,hostCount,remark\n".getBytes());
        for (QueryStockSupplyResultRsp.Result result : resp.getData().getResult()) {
            out.write((result.getLabel() + "," + result.getMatchType() + "," + result.getMatchCount()
                    + "," + result.getMatchInstanceType()
                    + "," + result.getRegion()
                    + "," + result.getZone()
                    + "," + result.getHostType()
                    + "," + result.getHostCount()
                    + "," + result.getRemark()
                    + "\n").getBytes());
        }
        out.close();

        System.out.println(JSON.toJsonFormatted(resp));
    }

    @Test
    public void checkQueryPlan() throws Exception {
        QueryStockSupplyResultRsp resp = yunxiaoAPIService.queryStockSupplyResult("plan-63b3ebcfd1");

        Map<String, QueryStockSupplyResultRsp.Item> itemsMap = ListUtils.toMap(
                resp.getData().getItems(), o -> o.getLabel(), o -> o);
        Map<String, List<QueryStockSupplyResultRsp.Result>> resultMap = ListUtils.toMapList(
                resp.getData().getResult(), o -> o.getLabel(), o -> o);

        for (Map.Entry<String, QueryStockSupplyResultRsp.Item> item : itemsMap.entrySet()) {
            List<QueryStockSupplyResultRsp.Result> results = resultMap.get(item.getKey());
            if (ListUtils.isEmpty(results)) {
                System.err.println("==label:" + item.getKey() + "不存在匹配结果");
            }
            BigDecimal resultCount = NumberUtils.sum(results, o -> o.getMatchCount());
            if (item.getValue().getCount() != resultCount.intValue()) {
                System.err.println("==label:" + item.getKey() + "数量不匹配，需求数:" + item.getValue().getCount()
                        + ",返回结果总数:" + resultCount.intValue());
            }
        }

        //  System.out.println(resp);

    }


    @Test
    public void completePlan() {
//        String resp = yunxiaoAPIService.completeStockSupply("plan-63992e5ef1");
//        System.out.println(JSON.toJsonFormatted(resp));
    }

    @Test
    public void testAppRole() {
        String cvm = yunxiaoAPIService.getYunxiaoAppRoleMapping("CVM");
        System.out.println(cvm);
        cvm = yunxiaoAPIService.getYunxiaoAppRoleMapping("CVM&CBS");
        System.out.println(cvm);

        cvm = yunxiaoAPIService.getYunxiaoAppRoleMapping("Elasticsearch Service");
        System.out.println(cvm);

        cvm = yunxiaoAPIService.getYunxiaoAppRoleMapping("ContainerService");
        System.out.println(cvm);
    }

    @Test
    public void orderExportToJson() {
        YearMonth yearMonth = YearMonth.of(2023, 10);
        Object res = yunxiaoAPIService.orderExportToJson(yearMonth);
        System.err.println(JSON.toJson(res));
    }

    @Test
    public void queryPrincipalInstanceFamily() throws JsonProcessingException {
        Object res = yunxiaoAPIService.queryPrincipalInstanceFamily();
        System.err.println(JSON.toJson(res));
    }

    /**
     * 预约单同步任务与页面上预约单提交的并发测试
     *
     * @throws InterruptedException
     */
    @Test
    public void currentTest() throws InterruptedException {
        List<RegionDTO> ret = industryDemandDictService.listAllRegionNoStatus();
        Map<String, String> regionCodeToName = ListUtils.toMap(ret,
                o -> o.getRegionStrId(), o -> o.getRegionShortChName());

        List<ZoneDTO> zoneDTOS = industryDemandDictService.listAllZoneNoStatus();
        Map<String, String> zoneCodeToName = ListUtils.toMap(zoneDTOS,
                o -> o.getZoneStrId(), o -> o.getZoneName());

        List<String> failOrders = new ArrayList<>();
        List<String> emptyOrders = new ArrayList<>();
        List<Exception> exceptions = new ArrayList<>();

        Map<String, OrderDetailDTO> applyOrderMap = new HashMap<>();

        String orderId = "order-657bfd744c";
        OrderDetailDTO.Data detailDTO = yunxiaoAPIService.queryOrderDetail(orderId).getData();
        OrderDTO orderDTO = JSON.parse(JSON.toJson(detailDTO), OrderDTO.class);

        String params = "{\n"
                + "        \"appRole\": \"CVM\",\n"
                + "        \"orderCategory\": \"CVM\",\n"
                + "        \"yunxiaoOrderId\": \"order-657bfd744c\",\n"
                + "        \"applyItem\": [\n"
                + "            {\n"
                + "                \"pplId\": \"\",\n"
                + "                \"yunxiaoDetailId\": 7091\n"
                + "            }\n"
                + "        ],\n"
                + "        \"matchedPpl\": [],\n"
                + "        \"industryDept\": \"智慧行业一部\"\n"
                + "    }";
        ApplyPplItemReq req = JSON.parse(params, ApplyPplItemReq.class);

        ExecutorService executor = Executors.newFixedThreadPool(5);
        executor.execute(() -> {
            ((PplApplyServiceImpl) pplApplyService).syncOneYunxiaoOrderInLock(orderDTO, true, applyOrderMap,
                    emptyOrders, regionCodeToName, zoneCodeToName, failOrders, exceptions);
        });
        executor.execute(() -> pplApplyService.applyPplId(req));
        Thread.sleep(1000 * 60);
    }


    @Test
    public void queryPreDeductOrderList() {
        QueryPreDeductOrderListReq req = new QueryPreDeductOrderListReq();
        req.setReservationFormId(Arrays.asList(7120, 5108));
        List<PreDeductData> preDeductData = yunxiaoAPIService.queryPreDeductOrderList(req);
        System.err.println(JSON.toJson(preDeductData));
    }


    @Test
    public void createPreDeductOrder() {
//        CreatePreDeductOrderReq req = new CreatePreDeductOrderReq();
//        req.setCreator("damianren");
//        req.setAppId("1301555531");
//        req.setUin("100013386154");
//        req.setZone("ap-shanghai-2");
//        req.setInstanceType("SN3ne.MEDIUM2");
//        req.setInstanceCategory("CVM");
//        req.setApplyCount(1);
//        req.setPayMode(YunxiaoPayModeEnum.POSTPAID_BY_HOUR.getCode());
//        req.setReason("11");
//        req.setStartTime("2024-05-10 02:39:43");
//        req.setDestroyTime("2024-05-20 02:39:43");
//        CreatePreDeductOrderResp preDeductOrder = yunxiaoAPIService.createPreDeductOrder(req);
        yunxiaoAPIService.preDeductOrderReserve(ListUtils.newArrayList(7389));

        System.err.println("");
    }

    @Test
    public void destroyPreDeductOrder() {
        DestroyPreDeductOrderReq req = new DestroyPreDeductOrderReq();
        req.setReservationFormId(Arrays.asList(7362));
        req.setOperator("oliverychen");
        yunxiaoAPIService.destroyPreDeductOrder(req);

    }

    @Test
    public void getPreDeductOrderDetail() {
        GetPreDeductOrderDetailResp preDeductOrderDetail = yunxiaoAPIService.getPreDeductOrderDetail(7358);
        System.err.println(JSON.toJson(preDeductOrderDetail));
    }

    @Test
    public void queryInstanceModelInfos() {
        QueryInstanceTypeReq req = new QueryInstanceTypeReq();
        req.setInstanceType(ListUtils.newArrayList("S1.SAMLL4"));
        Object res = yunxiaoAPIService.queryInstanceModelInfos(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void beforePreDeductForOrderCheck() {
        String data = "{\n"
                + "    \"creator\": \"erickssu\",\n"
                + "    \"appId\": \"251246362\",\n"
                + "    \"uin\": \"700000774606\",\n"
                + "    \"zone\": \"ap-shanghai-5\",\n"
                + "    \"instanceType\": \"SA5.2XLARGE32\",\n"
                + "    \"instanceCategory\": \"CVM\",\n"
                + "    \"applyCount\": 1,\n"
                + "    \"payMode\": \"PREPAID\",\n"
                + "    \"reason\": \"11\",\n"
                + "    \"startTime\": \"2025-05-10 02:39:43\",\n"
                + "    \"destroyTime\": \"2026-05-09 02:39:43\",\n"
                + "    \"keepReserved\": true\n"
                + "}";
        CreatePreDeductOrderReq req = JSON.parse(data, CreatePreDeductOrderReq.class);
        yunxiaoAPIService.beforePreDeductForOrderCheck(req);
    }

    @Test
    public void renewalPreDeduct() {
        LocalDate endPreDeductDate = LocalDate.now().plusDays(2);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        PreDeductRenewalReq req = new PreDeductRenewalReq();
        req.setReservationFormId(ListUtils.newArrayList(7482));
        req.setDestroyTime(LocalDateTime.of(endPreDeductDate, LocalTime.MIN).format(formatter));
        req.setReason("测试预扣续期接口");
//        req.setCreator("dotyou");
        RenewalPreDeductResp res = yunxiaoAPIService.renewalPreDeduct(req);
        System.out.println(JSON.toJson(res));

    }

    @Test
    public void queryQuota() {
        QuotaQueryReq req = new QuotaQueryReq("gz", Arrays.asList("1300243876"));
        QuotaQueryResp quotaQueryResp = yunxiaoAPIService.queryQuota(req);
        System.out.println(JSON.toJson(quotaQueryResp));

    }

    @Test
    public void queryGridList() {

        yunxiaoAPIService.queryGridList(Arrays.asList(8234));
        System.out.println(1);

    }


}
