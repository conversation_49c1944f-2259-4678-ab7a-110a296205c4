package cloud.demand.app.modules.p2p.ppl13week_forecast.service;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.YuntiDemandCvmItemForecastDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13weekTransformServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@SpringBootTest
class Ppl13weekTransformServiceTest {

    @Resource
    Ppl13weekTransformService ppl13weekTransformService;
    @Resource
    Ppl13weekTransformServiceImpl ppl13weekTransformServiceImpl;
    @Test
    void updateWeekData() {
        ppl13weekTransformService.updateWeekData();
    }

    @Test
    void test(){
        YuntiDemandCvmItemForecastDO source = new YuntiDemandCvmItemForecastDO();
        source.setYear(2024);
        source.setMonth(6);
        source.setCoreNum(BigDecimal.valueOf(100));
        List<YuntiDemandCvmItemForecastDO> target = ppl13weekTransformServiceImpl.splitToResPlanMonth( source);
        System.out.println(target);
    }

}