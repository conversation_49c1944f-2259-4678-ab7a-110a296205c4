package cloud.demand.app.modules.p2p.product_demand.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandDictService;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;

@SpringBootTest
class ProductDemandDictServiceImplTest {

    @Resource
    ProductDemandDictService productDemandDictService;

    @Test
    void listAllCampusName() {
        System.out.println(productDemandDictService.listAllCampusData(null));
    }
}