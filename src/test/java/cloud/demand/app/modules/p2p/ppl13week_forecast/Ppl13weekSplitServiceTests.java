package cloud.demand.app.modules.p2p.ppl13week_forecast;

import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekSplitService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class Ppl13weekSplitServiceTests {

    @Resource
    private Ppl13weekSplitService ppl13weekSplitService;

    @Test
    public void test() {
        ppl13weekSplitService.splitCloudMiddle(2648L, 24403L);
    }

}
