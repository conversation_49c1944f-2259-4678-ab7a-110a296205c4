package cloud.demand.app.modules.p2p.longterm.service.impl;

import org.junit.jupiter.api.Test;

class LongtermVersionImportServiceImplTest {

    @Test
    void downloadExcel() {
//        List<List<String>> excelHead =
//                DownloadExcelHeaderBuilder.getExcelHead(2025, Lang.list(1, 2)).getEasyExcelHeaders();
//
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        ExcelWriter excelWriter = EasyExcel.write(out)
//                .head(excelHead).registerWriteHandler(new DownloadExcelStyleHandler()).build();
//        WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
//        excelWriter.write(Lang.list(
//                ExcelImportTestDTO.getTestData(),
//                ExcelImportTestDTO.getTestData(),
//                ExcelImportTestDTO.getTestData(),
//                ExcelImportTestDTO.getTestData()
//        ), writeSheet).finish();
//
//        // 将生成的Excel文件输出到指定路径
//        File outputFile = new File("src/main/resources/longterm_output6"+System.currentTimeMillis()/1000%100000+".xlsx");
//        try (FileOutputStream fileOut = new FileOutputStream(outputFile)) {
//            fileOut.write(out.toByteArray());
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
    }
}