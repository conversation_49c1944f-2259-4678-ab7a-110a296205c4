package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ApprovalProductInfoReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastConstituteReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastConstituteReq.GroupCondition;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendReq.GroupQueryCondition;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendReq.HistoricalTrendGroup;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PermissionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PermissionDTO.PermissionVersionItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PermissionDTO.PermissionVersionRoleItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.BatchDealInnerPplOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.job.InnerProcessRelevantTask;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplOrderAuditRecordService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

@SpringBootTest
public class PplInnerProcessServiceTests {

    @Resource
    private PplInnerProcessService pplInnerProcessService;
    @Resource
    DBHelper demandDBHelper;
    @Resource
    private PplDraftService pplDraftService;

    @Resource
    private PplIndustryProcessService pplIndustryProcessService;
    @Resource
    PplOrderAuditRecordService auditRecordService;
    @Autowired
    private Alert alert;

    @Test
    public void test1() throws ExecutionException, InterruptedException {
        pplInnerProcessService.autoPassForIndustryOne();
    }

    @Test
    public void test() {
        BatchDealInnerPplOrderReq req = new BatchDealInnerPplOrderReq();
        req.setVersionId(298L);
        req.setApproveNote("测试打回通知");
        req.setIndustryDept("智慧行业七部");
        req.setPplOrderList(
                ListUtils.newArrayList("PN2311160004")
        );
        pplInnerProcessService.refusePreSubmitPplOrderDraft(req);
    }

    @Test
    public void DataInit() {
    }

    @Test
    public void updateInner() {
        List<PplOrderDO> raw = demandDBHelper.getRaw(PplOrderDO.class, "SELECT\n"
                + "  DISTINCT a.*\n"
                + "FROM\n"
                + "\tppl_order a\n"
                + "\tJOIN ppl_item b \n"
                + "\ton a.ppl_order = b.ppl_order\n"
                + "WHERE\n"
                + "\ta.YEAR = 2023 \n"
                + "\tAND a.`status` != 'APPLIED'\n"
                + "\tAND a.industry_dept = \"智慧行业一部\"\n"
                + "\tand a.source != \"SYNC_YUNXIAO\"\n"
                + "\tAnd a.deleted = 0\n"
                + "\tAND b.product = \"CVM&CBS\" \n"
                + "\tAND b.instance_num > 0\n"
                + "\tAND b.deleted = 0");
        for (PplOrderDO pplOrderDO : raw) {
            pplOrderDO.setNodeCode("");
            pplOrderDO.setAuditStatus("");
            demandDBHelper.update(pplOrderDO);
        }
    }

    @Test
    public void updateToDraft() {
        List<PplOrderDO> raw = demandDBHelper.getRaw(PplOrderDO.class, "SELECT\n"
                + "  DISTINCT a.*\n"
                + "FROM\n"
                + "\tppl_order a\n"
                + "\tJOIN ppl_item b \n"
                + "\ton a.ppl_order = b.ppl_order\n"
                + "WHERE\n"
                + "\ta.YEAR = 2023 \n"
                + "\tAND a.`month` >= 6\n"
                + "\tAND a.`status` != 'APPLIED'\n"
                + "\tAND a.industry_dept = \"智慧行业一部\"\n"
                + "\tand a.source != \"SYNC_YUNXIAO\"\n"
                + "\tAnd a.deleted = 0\n"
                + "\tAND b.product = \"CVM&CBS\" \n"
                + "\tAND b.instance_num > 0\n"
                + "\tAND b.deleted = 0");
        List<String> pplOrder = raw.stream().map(PplOrderDO::getPplOrder).distinct().collect(Collectors.toList());
        List<PplItemDO> pplItemList = demandDBHelper.getAll(PplItemDO.class,
                "where product = 'CVM&CBS' and instance_num>0 and ppl_order in (?)",
                pplOrder);
        Map<String, List<PplItemDO>> orderToItem = pplItemList.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));
        for (PplOrderDO pplOrderDO : raw) {
            List<PplItemDO> pplItemDOS = orderToItem.get(pplOrderDO.getPplOrder());
            if (CollectionUtils.isEmpty(pplItemDOS)) {
                System.out.println(pplOrderDO.getPplOrder());
                continue;
            }
            SavePplDraftReq req = new SavePplDraftReq();
            req.setType("update");
            req.setPplOrder(pplOrderDO.getPplOrder());
            req.setProduct("CVM&CBS");
            req.setCustomerType(pplOrderDO.getCustomerType());
            req.setCustomerUin(pplOrderDO.getCustomerUin());
            req.setCustomerName(pplOrderDO.getCustomerName());
            req.setCustomerShortName(pplOrderDO.getCustomerShortName());
            req.setCustomerSource(pplOrderDO.getCustomerSource());
            req.setBeginBuyDate(pplItemDOS.get(0).getBeginBuyDate().toString());
            req.setEndBuyDate(pplItemDOS.get(0).getEndBuyDate().toString());
            req.setWarZone(pplOrderDO.getWarZone());
            req.setCenter(pplOrderDO.getCenter());
            req.setIndustryDept(pplOrderDO.getIndustryDept());
            req.setIndustry(pplOrderDO.getIndustry());
            req.setDemandType(pplItemDOS.get(0).getDemandType());
            req.setDemandScene(pplItemDOS.get(0).getDemandScene());
            req.setProjectName(pplItemDOS.get(0).getProjectName());
            req.setSubmitUser(
                    Strings.isNotBlank(pplOrderDO.getSubmitUser()) ? pplOrderDO.getSubmitUser() : "kaijiazhang");

            List<DraftItemDTO> list = new ArrayList<>();
            for (PplItemDO pplItemDO : pplItemDOS) {
                DraftItemDTO draftItemDTO = new DraftItemDTO();
                draftItemDTO.setType("insert");
                BeanUtils.copyProperties(pplItemDO, draftItemDTO);
                draftItemDTO.setBeginBuyDate(pplItemDO.getBeginBuyDate().toString());
                draftItemDTO.setEndBuyDate(pplItemDO.getEndBuyDate().toString());
                draftItemDTO.setBeginElasticDate(
                        pplItemDO.getBeginElasticDate() != null ? pplItemDO.getBeginElasticDate().toString() : null);
                draftItemDTO.setEndElasticDate(
                        pplItemDO.getEndElasticDate() != null ? pplItemDO.getEndElasticDate().toString() : null);
                draftItemDTO.setTotalDiskNum(pplItemDO.getTotalDisk());
                draftItemDTO.setTotalCoreNum(pplItemDO.getTotalCore());
                list.add(draftItemDTO);
            }
            req.setResources(list);
            pplDraftService.saveDraft(req);
        }
    }


    @Test
    public void submit() {
        alert.sendMail("oliverychen", "行业内部PPL流程完结通知",
                "本次PPL审批流程已完结，您可以在CRP系统提交需求，<a href='https://"
                        + "crp.woa.com/13ppl/approval-process/forecast'>点击前往</a>。");

        List<PplOrderAuditRecordVO> allRecord = auditRecordService.queryHistoryAuditRecord(null, 1L);
        Set<String> operateUserList = allRecord.stream().filter(v -> v.getOperateUser() != null)
                .map(PplOrderAuditRecordVO::getOperateUser).collect(Collectors.toSet());
        String join = org.apache.logging.log4j.util.Strings.join(operateUserList, ';');
//

    }

    @Test
    public void queryApprovalProductInfo() {
        ApprovalProductInfoReq params = new ApprovalProductInfoReq();
        params.setVersionList(ListUtils.newArrayList(385L));
        params.setIndustryDept("智慧行业二部");
        Object res = pplInnerProcessService.queryApprovalProductInfo(params);
        System.err.println(JSON.toJson(res));
    }

    @Test
    public void queryForecastConstitute() {
        ForecastConstituteReq params = new ForecastConstituteReq();
        params.setVersionId(466L);
        params.setProduct(Arrays.asList("数据库"));
        params.setDatabaseName("Redis");
        params.setGroupBy(GroupCondition.databaseName);
        params.setBizRangeIsCustomer(true);
        params.setPreSubmit(true);
//        params.setDemandType(ListUtils.newArrayList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()));
        params.setDemandType(ListUtils.newArrayList(PplDemandTypeEnum.NEW.getCode()));
        Object res = pplInnerProcessService.queryForecastConstitute(params);
        System.err.println(JSON.toJson(res));
    }

    @Test
    public void queryForecastInstanceConstitute() {
        ForecastConstituteReq params = new ForecastConstituteReq();
        params.setVersionId(55L);
        params.setProduct(Arrays.asList("CVM&CBS"));
        params.setPreSubmit(false);
        params.setDemandType(
                ListUtils.newArrayList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()));
        Object res = pplInnerProcessService.queryForecastInstanceConstitute(params);
        System.err.println(JSON.toJson(res));
    }

    @Test
    public void queryHistoricalTrends() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        HistoricalTrendReq params = new HistoricalTrendReq();
        params.setVersionId(204L);
        params.setProduct("GPU(裸金属&CVM)");
        params.setQueryTrendChartData(false);
        params.setQueryTableData(true);
//        params.setBeginDemandYearMonth("2023-06");
//        params.setEndDemandYearMonth("2023-11");
//        params.setGroupList(ListUtils.newArrayList(HistoricalTrendGroup.warZone));
        params.setBizRange(ListUtils.newArrayList(HistoricalTrendReq.INDUSTRY_DEPT_ALL_WAR_ZONE));
//        params.setBizRange(ListUtils.newArrayList("虎牙"));
        params.setBizRangeIsCustomer(false);
        params.setWarZone(ListUtils.newArrayList("XXX"));
        params.setExecType(HistoricalTrendReq.EXEC_TYPE_CORE_DAY);
        Object res = pplInnerProcessService.queryHistoricalTrends(params);
        System.err.println(JSON.toJson(res));
        stopWatch.stop();
        System.err.println("times queryHistoricalTrends: " + stopWatch.getTotalTimeMillis());

        HistoricalTrendReq params_2 = new HistoricalTrendReq();
        params_2.setVersionId(204L);
        params_2.setProduct("CVM&CBS");
        params_2.setQueryTrendChartData(false);
        params_2.setQueryTableData(true);
        params_2.setBeginDemandYearMonth("2023-08");
        params_2.setEndDemandYearMonth("2023-08");
        params_2.setDemandType(ListUtils.newArrayList("NEW", "ELASTIC"));
        params_2.setGroupList(ListUtils.newArrayList(HistoricalTrendGroup.warZone, HistoricalTrendGroup.regionName,
                HistoricalTrendGroup.customerShortName, HistoricalTrendGroup.instanceType));
        GroupQueryCondition condition = new GroupQueryCondition();
        condition.setGroup(HistoricalTrendGroup.warZone);
        condition.setValue("华东");
        GroupQueryCondition condition_1 = new GroupQueryCondition();
        condition_1.setGroup(HistoricalTrendGroup.customerShortName);
        condition_1.setValue("拼多多");
        GroupQueryCondition condition_2 = new GroupQueryCondition();
        condition_2.setGroup(HistoricalTrendGroup.regionName);
        condition_2.setValue("上海");
        params_2.setGroupQueryConditions(ListUtils.newArrayList(condition, condition_1, condition_2));
        params_2.setBizRange(ListUtils.newArrayList(HistoricalTrendReq.INDUSTRY_DEPT_ALL_WAR_ZONE));
        Object res_2 = pplInnerProcessService.queryHistoricalTrends(params_2);
        System.err.println(JSON.toJson(res_2));

    }

    @Test
    public void queryHistoricalTrendsDetails() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        HistoricalTrendReq params = new HistoricalTrendReq();
        params.setVersionId(204L);
        params.setProduct("CVM&CBS");
        params.setQueryTrendChartData(false);
        params.setQueryTableData(true);
//        params.setBeginDemandYearMonth("2023-06");
//        params.setEndDemandYearMonth("2023-11");
        params.setGroupList(ListUtils.newArrayList(HistoricalTrendGroup.warZone, HistoricalTrendGroup.regionName,
                HistoricalTrendGroup.customerShortName));
        params.setBizRange(ListUtils.newArrayList(HistoricalTrendReq.INDUSTRY_DEPT_ALL_WAR_ZONE));
//        params.setBizRange(ListUtils.newArrayList("虎牙"));
        params.setBizRangeIsCustomer(false);
        params.setExecType(HistoricalTrendReq.EXEC_TYPE_CORE_DAY);
        Object res = pplInnerProcessService.queryHistoricalTrends(params);
        System.err.println(JSON.toJson(res));
        stopWatch.stop();
        System.err.println("times queryHistoricalTrends: " + stopWatch.getTotalTimeMillis());

    }

    @Test
    public void queryHistoricalTrendsOne() {
        HistoricalTrendReq params = new HistoricalTrendReq();
        params.setVersionId(56L);
        params.setProduct("CVM&CBS");
        params.setQueryTrendChartData(true);
        params.setQueryTableData(false);
        params.setPreSubmit(true);
//        params.setCustomerShortName(ListUtils.newArrayList("超参数", "北京卡路里科技有限公司"));
//        params.setCustomerUin(ListUtils.newArrayList("1794844604"));
//        params.setBeginDemandYearMonth("2023-06");
//        params.setEndDemandYearMonth("2023-11");
//        params.setGroupList(ListUtils.newArrayList(HistoricalTrendGroup.warZone));
        params.setBizRange(ListUtils.newArrayList(HistoricalTrendReq.INDUSTRY_DEPT_ALL_WAR_ZONE));
//        params.setBizRange(ListUtils.newArrayList("虎牙"));
        params.setBizRangeIsCustomer(false);
        params.setExecType(HistoricalTrendReq.EXEC_TYPE_CORE_DAY);
        params.setDemandType(ListUtils.newArrayList("NEW", "ELASTIC"));
        params.setCustomerShortName(ListUtils.newArrayList("上海趣侬网络科技有限公司"));
        params.setCustomerUin(ListUtils.newArrayList("100029282235"));
//        params.setDemandType(ListUtils.newArrayList("RETURN"));
        Object res = pplInnerProcessService.queryHistoricalTrends(params);
        System.err.println(JSON.toJson(res));

    }

    @Test
    public void dayScaleCurve() {
        HistoricalTrendReq params = new HistoricalTrendReq();
        params.setVersionId(56L);
        params.setProduct("CVM&CBS");
//        params.setBeginDemandYearMonth("2023-07");
//        params.setEndDemandYearMonth("2023-07");
        params.setBeginDemandDateForCkScale(LocalDate.of(2023, 7, 1));
        params.setEndDemandDateForCkScale(LocalDate.of(2023, 7, 2));
        params.setDemandType(ListUtils.newArrayList("NEW", "ELASTIC"));
        params.setCustomerShortName(ListUtils.newArrayList("上海趣侬网络科技有限公司"));
        params.setCustomerUin(ListUtils.newArrayList("100029282235"));
        Object res = pplInnerProcessService.dayScaleCurve(params);
        System.err.println(JSON.toJson(res));
    }

    @Test
    public void testPermissionDTO() {
        PermissionDTO p = new PermissionDTO();
        p.setNotNeedCheckPermission(false);
        p.setNotAnyPermission(false);
        List<PermissionVersionItem> permissions = new ArrayList<>();
        PermissionVersionItem item_1 = new PermissionVersionItem();
        item_1.setVersionId(54L);
        List<PermissionVersionRoleItem> role_1 = new ArrayList<>();
        PermissionVersionRoleItem roleItem_1_1 = new PermissionVersionRoleItem();
        roleItem_1_1.setIndustryDept("智慧行业一部");
        roleItem_1_1.setWarZoneList(ListUtils.newArrayList("其他"));
        role_1.add(roleItem_1_1);

        PermissionVersionRoleItem roleItem_1_2 = new PermissionVersionRoleItem();
        roleItem_1_2.setIndustryDept("智慧行业一部");
        roleItem_1_2.setWarZoneList(ListUtils.newArrayList("区域直销", "渠道非转售", "区域代理"));
        role_1.add(roleItem_1_2);

        PermissionVersionRoleItem roleItem_1_3 = new PermissionVersionRoleItem();
        roleItem_1_3.setIndustryDept("智慧行业一部");
        roleItem_1_3.setWarZoneList(ListUtils.newArrayList("区域代理", "社交娱乐", "其他", "渠道非转售", "技术服务",
                "KA", "出海", "电商/生活服务", "游戏", "传媒", "VIP", "区域直销"));
        role_1.add(roleItem_1_3);

        item_1.setRolePermissions(role_1);
        permissions.add(item_1);
        p.setPermissions(permissions);

        WhereContent permissionWhere = p.toWhereWithOutVersionId("智慧行业一部", null);
        System.out.println(permissionWhere.getSql());
        System.out.println(JSON.toJson(permissionWhere.getParams()));
    }

    @Test
    public void correctWarZone() {
        pplInnerProcessService.correctWarZone("PN2401230004", "通用SaaS", 382L);
    }

    @Test
    public void testVersion() {
        PplInnerProcessVersionDO one = demandDBHelper.getOne(PplInnerProcessVersionDO.class, "where id = ?", 468);
//        List<PplItemDO> versionValidPplItem = pplInnerProcessService.getVersionValidPplItem(one);

//        List<PplItemDO> versionValidPplItem = pplInnerProcessService.getExpiredPplItemForVersion(one,"2025-01");
        System.out.println(1);
    }

}
