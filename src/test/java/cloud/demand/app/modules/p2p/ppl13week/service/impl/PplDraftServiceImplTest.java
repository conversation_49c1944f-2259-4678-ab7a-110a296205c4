package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.modules.p2p.industry_demand.service.TodoService.ApprovalMessageBody;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.job.InnerProcessRelevantTask;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.json.JSON;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.ToString;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplDraftServiceImplTest {

    @Autowired
    PplDraftService pplDraftService;
    @Resource
    InnerProcessRelevantTask innerProcessRelevantTask;
    @Resource
    PplInnerVersionService pplInnerVersionService;
    @Resource
    PplInnerProcessService innerProcessService;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private PplVersionService pplVersionService;

//    @Resource
//    private DBHelper demandGpuDBHelper;

    SavePplDraftReq setUp() {
        SavePplDraftReq req = new SavePplDraftReq();
        req.setType("insert");
        req.setProduct("CVM&CBS(不包含GPU机型)");
        req.setCustomerUin("1122344");
        req.setCustomerName("test");
        req.setCustomerShortName("test");
        req.setCustomerSource("");
        req.setCustomerType("EXISTING");

        req.setWarZone("出海");
        req.setBeginBuyDate("2023-04-01");
        req.setEndBuyDate("2023-05-01");

        req.setDemandType("新增需求");
        req.setDemandScene("新项目");
        req.setProjectName("常规");

        List<DraftItemDTO> ls = new ArrayList();
        DraftItemDTO itemDTO = new DraftItemDTO();

        String str = "{\"type\": \"insert\", \"note\": \"备注11\", \"status\": "
                + "\"VALID\", "
                + "\"deleted\": false, \"product\": \"CVM&CBS\", \"winRate\": 99.0, \"billType\": \"包年包月\", \"pplOrder\": \"PN2210281010\", \"zoneName\": \"广州三区\", \"createTime\": \"2022-10-28 14:15:49\", \"demandType\": \"NEW\", \"endBuyDate\": \"2022-10-29\", \"regionName\": \"广州\", \"updateTime\": \"2022-10-28 15:04:01\", \"dataDiskNum\": 1, \"demandScene\": \"winback新增\", \"instanceNum\": 200, \"projectName\": \"腾讯项目\", \"affinityType\": \"母机\", \"beginBuyDate\": \"2022-10-27\", \"dataDiskType\": \"SSD\", \"instanceType\": \"S6\", \"affinityValue\": 9.0, \"instanceModel\": \"S6.12XLARGE128\", \"systemDiskNum\": null, \"endElasticDate\": null, \"systemDiskType\": \"SSD\", \"dataDiskStorage\": 1000, \"beginElasticDate\": null, \"systemDiskStorage\": 100}\n";
        itemDTO = JSON.parse(str, DraftItemDTO.class);
        ls.add(itemDTO);
        req.setResources(ls);
        return req;
    }

    SavePplDraftReq setUp2() {
        SavePplDraftReq req = new SavePplDraftReq();
        req.setType(OperateTypeEnum.UPDATE.getCode());
        req.setProduct("CVM&CBS(不包含GPU机型)");
        req.setCustomerUin("1122344");
        req.setCustomerName("test");
        req.setCustomerShortName("test");
        req.setCustomerSource("");
        req.setCustomerType("EXISTING");
        req.setWarZone("出海");
        req.setBeginBuyDate("2023-04-01");
        req.setEndBuyDate("2023-05-01");
        req.setDemandType("新增需求");
        req.setDemandScene("新项目");
        req.setProjectName("常规");
        req.setPplOrder("PE2303152380");

        List<PplItemDO> ls = demandDBHelper.getAll(PplItemDO.class, "where ppl_order = ?", "PE2303152380");
        String jsonStr = JSON.toJson(ls);
        System.out.println(jsonStr);
        List<DraftItemDTO> dfs = JSON.parse(jsonStr, new TypeReference<List<DraftItemDTO>>() {
        });
        dfs.forEach(s -> {
            s.setType(OperateTypeEnum.UPDATE.getCode());
            s.setNote("皮革厂倒闭拉。");
        });
        req.setResources(dfs);
        return req;
    }

//    @Test
//    void checkSubmit() {
//        SavePplDraftReq req = setUp();
//        System.out.println(JSON.toJsonFormatted(req));
//        pplDraftService.checkSubmit(req);
//        SavePplDraftRsp resp = pplDraftService.saveDraft(req);
//        System.out.println(resp);
//        pplDraftService.submitDraft(Lists.newArrayList(resp.getPplOrder()));
//    }

//    @Test
//    void saveDraft() {
//        SavePplDraftReq req = setUp();
//        pplDraftService.checkSubmit(req);
//        SavePplDraftRsp resp = pplDraftService.saveDraft(req);
//        System.out.println(resp);
//        pplDraftService.submitDraft(Lists.newArrayList(resp.getPplOrder()));
//    }


    @Test
    void removeDraft() throws InterruptedException {

//        Long versionId = 228L;
//        String nodeCode = "98A3887ECB164CA8AFB81FB706A6EA59_AUDIT";
//        PplInnerProcessVersionSlaDO one = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
//                "where node_code = ? and version_id = ?", nodeCode, versionId);
//        innerProcessService.sendTodoAndMoa("oliverychen",
//                one, versionId, "智慧行业八部", "资源拓展中心", null);
        ApprovalMessageBody messageBody = new ApprovalMessageBody();
        messageBody.setApprover("oliverychen");
        messageBody.setApproveResult(0);
        messageBody.setApproverOrder("14db65ed-5d46-4fe6-b30f-2c83dffe4331");
        messageBody.setApproverAppOrder("14db65ed-5d46-4fe6-b30f-2c83dffe4331");
        innerProcessService.acceptTodoCallback(messageBody);
    }

    @Test
    void initData() {
        // 获取下版本需求范围内已生效数据 转换为草稿箱数据，并提交至需求沟通
        PplInnerProcessVersionDO nextVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where status = ? and industry_dept = ?", PplInnerProcessVersionStatusEnum.PROCESSING.getCode(),
                "智慧行业一部");
        List<PplItemDO> versionValidPplItem = getVersionValidPplItem(nextVersion);
        Map<String, List<PplItemDO>> pplOrderToItem = versionValidPplItem.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));
        pplOrderToItem.forEach((k, v) -> {
            // 保存草稿
            PplOrderDO one = demandDBHelper.getOne(PplOrderDO.class, "where ppl_order = ?", k);
            SavePplDraftReq savePplDraftReq = pplItemTransToDraftReq(one, v);
            pplDraftService.saveDraft(savePplDraftReq);
        });
        // 将提交至草稿箱的数据扭转至需求沟通
        demandDBHelper.executeRaw("update ppl_order_draft set draft_status = ? "
                        + "where draft_status = ? and ppl_order in (?) and deleted = 0 ",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                PplOrderDraftStatusEnum.DRAFT.getCode(), pplOrderToItem.keySet());
        demandDBHelper.executeRaw("update ppl_item_draft set draft_status = ? "
                        + "where draft_status = ? and ppl_order in (?) and deleted = 0 ",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                PplOrderDraftStatusEnum.DRAFT.getCode(), pplOrderToItem.keySet());
    }


    List<PplItemDO> getVersionValidPplItem(PplInnerProcessVersionDO versionDO) {
        String beginBuyDate = versionDO.getDemandBeginYear().toString()
                + "-" + (versionDO.getDemandBeginMonth().toString()) + "-" + 1;
        return demandDBHelper.getAll(PplItemDO.class,
                "where instance_num > 0 and status != ?" +
                        "and begin_buy_date >= ? and product = ? " +
                        "and ppl_order in (select ppl_order from ppl_order " +
                        "where deleted = 0 and industry_dept = ? " +
                        "and source = ? )",
                PplItemStatusEnum.APPLIED.getCode(), beginBuyDate,
                Ppl13weekProductTypeEnum.GPU.getName(), versionDO.getIndustryDept(),
                PplOrderSourceTypeEnum.IMPORT.getCode());
    }

    public SavePplDraftReq pplItemTransToDraftReq(PplOrderDO pplOrderDO, List<PplItemDO> pplItemDOS) {
        SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
        BeanUtils.copyProperties(pplOrderDO, savePplDraftReq);
        savePplDraftReq.setType(OperateTypeEnum.ORIGINAL.getCode());
        savePplDraftReq.setBeginBuyDate(pplItemDOS.get(0).getBeginBuyDate().toString());
        savePplDraftReq.setEndBuyDate(pplItemDOS.get(0).getEndBuyDate().toString());
        savePplDraftReq.setProduct(pplItemDOS.get(0).getProduct());
        savePplDraftReq.setSubmitUser(pplOrderDO.getSubmitUser());
        List<SavePplDraftReq.DraftItemDTO> resources = new ArrayList<>();
        for (PplItemDO itemDO : pplItemDOS) {
            SavePplDraftReq.DraftItemDTO draftItemDTO = new SavePplDraftReq.DraftItemDTO();
            BeanUtils.copyProperties(itemDO, draftItemDTO);
            draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
            draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
            if (itemDO.getBeginElasticDate() != null) {
                draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
            }
            if (itemDO.getEndElasticDate() != null) {
                draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
            }
            draftItemDTO.setPplId(itemDO.getPplId());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
            draftItemDTO.setType(savePplDraftReq.getType());
            draftItemDTO.setBizId(itemDO.getBizId());
            draftItemDTO.setInstanceNum(itemDO.getInstanceNum());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            resources.add(draftItemDTO);
        }
        savePplDraftReq.setResources(resources);
        return savePplDraftReq;
    }

    @Test
    void autoInitNewVersion() {
        pplInnerVersionService.autoInitNewVersion("智慧行业一部", 11L);
    }

    @Test
    void queryOrderList() {
    }

//    @Test
////    @Transactional("demandTransactionManager")
//    void initGpu(){
//        List<PplOrderAuditRecordDO> all = demandGpuDBHelper.getAll(PplOrderAuditRecordDO.class, "where deleted = 0 and version_id = 19 and operate_user = 'huiye'");
//        List<String> pplOrder = all.stream().map(PplOrderAuditRecordDO::getPplOrder).collect(Collectors.toList());
//        List<PplOrderDO> pplOrderDOList = demandGpuDBHelper.getAll(PplOrderDO.class, "where ppl_order in (?)", pplOrder);
//        List<PplItemDO> pplItemDOList = demandGpuDBHelper.getAll(PplItemDO.class, "where ppl_order in (?)", pplOrder);
//        System.out.println(1);
//
//        demandDBHelper.executeRaw("update ppl_order set deleted = 1 where ppl_order in (?)",pplOrder);
//        demandDBHelper.executeRaw("update ppl_item set deleted = 1 where ppl_order in (?)",pplOrder);
//
//        for (PplOrderDO pplOrderDO : pplOrderDOList) {
//            pplOrderDO.setId(null);
//            demandDBHelper.insert(pplOrderDO);
//        }
//        for (PplItemDO pplItemDO : pplItemDOList) {
//            pplItemDO.setId(null);
//            demandDBHelper.insert(pplItemDO);
//        }
//        System.out.println(1);
//
//    }
//
//    @Test
//    void initItemQueue(){
//        List<PplAuditItemQueueDO> all = demandGpuDBHelper.getAll(PplAuditItemQueueDO.class, "where version_id = ? ", 19L);
//
//        System.out.println(1);
//        for (PplAuditItemQueueDO pplAuditItemQueueDO : all) {
//            pplAuditItemQueueDO.setId(null);
//            pplAuditItemQueueDO.setVersionId(11L);
//            demandDBHelper.insert(pplAuditItemQueueDO);
//        }
//    }

    @Data
    @ToString
    public class PendingPPlVO extends PplOrderAuditRecordItemDO {

        @RelatedColumn(localColumn = "ppl_order", remoteColumn = "ppl_order")
        private PplOrderDO pplOrderDO;

    }

}