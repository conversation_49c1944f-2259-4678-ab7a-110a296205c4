package cloud.demand.app.modules.p2p.industry_demand;

import cloud.demand.app.modules.p2p.industry_demand.dto.input.MoveInputGroupStatusDTO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandInputCustomerGroupStatusEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandInputGroupService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
@Slf4j
public class IndustryInputGroupServiceTests {

    @Autowired
    private IndustryDemandInputGroupService industryDemandInputGroupService;
    @Autowired
    private PermissionService permissionService;

    @Test
    public void testInitGroup() {
        industryDemandInputGroupService.initInputGroupForVersion("V20220422");
    }

    @Test
    public void testAuth() {
        String userByRole = permissionService.getUserByRole(IndustryDemandAuthRoleEnum.WAR_ZONE_INPUT,
                "战略客户部", "CVM", "华南");
        System.out.println(userByRole);
    }

    @Test
    public void testMoveStatus() {
        MoveInputGroupStatusDTO req = new MoveInputGroupStatusDTO();
        req.setInputGroupId(51L);
        req.setNextStatus(IndustryDemandInputCustomerGroupStatusEnum.PASS.getCode());
        req.setApprover("nickxie");
        req.setApproveMsg("通过");
        req.setAction("通过");

        List<Long> ids = industryDemandInputGroupService.moveInputGroupToStatus(req);
        System.out.println(JSON.toJsonFormatted(ids));
    }

}
