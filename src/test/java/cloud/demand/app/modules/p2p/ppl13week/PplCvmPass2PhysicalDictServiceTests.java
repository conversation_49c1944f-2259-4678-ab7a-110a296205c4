package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.dto.dict.RegionDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.dict.ZoneDTO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class PplCvmPass2PhysicalDictServiceTests {

    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private DictService dictService;
    @Resource
    private DBHelper yuntiDBHelper;


    @Test
    public void test() {
        List<RegionDTO> regionDTOS = industryDemandDictService.listAllRegion();
        System.out.println(JSON.toJsonFormatted(regionDTOS));
    }

    @Test
    public void test2() {
        List<ZoneDTO> zoneDTOS = industryDemandDictService.listAllZone(null);
        System.out.println(JSON.toJsonFormatted(zoneDTOS));
    }

    @Test
    public void testGetRegion() {
        List<String> regions = pplDictService.queryAllRegionName(true);
        System.out.println(JSON.toJsonFormatted(regions));
    }

    @Test
    public void test3() {
        Map<String, String> map = dictService.getCsigDeviceTypeToInstanceTypeMap();
        log.info("{}", JSON.toJson(map));

        Set<Map.Entry<String, String>> entries =
                dictService.getCsigDeviceTypeToInstanceTypeMap().entrySet();
        log.info("{}", JSON.toJson(entries));

        List<Device2InstanceTypeDTO> list = Lang.list();
        ListUtils.forEach(map.entrySet(), o -> {
            list.add(new Device2InstanceTypeDTO(o.getKey(), o.getValue()));
        });


    }

    @Test
    public void test4() {

        List<YuntiStategyZoneDO> all = yuntiDBHelper.getAll(YuntiStategyZoneDO.class);
        Map<String, String> collect = all.stream()
                .collect(Collectors.toMap(YuntiStategyZoneDO::getZone, YuntiStategyZoneDO::getCampusName));

//        Map<String, List<String>> campusByZone = dictService.getCampusByZone();
//        System.out.println(campusByZone);
//        campusByZone.forEach((k, v) -> {
//            System.out.println(k + " : " + v.toString());
//        });
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Device2InstanceTypeDTO {

        private String deviceType;
        private String instanceType;
    }

}
