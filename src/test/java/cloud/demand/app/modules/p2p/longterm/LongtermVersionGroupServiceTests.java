package cloud.demand.app.modules.p2p.longterm;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.longterm.dto.LongTermUnifiedVersionDTO;
import cloud.demand.app.modules.p2p.longterm.dto.LongtermPpl13weekDataDTO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionStatusEnum;
import cloud.demand.app.modules.p2p.longterm.service.LongTermVersionService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermUnifiedVersionService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionApprovalService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class LongtermVersionGroupServiceTests {

    @Resource
    private LongtermVersionGroupService longtermVersionGroupService;
    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private LongtermUnifiedVersionService longtermUnifiedVersionService;

    @Resource
    private LongTermVersionService longTermVersionService;

    @Resource
    private LongtermVersionApprovalService approvalService;

    @Resource
    private DictService dictService;

    @Test
    public void test() {
        boolean result = longtermVersionGroupService.initVersionGroup("V_202412");
        System.out.println(result);
    }

    @Test
    public void testStartVersion() {
        LongTermUnifiedVersionDTO longTermUnifiedVersionDTO = demandDBHelper.getOne(LongTermUnifiedVersionDTO.class,
                "where status = ?",
                UnificatedVersionStatusEnum.PROCESS.getCode());
        longtermUnifiedVersionService.startUnifiedVersion(longTermUnifiedVersionDTO,
                longTermUnifiedVersionDTO.getEventByCode(CrpEventEnum.long_term_version_start_time.getCode()));

//        LongtermVersionDO one = demandDBHelper.getOne(LongtermVersionDO.class, "where status = ?",
//                LongtermVersionStatusEnum.PROCESS.getCode());
//        List<LongtermVersionGroupDO> all = demandDBHelper.getAll(LongtermVersionGroupDO.class, "where version_code = ?",
//                one.getVersionCode());
//
//        List<LongtermPpl13weekDataDTO> ppl13WeekData = longtermVersionGroupService.getPpl13WeekData(one, all);
        System.out.println(1);
    }


    @Test
    public void testGenerateGroupSummaryForMoaAudit() {
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?", 1656);
        System.out.println(JSON.toJson(groupDO));

        String s = longtermVersionGroupService.generateGroupSummaryForMoaAudit(groupDO);
        System.out.println(s);
    }

    @Test
    public void testMsg() {
        LongTermUnifiedVersionDTO version = demandDBHelper.getOne(LongTermUnifiedVersionDTO.class, "where status = ?",
                UnificatedVersionStatusEnum.PROCESS.getCode());
//        longtermUnifiedVersionService.sendOpenVersionMsg(version);

//        longTermVersionService.sendNotSubmitMsg();

//        Map<String, Object> templateParams = new HashMap<>();
//        templateParams.put("operateUser", "oliverychen");
//        templateParams.put("rejectReason","试一下驳回");
//        dictService.eventNotice(CrpEventEnum.long_term_reject_notice.getCode(),null,null,
//                templateParams,approvalService.getNodeCodeProcessor(LongtermVersionGroupStatusEnum.CREATE.getCode(),"智慧行业一部",""));

//        longtermUnifiedVersionService.closeUnifiedVersion(version,version.getEventByCode(CrpEventEnum.long_term_version_end_time.getCode()));

    }
}
