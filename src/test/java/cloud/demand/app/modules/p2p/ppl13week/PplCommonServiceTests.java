package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConvertPhysicalServerService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import com.pugwoo.dbhelper.DBHelper;
import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import yunti.boot.client.JsonrpcClient;
import yunti.boot.config.DynamicProperty;

@SpringBootTest
public class PplCommonServiceTests {

    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private PplConvertPhysicalServerService pplConvertPhysicalServerService;
    @Resource
    private DictService dictService;
    @Resource
    private PplStockSupplyService pplStockSupplyService;
    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplDictService pplDictService;


    @Autowired
    JsonrpcClient jsonrpcClient;
    Supplier<String> todoUrlSupplier = DynamicProperty.create("app.url.todo-url", "");

    @Test
    public void test() {
        String users = pplCommonService.getApproveUser(IndustryDemandAuthRoleEnum.PPL_INDUSTRY_APPROVE,
                "行业一部", "CVM&CBS");
        System.out.println(users);
    }

    @Test
    public void testTodo() {
        TodoService.ApprovalData approvalData = new TodoService.ApprovalData();

        approvalData.setOrderId("*********-10-云计算部门-CVM&CBS");
        approvalData.setTaskId("V202210233-10-云计算部门-CVM&CBS-INDUSTRY_APPROVE-88");
        approvalData.setSystem("YUNTI");
        approvalData.setSourceApp("PPL13WeekDemand-Version");
        approvalData.setSourceEvent("行业审批");
        approvalData.setSourceAppCnName("PPL13周需求-版本管理-");
        approvalData.setActivity("行业审批");
        approvalData.setHandler("oliverychen");
        approvalData.setFormUrl("https://crp.woa.com/13ppl/view-approval/20");
        approvalData.setIsCallBackApi(0);

        List<TodoService.ListView> listViewList = Arrays.asList(
                new TodoService.ListView("【特别说明】", "请访问PC端链接进行审批，暂不支持在待办界面或移动端审批"),
                new TodoService.ListView("链接", "https://crp.woa.com/13ppl/view-approval/20"),
                new TodoService.ListView("版本", "*********-7"),
                new TodoService.ListView("行业部门", "云计算部门"),
                new TodoService.ListView("产品", "CVM&CBS"));
        approvalData.setListView(listViewList);

        approvalData.setCallBackUrl("http://localhost/cloud-demand-app/api/NOT-VALID");

        pplCommonService.createTodo(approvalData);
    }

    @Test
    public void finishTodo() {
        TodoService.ApprovalMessageBody msg = new TodoService.ApprovalMessageBody();

        msg.setApproveMemo("审批通过");
        msg.setApprover("nickxie");
        msg.setApproverOrder("test11545435435-1"); // 实际是靠taskId来结束任务的
        msg.setApproveResult(0); // 0是通过，否则不通过

        pplCommonService.finishTodo(msg);

    }

    @Test
    public void queryVersionCode() {
        pplConvertPhysicalServerService.acceptPushRequest("测试supply");
//        pplConvertPhysicalServerService.acceptPushRequestByGroupId(77L);
        System.out.println(1);
    }

    @Test
    public void syncCustomerUinType() {
        pplCommonService.syncCustomerUinType(true);
    }

    @Test
    public void testInitAdsMckForecastSummaryDF() {
//        pplCommonService.initAdsMckForecastSummaryDF();
    }
}
