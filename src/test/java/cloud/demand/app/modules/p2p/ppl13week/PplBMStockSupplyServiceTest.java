package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.common.excel.core.ReadResult;
import cloud.demand.app.modules.p2p.ppl13week.dto.VersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.CheckStockSupplyResultVO;
import cloud.demand.app.modules.p2p.ppl13week.service.PplBMStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplStockSupplyBMDetailVO;
import com.pugwoo.wooutils.json.JSON;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

@SpringBootTest
public class PplBMStockSupplyServiceTest {

    @Resource
    private PplBMStockSupplyService pplBMStockSupplyService;
    @Resource
    private PplStockSupplyService pplStockSupplyService;

    @Test
    public void startNewStockSupply() {
        pplBMStockSupplyService.startNewStockSupply("V_20230813");
    }

    @Test
    public void importStockSupplyForCheck() throws IOException {
        MultipartFile file = new MockMultipartFile("test.xlx",
                Files.newInputStream(new File("/Users/<USER>/Downloads/裸金属需求对冲空白模版.2.xlsx").toPath()));
        String versionCode = "V_20230813";
        ReadResult<PplStockSupplyBMDetailVO> res = pplBMStockSupplyService.importStockSupplyAndCheck(file, versionCode);
        System.err.println(JSON.toJson(res));
    }

    @Test
    public void finishStockSupply() {
        pplBMStockSupplyService.finishStockSupply("V_20230813");
    }

    @Test
    public void startNewStockSupplyAll() {
        String versionCode = "V_20230828";
        CheckStockSupplyResultVO flag = pplStockSupplyService.checkStartNewStockSupplyAll(versionCode);
        if (flag.isShowStockAll()) {
            pplStockSupplyService.startNewStockSupplyAll(versionCode);
        }
    }

    @Test
    public void downloadStockSupply() {
        VersionGroupReq req = new VersionGroupReq();
        req.setVersionCode("V_20230828");
        pplBMStockSupplyService.downloadStockSupply(req);
    }

}
