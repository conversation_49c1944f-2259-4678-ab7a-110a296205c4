package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.p2p.ppl13week.job.StdTableTask;
import cloud.demand.app.modules.p2p.ppl13week.service.PplOrderAdjustService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStdTableService;
import java.time.LocalDate;
import java.time.YearMonth;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplStdTableServiceTests {

    @Resource
    private PplStdTableService pplStdTableService;

    @Resource
    private PplOrderAdjustService pplOrderAdjustService;

    @Resource
    private StdTableTask stdTableTask;

    @Test
    public void test() {
        pplStdTableService.syncPplItemLatestToCkStdTable();
    }

    @Test
    public void test2() {
        stdTableTask.syncPplItemVersionToCkStdTable();
//        pplStdTableService.syncPplItemVersionToCkStdTable("V_20250618");
    }

    @Test
    public void test3() {
        pplStdTableService.syncPplYunxiaoApplyToCkStdTable();
    }

    @Test
    public void test4() {
        pplStdTableService.syncLatestPplItemVersionBaseToCkStdTable();
    }

    @Test
    public void test5() {
        pplStdTableService.syncLatestPplItemVersion532ToCkStdTable();
    }

    @Test
    public void test6() {
        YearMonth end = YearMonth.of(2023,7);
        pplStdTableService.syncYunxiaoApplyOrderDetailToToCkStdTable(end, 1, false);
    }

    @Test
    public void test7() {
        pplStdTableService.syncPplItemVersionNewestFromStdSwapToCkStdTable();
    }

    @Test
    public void test8() {
        pplStdTableService.syncOrderItemAndInfoToCkStdTable();
    }

    @Test
    public void syncPplItemVersionJoinOrderToCkStdTable() {
        pplStdTableService.syncPplItemVersionJoinOrderToCkStdTable("V_20250521");
    }

    @Test
    public void syncPplItemVersionJoinOrderLatestToCkStdTable() {
        pplStdTableService.syncPplItemVersionJoinOrderLatestToCkStdTable();
    }

    @Test
    public void synPplAndOrderData() {
        pplOrderAdjustService.synPplAndOrderData("2025-07-22");
    }

}
