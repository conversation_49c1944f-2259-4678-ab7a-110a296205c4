package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.*;
import cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemWithOrderVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
public class PplVersionTests {

    @Autowired
    private PplVersionService pplVersionService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplDraftService pplDraftService;
    @Resource
    private Alert alert;

    @Test
    void update(){
        List<PplOrderDO> raw = demandDBHelper.getRaw(PplOrderDO.class, "SELECT\n" +
                "\tDISTINCT a.*\n" +
                "FROM\n" +
                "\tppl_order a\n" +
                "\tLEFT JOIN ppl_item b ON a.ppl_order = b.ppl_order \n" +
                "WHERE\n" +
                "\ta.industry_dept = \"战略客户部\" \n" +
                "\tAND a.deleted = 0 \n" +
                "\tAND b.deleted =0\n" +
                "\tAND b.instance_num > 0\n" +
                "\tAND b.begin_buy_date < '2023-07-01'\n" +
                "\tAND b.begin_buy_date > '2022-12-31'\n" +
                "\tAND source = 'IMPORT'");
        for (PplOrderDO pplOrderDO : raw) {
            pplOrderDO.setNodeCode("");
            demandDBHelper.update(pplOrderDO);
        }
    }


    @Test
    public void test() {
    }


    public SavePplDraftReq pplItemTransToDraftReq(PplOrderDO pplOrderDO,List<PplItemDO> pplItemDOS) {
        SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
        BeanUtils.copyProperties(pplOrderDO,savePplDraftReq);
        savePplDraftReq.setType(OperateTypeEnum.ORIGINAL.getCode());
        savePplDraftReq.setBeginBuyDate(pplItemDOS.get(0).getBeginBuyDate().toString());
        savePplDraftReq.setEndBuyDate(pplItemDOS.get(0).getEndBuyDate().toString());
        savePplDraftReq.setProduct(pplItemDOS.get(0).getProduct());
        savePplDraftReq.setSubmitUser(pplOrderDO.getSubmitUser());
        List<SavePplDraftReq.DraftItemDTO> resources = new ArrayList<>();
        for (PplItemDO itemDO : pplItemDOS) {
            SavePplDraftReq.DraftItemDTO draftItemDTO = new SavePplDraftReq.DraftItemDTO();
            BeanUtils.copyProperties(itemDO, draftItemDTO);
            draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
            draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
            if (itemDO.getBeginElasticDate() != null) {
                draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
            }
            if (itemDO.getEndElasticDate() != null) {
                draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
            }
            draftItemDTO.setPplId(itemDO.getPplId());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
            draftItemDTO.setType(savePplDraftReq.getType());
            draftItemDTO.setBizId(itemDO.getBizId());
            draftItemDTO.setInstanceNum(itemDO.getInstanceNum());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            resources.add(draftItemDTO);
        }
        savePplDraftReq.setResources(resources);
        return savePplDraftReq;
    }

    /**
     * 刷新5月份的gpu的pplItem数据到最新版本 按部门维度
     */
    @Test
    public void flushPplItemToVersionGroupItem() {
        String industryDept = "战略客户部";
        PplVersionGroupDO groupDO = demandDBHelper.getOne(PplVersionGroupDO.class,
                "where industry_dept = ? and product = 'GPU(裸金属&CVM)' and version_code = 'V_20230425'", industryDept);
        PplVersionGroupRecordDO lastRecord = demandDBHelper.getOne(PplVersionGroupRecordDO.class, "where id = " +
                "(select max(id) from ppl_version_group_record where version_group_id = ? and deleted = 0)", groupDO.getId());
        WhereSQL whereSQL = new WhereSQL();
        // item里的状态只有有效和已执行，所以都要
        // 只要满足条件的ppl单   23-2-6 继承ppl单 只要求ppl大于版本启动时间, 可不小于版本截止时间
        whereSQL.and("ppl_order in (select ppl_order from ppl_order"
                + " where deleted=0 and year=2023 and month=5 and industry_dept = ? and source!='SYNC_YUNXIAO')", industryDept);
        whereSQL.and("status!='APPLIED'");

        List<PplItemWithOrderVO> all = demandDBHelper.getAll(PplItemWithOrderVO.class, whereSQL.getSQL(), whereSQL.getParams());

        List<PplItemWithOrderVO> list = all.stream().filter(v -> v.getProduct().equals("GPU(裸金属&CVM)") && v.getInstanceNum() > 0).collect(Collectors.toList());
        // 插入数据
        List<PplVersionGroupRecordItemDO> recordItems = ListUtils.transform(list, o -> {
            PplVersionGroupRecordItemDO d = new PplVersionGroupRecordItemDO();
            d.setStatus(o.getStatus());
            d.setVersionGroupId(groupDO.getId());
            d.setRecordVersion(lastRecord.getRecordVersion());
            d.setVersionGroupRecordId(lastRecord.getId());
            d.setPplOrder(o.getPplOrder());
            d.setPplId(o.getPplId());
            d.setProduct(o.getProduct());
            d.setDemandType(o.getDemandType());
            d.setDemandScene(o.getDemandScene());
            d.setProjectName(o.getProjectName());
            d.setBillType(o.getBillType());
            d.setWinRate(o.getWinRate());
            d.setBeginBuyDate(o.getBeginBuyDate());
            d.setEndBuyDate(o.getEndBuyDate());
            d.setBeginElasticDate(o.getBeginElasticDate());
            d.setEndElasticDate(o.getEndElasticDate());
            d.setNote(o.getNote());
            d.setRegionName(o.getRegionName());
            d.setZoneName(o.getZoneName());
            d.setInstanceType(o.getInstanceType());
            d.setInstanceModel(o.getInstanceModel());
            d.setInstanceNum(o.getInstanceNum());

            Tuple2<Integer, Integer> cpuRam = P2PInstanceModelParse.parseInstanceModel(
                    o.getInstanceModel());
            if (o.getInstanceNum() != null) {
                d.setTotalCore(o.getInstanceNum() * cpuRam._1);
            }
            d.setAlternativeInstanceType(o.getAlternativeInstanceType());
            d.setAffinityType(o.getAffinityType());
            d.setAffinityValue(o.getAffinityValue());
            d.setSystemDiskType(o.getSystemDiskType());
            d.setSystemDiskStorage(o.getSystemDiskStorage());
            d.setSystemDiskNum(o.getSystemDiskNum());
            d.setDataDiskType(o.getDataDiskType());
            d.setDataDiskStorage(o.getDataDiskStorage());
            d.setDataDiskNum(o.getDataDiskNum());
            /*
             * 继承版本，使用创建版本的人作为 creator
             */
            d.setCreator(o.getPplOrderDO().getSubmitUser());

            //gpu
            d.setGpuProductType(o.getGpuProductType());
            d.setGpuType(o.getGpuType());
            d.setGpuNum(o.getGpuNum());
            d.setIsAcceptAdjust(o.getIsAcceptAdjust());
            d.setAcceptGpu(o.getAcceptGpu());
            d.setTotalGpuNum(o.getTotalGpuNum());
            d.setBizScene(o.getBizScene());
            d.setBizDetail(o.getBizDetail());
            d.setServiceTime(o.getServiceTime());
            return d;
        });
        demandDBHelper.insertBatchWithoutReturnId(recordItems);
    }
}
