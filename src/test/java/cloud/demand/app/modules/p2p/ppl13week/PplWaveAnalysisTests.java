package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryWaveReq;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplWaveAnalysisServiceImpl;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class PplWaveAnalysisTests {

    @Resource
    PplWaveAnalysisServiceImpl service;
    @Resource
    DBHelper demandDBHelper;

    @Test
    public void test1(){
        List<PplVersionVO> all = demandDBHelper.getAll(PplVersionVO.class, "where version_type = 'MONTH'");
        all = ListUtils.filter(all, o -> {
            if (o.getStartYearMonth().compareTo("2022-12") > 0){
                return false;
            }
            if (o.getEndYearMonth().compareTo("2022-11") < 0){
                return false;
            }
            return true;
        });
        ListUtils.sortDescNullLast(all, o -> o.getId());
        all.forEach(System.out::println);
    }

    @Test
    public void test2(){
        String d1 = "2022-03";
        String d2 = "2022-12";
        System.out.println(d1.compareTo(d2));
    }

    @Test
    public void test3(){
        QueryWaveReq req = new QueryWaveReq();
        req.setBeginYearMonth(DateUtils.parse("2023-02"));
        req.setEndYearMonth(DateUtils.parse("2023-03"));
        System.out.println(service.compareAnalysis(req));
    }
}
