package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.ppl13week.service.UnificatedVersionService;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class UnificatedVersionServiceImplTest {

    @Resource
    private UnificatedVersionService unificatedVersionService;
    @Resource
    private DictService dictService;


    @Test
    public void checkUnificatedVersionInit() {
        dictService.eventNotice("stock_supply_done", "", "");
    }

}