package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekSplitService;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.web.bind.annotation.RestController;


@SpringBootTest
class Ppl13weekSplitServiceImplTest {


    @Resource
    Ppl13weekSplitService ppl13weekSplitService;



    @Test
    public void splitProjectCustomBgDeviceGroupTest() {

        ppl13weekSplitService.splitProjectCustomBgDeviceGroup(1207L,"测试", "test");


    }



}