package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySpikeDetailReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySpikeDetailResp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySplitDetailForPplReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp;
import com.pugwoo.wooutils.json.JSON;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class Ppl13WeekRateViewServiceImplTest {

    @Resource
    Ppl13WeekRateViewServiceImpl ppl13WeekRateViewService;

    @Test
    void querySplitDetailForPpl() {

        QuerySplitDetailForPplReq req = new QuerySplitDetailForPplReq();
        req.setLookBackDate(null);
        req.setProduct("EKS");
        QuerySplitDetailForPplResp resp = ppl13WeekRateViewService.querySplitDetailForPpl(req);
        System.out.println(JSON.toJsonFormatted(resp));


        req = new QuerySplitDetailForPplReq();
        req.setLookBackDate(null);
        req.setProduct("CDB");
        resp = ppl13WeekRateViewService.querySplitDetailForPpl(req);
        System.out.println(JSON.toJsonFormatted(resp));

        req = new QuerySplitDetailForPplReq();
        req.setLookBackDate(null);
        req.setProduct("EMR");
        resp = ppl13WeekRateViewService.querySplitDetailForPpl(req);
        System.out.println(JSON.toJsonFormatted(resp));
    }

    @Test
    void querySpikeDetail() {
        // 使用一个示例任务ID进行测试
        QuerySpikeDetailReq req = new QuerySpikeDetailReq();
        req.setTaskId(1L); // 请根据实际情况修改任务ID

        QuerySpikeDetailResp resp = ppl13WeekRateViewService.querySpikeDetail(req);

        System.out.println("毛刺明细查询结果:");
        System.out.println(JSON.toJsonFormatted(resp));

        if (resp.getSpikes() != null) {
            System.out.println("共查询到 " + resp.getSpikes().size() + " 条毛刺记录");
        }
    }
}