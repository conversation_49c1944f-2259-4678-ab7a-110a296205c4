package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryInnerPplOrderListReq;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderQueryTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplOrderAuditRecordService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderVo;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.logging.log4j.util.Strings;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplInnerProcessTests {

    @Autowired
    private PplInnerProcessService innerProcessService;
    @Autowired
    private PplDictService pplDictService;
    @Autowired
    private PplOrderAuditRecordService pplOrderAuditRecordService;

    @Resource
    PplOrderAuditRecordService auditRecordService;

    @Test
    public void queryDiagram() {
//        innerProcessService.getProcessByDeptAndProduct("智慧行业一部");
        System.out.println(1);
    }

    @Test
    public void queryInnerOrderVo() {
        QueryInnerPplOrderListReq req = new QueryInnerPplOrderListReq();
        req.setQueryType(PplOrderQueryTypeEnum.MY_AUDIT.getCode());
        List<PplOrderVo> pplOrderVos = innerProcessService.queryInnerPplOrderList(req);
        System.out.println(1);
    }

    @Test
    public void queryDictTest() {
        Long versionId = 1L;
        String industryDept = "智慧行业一部";
        List<PplOrderAuditRecordVO> allRecord = auditRecordService.queryHistoryAuditRecord(null, versionId);
        Set<String> operateUserList = allRecord.stream().filter(v -> v.getOperateUser() != null)
                .map(PplOrderAuditRecordVO::getOperateUser).collect(Collectors.toSet());
        String join = Strings.join(operateUserList, ';');
        System.out.println(11);
//        sendMail(join, null, "FINISH", null, versionId, null, null);
    }
}
