package cloud.demand.app.modules.p2p.ppl13week_forecast.service;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.p2p.ppl13week_forecast.VO.DwdYuntiCvmDemandForecastItemDfVO;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.function.Function;
import javax.annotation.Resource;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
class Ppl13weekZiyanRateTest {

    @Resource
    private DBHelper obsDBHelper;


    @Test
    void getZiyanRate() {

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/ziyan/monthly_ziyan_biz_rate.sql");

        YearMonth begin = YearMonth.of(2023, 1);
        YearMonth end = YearMonth.of(2024, 1);

        YearMonth index = JSON.clone(begin);

        StringBuilder timeSql = new StringBuilder();
        while (!index.isAfter(end)) {
            String oneSql = String.format(" or (stat_date in ('%s') and toDate(use_time) between '%s' and '%s')\n",
                    index.plusMonths(1).atDay(1), index.atDay(1), index.atDay(index.lengthOfMonth()));
            timeSql.append(oneSql);
            index = index.plusMonths(1);
        }
        String andSql = " and ( 1=0  " + timeSql + " ) ";
        String sql1 = sql.replace("${CONDITION}", andSql);
        List<DwdYuntiCvmDemandForecastItemDfVO> raw = DBList.prodReadOnlyCkStdCrpDBHelper.getRaw(
                DwdYuntiCvmDemandForecastItemDfVO.class, sql1);
        raw = transInstanceTypeForZiyan(raw);

        index = JSON.clone(begin);
        timeSql = new StringBuilder();
        while (!index.isAfter(end)) {
            String oneSql = String.format(" or (stat_date in ('%s') and toDate(use_time) between '%s' and '%s')\n",
                    index.plusMonths(0).atDay(1), index.atDay(1), index.atDay(index.lengthOfMonth()));
            timeSql.append(oneSql);
            index = index.plusMonths(1);
        }
        andSql = " and ( 1=0  " + timeSql + " ) ";
        sql1 = sql.replace("${CONDITION}", andSql);
        List<DwdYuntiCvmDemandForecastItemDfVO> predict1 = DBList.prodReadOnlyCkStdCrpDBHelper.getRaw(
                DwdYuntiCvmDemandForecastItemDfVO.class, sql1);
        predict1 = transInstanceTypeForZiyan(predict1);

        index = JSON.clone(begin);
        timeSql = new StringBuilder();
        while (!index.isAfter(end)) {
            String oneSql = String.format(" or (stat_date in ('%s') and toDate(use_time) between '%s' and '%s')\n",
                    index.minusMonths(1).atDay(1), index.atDay(1), index.atDay(index.lengthOfMonth()));
            timeSql.append(oneSql);
            index = index.plusMonths(1);
        }
        andSql = " and ( 1=0  " + timeSql + " ) ";
        sql1 = sql.replace("${CONDITION}", andSql);
        List<DwdYuntiCvmDemandForecastItemDfVO> predict2 = DBList.prodReadOnlyCkStdCrpDBHelper.getRaw(
                DwdYuntiCvmDemandForecastItemDfVO.class, sql1);
        predict2 = transInstanceTypeForZiyan(predict2);

        index = JSON.clone(begin);
        timeSql = new StringBuilder();
        while (!index.isAfter(end)) {
            String oneSql = String.format(" or (stat_date in ('%s') and toDate(use_time) between '%s' and '%s')\n",
                    index.minusMonths(2).atDay(1), index.atDay(1), index.atDay(index.lengthOfMonth()));
            timeSql.append(oneSql);
            index = index.plusMonths(1);
        }
        andSql = " and ( 1=0  " + timeSql + " ) ";
        sql1 = sql.replace("${CONDITION}", andSql);
        List<DwdYuntiCvmDemandForecastItemDfVO> predict3 = DBList.prodReadOnlyCkStdCrpDBHelper.getRaw(
                DwdYuntiCvmDemandForecastItemDfVO.class, sql1);
        predict3 = transInstanceTypeForZiyan(predict3);

        Map<YearMonth, Map<String, DwdYuntiCvmDemandForecastItemDfVO>> stringMapMap = convertListToMap(raw,
                (o) -> YearMonth.of(o.getYear(), o.getMonth()), (o) -> o.getGinsFamily() + o.getRegionName1());

        Map<YearMonth, Map<String, DwdYuntiCvmDemandForecastItemDfVO>> predict1map = convertListToMap(predict1,
                (o) -> YearMonth.of(o.getYear(), o.getMonth()), (o) -> o.getGinsFamily() + o.getRegionName1());
        Map<YearMonth, Map<String, DwdYuntiCvmDemandForecastItemDfVO>> predict2map = convertListToMap(predict2,
                (o) -> YearMonth.of(o.getYear(), o.getMonth()), (o) -> o.getGinsFamily() + o.getRegionName1());
        Map<YearMonth, Map<String, DwdYuntiCvmDemandForecastItemDfVO>> predict3map = convertListToMap(predict3,
                (o) -> YearMonth.of(o.getYear(), o.getMonth()), (o) -> o.getGinsFamily() + o.getRegionName1());

        for (YearMonth ym : stringMapMap.keySet()) {
            Map<String, DwdYuntiCvmDemandForecastItemDfVO> value = stringMapMap.get(ym);
            BigDecimal rate = BigDecimal.ZERO;
            // 包含3个月的预测数据
            if (predict1map.containsKey(ym) && predict2map.containsKey(ym) && predict3map.containsKey(ym)) {
                double totalReal = value.values().stream().mapToDouble((o) -> o.getNewDiffReal().doubleValue()).sum();
                Map<String, DwdYuntiCvmDemandForecastItemDfVO> value1 = predict1map.get(ym);
                Map<String, DwdYuntiCvmDemandForecastItemDfVO> value2 = predict2map.get(ym);
                Map<String, DwdYuntiCvmDemandForecastItemDfVO> value3 = predict3map.get(ym);

                double totalPredictSum = 0;
                for (String s : value.keySet()) {

                    BigDecimal v1 = getOrDefault(value1, s).multiply(BigDecimal.valueOf(0.2));
                    BigDecimal v2 = getOrDefault(value2, s).multiply(BigDecimal.valueOf(0.3));
                    BigDecimal v3 = getOrDefault(value3, s).multiply(BigDecimal.valueOf(0.5));

                    BigDecimal totalPredict = v1.add(v2).add(v3);
                    totalPredictSum += totalPredict.doubleValue();

                    if (totalReal > 1) {
                        BigDecimal weight = value.get(s).getNewDiffReal().divide(BigDecimal.valueOf(totalReal), 16,
                                RoundingMode.HALF_UP);
                        BigDecimal minMax = getMinMax(totalPredict, value.get(s).getNewDiffReal());
                        rate = rate.add(weight.multiply(minMax));
                    }
                }
                System.out.printf("ym: %s, totalReal: %f,totalPredictSum: %f, rate: %f\n", ym, totalReal,
                        totalPredictSum, rate);
            }
        }

    }

    private BigDecimal getOrDefault(Map<String, DwdYuntiCvmDemandForecastItemDfVO> value1, String s) {
        BigDecimal v = null;
        DwdYuntiCvmDemandForecastItemDfVO dwdYuntiCvmDemandForecastItemDfVO = value1.get(s);
        if (dwdYuntiCvmDemandForecastItemDfVO == null) {
            return BigDecimal.ZERO;
        }
        v = dwdYuntiCvmDemandForecastItemDfVO.getNewDiffPredict();
        return v;
    }

    private BigDecimal getMinMax(BigDecimal totalPredict, BigDecimal newDiffReal) {
        BigDecimal min = totalPredict.min(newDiffReal);
        BigDecimal max = totalPredict.max(newDiffReal);

        // 考虑0的情况
        if (BigDecimal.ONE.compareTo(min) > 0) {
            return BigDecimal.ZERO;
        }
        if (BigDecimal.ONE.compareTo(max) > 0) {
            return BigDecimal.ZERO;
        }
        return min.divide(max, 16, RoundingMode.HALF_UP);
    }

    public static <T> Map<YearMonth, Map<String, T>> convertListToMap(List<T> list,
            Function<T, YearMonth> mainKeyExtractor, Function<T, String> nestedKeyExtractor) {
        Map<YearMonth, Map<String, T>> resultMap = new HashMap<>();
        for (T item : list) {
            YearMonth mainKey = mainKeyExtractor.apply(item);
            String nestedKey = nestedKeyExtractor.apply(item);

            if (!resultMap.containsKey(mainKey)) {
                resultMap.put(mainKey, new HashMap<>());
            }

            resultMap.get(mainKey).put(nestedKey, item);
        }

        return resultMap;
    }

    private List<DwdYuntiCvmDemandForecastItemDfVO> transInstanceTypeForZiyan(
            List<DwdYuntiCvmDemandForecastItemDfVO> details) {
        // 目前没有黑名单机制，如果有，则在这里filter过滤掉，不要在收敛之后再去过滤
        String sql = "select distinct CvmInstanceTypeCode,CvmInstanceGroup from bas_obs_cloud_cvm_type";
        Map<String, String> mapping = ORMUtils.db(obsDBHelper).getKVMap(sql);

        Map<String, List<DwdYuntiCvmDemandForecastItemDfVO>> detailsMap = ListUtils.toMapList(details,
                o -> StringTools.join("&", mapping.getOrDefault(o.getGinsFamily(), o.getGinsFamily()),
                        o.getFirstDay(), o.getRegionName1()),
                o -> o);
        return ListUtils.transform(detailsMap.values(), o -> {
            DwdYuntiCvmDemandForecastItemDfVO first = JSON.clone(o.get(0)); // 不要影响原值
            first.setGinsFamily(mapping.getOrDefault(first.getGinsFamily(), first.getGinsFamily()));
            BigDecimal real = NumberUtils.sum(o, DwdYuntiCvmDemandForecastItemDfVO::getNewDiffReal);
            BigDecimal predict = NumberUtils.sum(o, DwdYuntiCvmDemandForecastItemDfVO::getNewDiffPredict);
            first.setNewDiffReal(real);
            first.setNewDiffPredict(predict);
            return first;
        });
    }

    @Data
    public static class A {

        B b;

        public A() {
            b = new B();
        }

        public static A create() {
            return new A();
        }
    }

    @Data
    public static class B {

        private final List<Runnable> hold = Lists.newArrayList();

        public B() {
            Runnable callback = B.this::getData;
            this.hold.add(callback);
            for (int i = 0; i < 10000; i++) data += (new Random()).nextInt();
            data = data + data + data;
            data = data + data + data;
            data = data + data + data;
            data = data + data + data;
            data = data + data + data;
            data = data + data + data;
            System.out.println(data.length());
        }

        String data = "";
    }

    public static void main(String[] args) throws InterruptedException {
        for (int i = 0; i < 1000000; i++) {
            A.create();
            Thread.sleep(2);
        }
    }
}