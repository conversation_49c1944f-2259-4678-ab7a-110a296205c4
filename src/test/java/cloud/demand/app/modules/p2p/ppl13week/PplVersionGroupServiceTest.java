package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputDetailDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplVersionGroupServiceTest {

    @Resource
    PplVersionGroupService pplVersionGroupService;

    @Test
    public void queryLatestPplVersionItem() {
        List<String> pplId = ListUtils.newArrayList("PN2305300006-001","PN2306080009-001");
        List<PplVersionGroupRecordItemDO> result = pplVersionGroupService
                .queryLatestPplVersionItem("V_20230823", pplId);
        System.err.println(result);
    }


    @Resource
    private DBHelper demandDBHelper;

    @Test
    public  void test(){

        System.out.println(((SpringJdbcDBHelper) demandDBHelper).getJdbcTemplate().getFetchSize());

        System.out.println(demandDBHelper.getAllForStream(PplForecastInputDetailDO.class).limit(10));


    }

    @Test
    public void forecastApplyMatch() {
        pplVersionGroupService.forecastApplyMatch(3055L);
    }

}
