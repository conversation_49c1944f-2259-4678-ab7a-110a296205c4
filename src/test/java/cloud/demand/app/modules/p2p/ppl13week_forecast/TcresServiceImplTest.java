package cloud.demand.app.modules.p2p.ppl13week_forecast;

import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13WeekInputServiceImpl;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
class TcresServiceImplTest {

    @Resource
    Ppl13WeekInputServiceImpl tcresService;

}