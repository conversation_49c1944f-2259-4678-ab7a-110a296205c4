package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplOrderItemTests {

    @Autowired
    private DBHelper demandDBHelper;
    @Resource
    private PplCommonService pplCommonService;

    @Test
    public void addSomePplId() {
        for (int i = 0; i < 10; i++) {
            gen();
        }
    }

    private List<String> projectNames = ListUtils.newList("双11大促");

    private void gen() {
        PplOrderDO pplOrderDO = new PplOrderDO();

        pplOrderDO.setPplOrder(pplCommonService.generatePplOrderId("N"));
        pplOrderDO.setSource(PplOrderSourceTypeEnum.IMPORT.getCode());
        pplOrderDO.setSubmitUser("nickxie");
        pplOrderDO.setIndustry("电商");
        pplOrderDO.setIndustryDept("智慧行业一部");
        pplOrderDO.setCustomerType(CustomerTypeEnum.EXISTING.getCode());
        pplOrderDO.setCustomerShortName("杭州永卓电子商务有限公司");
        pplOrderDO.setCustomerName("杭州永卓电子商务有限公司");
        pplOrderDO.setStatus(PplOrderStatusEnum.VALID.getCode());
        pplOrderDO.setCustomerUin("100010274760");
        pplOrderDO.setWarZone("中长尾个人");
        pplOrderDO.setCustomerSource("中长尾");

        List<PplItemDO> pplItems = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            PplItemDO pplItemDO = new PplItemDO();
            pplItems.add(pplItemDO);

            pplItemDO.setPplOrder(pplOrderDO.getPplOrder());
            pplItemDO.setPplId(pplCommonService.generatePplItemId(pplOrderDO.getPplOrder()));
            pplItemDO.setStatus(PplItemStatusEnum.VALID.getCode());
            pplItemDO.setProduct("CVM&CBS");
            pplItemDO.setDemandType("NEW");
            pplItemDO.setDemandScene("存量扩容");
            pplItemDO.setProjectName(projectNames.get(i % projectNames.size()));
            pplItemDO.setBillType("包年包月");
            pplItemDO.setBeginBuyDate(DateUtils.parseLocalDate("2023-06-15"));
            pplItemDO.setEndBuyDate(DateUtils.parseLocalDate("2023-06-28"));
            pplItemDO.setRegionName("上海");
            pplItemDO.setZoneName("上海三区");
            pplItemDO.setInstanceType("IT5nt");
            pplItemDO.setInstanceModel("IT5nt.21XLARGE208");
            pplItemDO.setTotalCore(84 * 10);
            pplItemDO.setTotalDisk((100 + 10 * 1000) * 10);
            pplItemDO.setSystemDiskType("高性能");
            pplItemDO.setSystemDiskNum(1);
            pplItemDO.setSystemDiskStorage(100);
            pplItemDO.setDataDiskType("SSD");
            pplItemDO.setDataDiskNum(10);
            pplItemDO.setDataDiskStorage(1000);
            pplItemDO.setInstanceNum(10);
            pplItemDO.setAppRole("CVM");
            pplItemDO.setOrderCategory("CVM");
        }

        demandDBHelper.insert(pplItems);
        // pplOrder total core 和 totaldisk
        pplOrderDO.setAllCore(NumberUtils.sum(pplItems, o -> o.getTotalCore()).intValue());
        pplOrderDO.setAllDisk(NumberUtils.sum(pplItems, o -> o.getTotalDisk()).intValue());

        demandDBHelper.insert(pplOrderDO);

        System.out.println("insert ppl order:" + pplOrderDO.getPplOrder());

    }

}
