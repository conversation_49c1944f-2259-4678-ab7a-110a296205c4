package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import java.time.LocalDate;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class Ppl13weekCommonDataAccessImplTest {

    @Resource
    Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;
    @Test
    void createYearMonthCustomerDefines() {
        ppl13weekCommonDataAccess.createYearMonthCustomerDefines(LocalDate.now());
    }


    @Test
    void getHeadCustomerShortName() {
        Map<String, Set<String>> head = ppl13weekCommonDataAccess.getHeadCustomerShortNameGroup();


        head.forEach((k,v)->{
            System.out.println(k + " = " + v.toString());
        });


        System.out.println(ppl13weekCommonDataAccess.getHeadCustomerShortName());
        System.out.println(ppl13weekCommonDataAccess.getHeadCustomerShortName().size());
    }


    @Test
    void createVersionItemYearMonthCustomerDefines() {

        LocalDate start = LocalDate.of(2023, 3, 1);
        LocalDate end = LocalDate.of(2023, 5, 1);
        while (!start.isAfter(end)) {
            ppl13weekCommonDataAccess.createVersionItemYearMonthCustomerDefines(start);
            start = start.plusMonths(1);
        }

    }

    @Test
    void getZiyanGinsfamily2DeviceGroup() {

        Map<String, String> map = ppl13weekCommonDataAccess.getZiyanGinsfamily2DeviceGroup();
        System.out.println(map);

    }




}