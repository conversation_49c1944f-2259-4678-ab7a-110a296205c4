package cloud.demand.app.modules.p2p.industry_demand;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.BaseProductDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.BigDataDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.CbsDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.CvmDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.GpuDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.product.MetalDTO;
import cloud.demand.app.modules.p2p.industry_demand.utils.ExcelUtil;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

@SpringBootTest
@Slf4j
public class ExcelTest {

    private MultipartFile getFile(String path) throws IOException {
        File file = ResourceUtils.getFile("classpath:" + path);
        return new MockMultipartFile(file.getName(), Files.newInputStream(file.toPath()));
    }

    private <T extends BaseProductDTO> List<T> getDTOList(String path, Class<T> tClass) throws IOException {
        MultipartFile multipartFile = getFile(path);
        List<YearMonth> yearMonthList = DateUtils.listYearMonth(2023, 1, 2023, 12);
        return ExcelUtil.baseDecodeExcel(multipartFile, tClass, yearMonthList, null);
    }

    @Test
    public void testDecodeExcel() throws IOException {
        List<YearMonth> yearMonthList = DateUtils.listYearMonth(2023, 1, 2023, 12);
        Map<String, Object> cvm = ExcelUtil.decodeExcel(getFile("excel/cvm.xlsx"), CvmDTO.class, yearMonthList, null);
        Map<String, Object> cbs = ExcelUtil.decodeExcel(getFile("excel/cbs.xlsx"), CbsDTO.class, yearMonthList, null);
        Map<String, Object> metal = ExcelUtil.decodeExcel(getFile("excel/metal.xlsx"), MetalDTO.class, yearMonthList,
                null);
        Map<String, Object> gpu = ExcelUtil.decodeExcel(getFile("excel/gpu.xlsx"), GpuDTO.class, yearMonthList, null);
        Map<String, Object> bigData = ExcelUtil.decodeExcel(getFile("excel/bigdata.xlsx"), BigDataDTO.class,
                yearMonthList, null);
        assert ((List) cvm.get("data")).size() > 0;
        assert ((List) cbs.get("data")).size() > 0;
        assert ((List) metal.get("data")).size() > 0;
        assert ((List) gpu.get("data")).size() > 0;
        assert ((List) bigData.get("data")).size() > 0;
    }

    @Test
    public void testBaseDecodeExcel() throws IOException {
        List<CvmDTO> cvmDTOList = getDTOList("excel/cvm.xlsx", CvmDTO.class);
        assert cvmDTOList.get(0).getYearMonthValueList().get(0).getNum().equals(100);
        assert cvmDTOList.get(1).getPrePaidType().equals("包年包月");

        List<BigDataDTO> bigDataDTOList = getDTOList("excel/bigdata.xlsx", BigDataDTO.class);
        assert bigDataDTOList.get(0).getYearMonthValueList().get(1).getNum().equals(100);
        assert bigDataDTOList.get(1).getPlatformArch().equals("INTEL");

        List<GpuDTO> gpuDTOList = getDTOList("excel/gpu.xlsx", GpuDTO.class);
        assert gpuDTOList.get(0).getYearMonthValueList().get(2).getNum().equals(100);
        assert gpuDTOList.get(1).getGpuType().equals("P4");

        List<MetalDTO> metalDTOList = getDTOList("excel/metal.xlsx", MetalDTO.class);
        assert metalDTOList.get(0).getYearMonthValueList().get(3).getNum().equals(100);
        assert metalDTOList.get(1).getInstanceType().equals("S5");

        List<CbsDTO> cbsDTOList = getDTOList("excel/cbs.xlsx", CbsDTO.class);
        assert cbsDTOList.get(0).getYearMonthValueList().get(4).getNum().equals(100);
        assert cbsDTOList.get(1).getDiskType().equals("增强型SSD云硬盘");
    }

    @Test
    public void testMap2DTO() throws IOException {
        List<YearMonth> yearMonthList = DateUtils.listYearMonth(2023, 1, 2023, 12);
        Map<String, Object> cvm = ExcelUtil.decodeExcel(getFile("excel/cvm.xlsx"), CvmDTO.class, yearMonthList, null);
        Map<String, Object> cbs = ExcelUtil.decodeExcel(getFile("excel/cbs.xlsx"), CbsDTO.class, yearMonthList, null);
        Map<String, Object> metal = ExcelUtil.decodeExcel(getFile("excel/metal.xlsx"), MetalDTO.class, yearMonthList,
                null);
        Map<String, Object> gpu = ExcelUtil.decodeExcel(getFile("excel/gpu.xlsx"), GpuDTO.class, yearMonthList, null);
        Map<String, Object> bigData = ExcelUtil.decodeExcel(getFile("excel/bigdata.xlsx"), BigDataDTO.class,
                yearMonthList, null);

        List<CvmDTO> cvmDTOS = ExcelUtil.map2DTO((List<Map<String, Object>>) cvm.get("data"), yearMonthList,
                CvmDTO.class);
        List<CbsDTO> cbsDTOS = ExcelUtil.map2DTO((List<Map<String, Object>>) cbs.get("data"), yearMonthList,
                CbsDTO.class);
        List<MetalDTO> metalDTOS = ExcelUtil.map2DTO((List<Map<String, Object>>) metal.get("data"), yearMonthList,
                MetalDTO.class);
        List<GpuDTO> gpuDTOS = ExcelUtil.map2DTO((List<Map<String, Object>>) gpu.get("data"), yearMonthList,
                GpuDTO.class);
        List<BigDataDTO> bigDataDTOS = ExcelUtil.map2DTO((List<Map<String, Object>>) bigData.get("data"), yearMonthList,
                BigDataDTO.class);

        assert !CollectionUtils.isEmpty(cvmDTOS);
        assert !CollectionUtils.isEmpty(cbsDTOS);
        assert !CollectionUtils.isEmpty(metalDTOS);
        assert !CollectionUtils.isEmpty(gpuDTOS);
        assert !CollectionUtils.isEmpty(bigDataDTOS);
    }
}
