package cloud.demand.app.modules.common.service;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class DictServiceTest {


    @Resource
    DictService dictService;

    @Test
    void getZoneName2RegionName() {
        IntStream.range(1, 30).mapToObj((o) -> CompletableFuture.runAsync(() -> {
                    Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
                    System.out.println(zoneName2RegionName.size());
                })
        ).collect(Collectors.toList()).forEach((o) -> {
            try {
                o.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        });
    }
}