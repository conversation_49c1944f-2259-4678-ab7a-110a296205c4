package cloud.demand.app.modules.common;

import cloud.demand.app.entity.demand.CdTaskRunLogDO;
import cloud.demand.app.modules.common.service.TaskLog;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class TaskLogCase {

    @Resource
    DBHelper demandDBHelper;

    @TaskLog(taskName = "TestTask")
    @Transactional(value = "demandTransactionManager")
    public Integer testTaskLog() {
        int insert = demandDBHelper.insert(new CdTaskRunLogDO());
        System.out.println(insert);
        int i = 1 / 0;
        return 3;
    }

}
