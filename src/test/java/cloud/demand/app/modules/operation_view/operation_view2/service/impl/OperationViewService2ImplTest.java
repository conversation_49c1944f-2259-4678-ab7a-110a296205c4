package cloud.demand.app.modules.operation_view.operation_view2.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.common.utils.TimeUtils;
import cloud.demand.app.modules.operation_view.inventory_health.enums.CustomerCustomGroupEnum;
import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsApiSuccessDataDfLocalDO;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewReq2;
import cloud.demand.app.modules.operation_view.operation_view2.service.InventoryHealthGenService;
import cloud.demand.app.modules.operation_view.operation_view2.service.impl.OperationViewService2Impl.DwsInventoryHealthWeeklyScaleDfAnyDO;
import cloud.demand.app.modules.p2p.ppl13week.dto.dict.InstanceModelAndTypeNameDTO;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplDictServiceImpl;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.StopWatch;

@SpringBootTest
class OperationViewService2ImplTest {

    @Resource
    OperationViewService2Impl operationViewService2;

    @Resource
    InventoryHealthGenService inventoryHealthGenService;

    @Resource
    private DBHelper ckcldDBHelper;


    @Test
    public void test() {

        OperationViewReq2 req = new OperationViewReq2();
        req.setDate(DateUtils.parse("2024-04-23"));

        TimeUtils.printExecutionTime("test", () -> {

            StopWatch stopWatch = new StopWatch();
            stopWatch.start("getInventoryHealthWeeklyScales" + "sql生成");
            String customerCustomGroup = req.getCustomerCustomGroup();
            CustomerCustomGroupEnum groupEnum = CustomerCustomGroupEnum.getByCode(customerCustomGroup);
            if (groupEnum == null) {
                groupEnum = CustomerCustomGroupEnum.ALL;
            }

            String statTime = DateUtils.formatDate(req.getDate());

            WhereSQL condition = req.genCondition();
            condition.and("stat_time = ?", statTime);
            condition.and("week_index < 0 and product_type = 'CVM'");
            condition.and("customer_custom_group = ?", groupEnum.getName());
            if (ListUtils.isNotEmpty(req.getExcludeUinList())) {
                condition.and("exclude_uin_list = ?", req.handleExcludeUinList());
            } else {
                condition.and("exclude_uin_list = '(空值)'");
            }

            WhereSQL categoryCondition = operationViewService2.genCategoryCondition(req.getZoneCategory(),
                    req.getInstanceTypeCategory(),
                    req.getIsCombine(), req.getCategoryDate());
            condition.and(categoryCondition);
            // 聚合分组
            condition.addGroupBy("stat_time", "holiday_week_start_date", "week_index", "holiday_week_end_date",
                    "product_type", "instance_type", "customhouse_title", "area_name", "region_name", "zone_name");
            stopWatch.stop();
            stopWatch.start("数据获取");
            List<DwsInventoryHealthWeeklyScaleDfAnyDO> all = DBList.ckcldDBHelper.getAll(
                    DwsInventoryHealthWeeklyScaleDfAnyDO.class, condition.getSQL(), condition.getParams());
            stopWatch.stop();
            System.out.println(stopWatch.prettyPrint());
            System.out.println(all.size());
        });
    }
    @Test
    public void test2() {
        LocalDate startDate = DateUtils.parseLocalDate("2024-11-30");
        LocalDate endDate = DateUtils.parseLocalDate("2024-11-30");
        InventoryHealthGenServiceImpl bean = SpringUtil.getBean(InventoryHealthGenServiceImpl.class);
        LocalDate cur = startDate;
        while(!cur.isAfter(endDate)) {
            String statTime = DateUtils.formatDate(cur);
            List<DwsApiSuccessDataDfLocalDO> data = bean.getTCApiSuccessData(DateUtils.formatDate(cur));
            Map<String, String> map = ListUtils.toMap(data, o -> o.getInstanceType(),
                    o -> o.getInstanceFamily());
            List<DwsApiSuccessDataDfLocalDO> all = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                    "where stat_time = ?", statTime);
            ckcldDBHelper.executeRaw("ALTER TABLE dwd_api_success_data_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                    statTime);
            for (DwsApiSuccessDataDfLocalDO item : all) {
                if (item.getProductType().equals("TC")) {
                    item.setInstanceFamily(map.get(item.getInstanceType()));
                }
            }
            if (ListUtils.isNotEmpty(all)) {
                ckcldDBHelper.insertBatchWithoutReturnId(all);
            }
            cur = cur.plusDays(1);
        }
    }


    @Test
    public void test3() {
        PplDictServiceImpl bean = SpringUtil.getBean(PplDictServiceImpl.class);
        Map<String, InstanceModelAndTypeNameDTO> stringInstanceModelAndTypeNameDTOMap = bean.queryFullInstanceModelAndName();
        System.out.println(stringInstanceModelAndTypeNameDTOMap);
    }


}