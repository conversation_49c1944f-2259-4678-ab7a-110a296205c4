package cloud.demand.app.modules.operation_view;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.cvmjxc.model.external.BufferAverageCoreDTO;
import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsApiSuccessDataDfLocalDO;
import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsSoldOutDataDfDO;
import cloud.demand.app.modules.operation_view.operation_view2.service.InventoryHealthGenService;
import cloud.demand.app.modules.operation_view.operation_view2.service.impl.InventoryHealthGenServiceImpl;
import cloud.demand.app.modules.operation_view.operation_view2.service.impl.OperationViewService2Impl;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@SpringBootTest
public class InventoryHealthServiceTest {

    @Resource
    private InventoryHealthGenService genService;
    @Resource
    private OperationViewService2Impl operationViewService2;
    @Resource
    private DictService dictService;

    @Test
    public void testGenSafetyInventoryData() {
        genService.genSafetyInventoryDetail("2023-06-13", Lang.list(), Lang.list(), null);
        //  未来预测算法单元测试
//        SpringUtil.getBean(InventoryHealthGenServiceImpl.class).genFuturePartData("2023-05-31", 13);
    }

    @Test
    public void testSnapshotZlkhbData() {
        genService.snapshotHeadZlkhbData("2023-05-31");
    }

    @Test
    public void testBufferScale() {
        List<BufferAverageCoreDTO> buffers = operationViewService2.queryBufferScaleCoreAverage(new Date());
        List<BufferAverageCoreDTO> filter =
                ListUtils.filter(buffers,
                        o -> Objects.equals(o.getInstanceType(), "S5") && Objects.equals(o.getZoneName(), "上海五区"));
        System.out.println(filter);


    }

    @Test
    public void testSoldOutData() {
        String startDate = "2024-07-10";
        long start = DateUtils.parse(startDate).getTime() / 1000;
        genService.genServiceLevelSoldData(String.valueOf(start), startDate);
        genService.genServiceLevelApiSuccessData(String.valueOf(start), startDate);
    }

    @Test
    public void testEsData() {
        InventoryHealthGenServiceImpl impl = SpringUtil.getBean(InventoryHealthGenServiceImpl.class);
        String statTime = "2024-07-10";
        List<DwsSoldOutDataDfDO> esSoldOutData = impl.getESSoldOutData(statTime);
        System.out.println(esSoldOutData);
        List<DwsApiSuccessDataDfLocalDO> esApiSuccessData = impl.getESApiSuccessData(statTime);
        System.out.println(esApiSuccessData);
    }

    @Test
    public void testTCHouseData() {
        InventoryHealthGenServiceImpl impl = SpringUtil.getBean(InventoryHealthGenServiceImpl.class);
        impl.getTCHouseCustomerData("2024-12-25");
    }


}
