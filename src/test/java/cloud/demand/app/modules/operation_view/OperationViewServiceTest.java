package cloud.demand.app.modules.operation_view;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.operation_view.inventory_health.dto.CommonQueryConditionDTO;
import cloud.demand.app.modules.operation_view.inventory_health.dto.HolidayWeekDTO;
import cloud.demand.app.modules.operation_view.inventory_health.dto.SafetyInventoryDTO;
import cloud.demand.app.modules.operation_view.inventory_health.service.ForecastViewService;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.app.modules.operation_view.inventory_health.service.impl.ForecastViewServiceImpl;
import cloud.demand.app.modules.operation_view.operation_view2.model.SafetyInvThresholdReq;
import cloud.demand.app.modules.operation_view.operation_view2.service.ThresholdTransferService;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.OperationViewService;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.OperationViewGenDataService;
import cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.cvm.GenCvmDetailService;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@SpringBootTest
public class OperationViewServiceTest {

    @Resource
    OperationViewGenDataService genAllDetailData;
    @Resource
    GenCvmDetailService genCvmDetailService;
    @Resource
    ForecastViewService forecastViewService;
    @Resource
    OperationViewService operationViewService;
    @Resource
    InventoryHealthDictService dictService;

    @Test
    public void test1(){
        genAllDetailData.genAllDetailData("2023-05-29");
    }

    @Test
    public void test2(){
        List<HolidayWeekDTO> futureHolidayWeekInfo =
                SpringUtil.getBean(ForecastViewServiceImpl.class).getFutureHolidayWeekInfo(13);

        //  获取当周周一及第13周的周日，框定数据筛选范围
        String beginDate = DateUtils.formatDate(DateUtils.parse(DateUtils.formatDate(LocalDate.now().with(DayOfWeek.MONDAY))));
        HolidayWeekDTO lastWeek = futureHolidayWeekInfo.get(futureHolidayWeekInfo.size() - 1);
        Date lastSunday = DateUtils.addTime(DateUtils.parse(lastWeek.getDate()), Calendar.DATE, 6);
        String endDate = DateUtils.formatDate(lastSunday);

        System.out.println(beginDate);
        System.out.println(endDate);
    }

    @Test
    public void testQueryFutureSafetyInventory() {
        List<Date> dates = new ArrayList<>();
        dates.add(DateUtils.parse("2023-04-03"));
        dates.add(DateUtils.parse("2023-04-10"));
        dates.add(DateUtils.parse("2023-04-17"));
        dates.add(DateUtils.parse("2023-04-24"));

        CommonQueryConditionDTO condition = new CommonQueryConditionDTO();

        List<SafetyInventoryDTO> list = forecastViewService.queryFutureSafetyInventory(dates, condition);
        System.out.println(list);
    }

    @Test
    public void testGenAll(){
        long startTime = System.currentTimeMillis();
//        forecastViewService.genForecastHolidayWeekData();
        forecastViewService.genPurchaseFutureData();
//        forecastViewService.genFutureForecastData();
        long endTime = System.currentTimeMillis();
        System.out.println("总耗时：" + (endTime - startTime) + "ms");
        System.out.println("done");
    }

    @Test
    public void testSla(){
        System.out.println(operationViewService.getSLAByInstanceType("MA2", true));
    }

    @Test
    public void testCombination(){
        System.out.println(dictService.queryAllCombineInstanceType());

    }

    @Test
    public void testForecastGen(){
        forecastViewService.genForecastHolidayWeekData();
    }


    @Resource
    ThresholdTransferService thresholdTransferService;
    @Test
    public void testTransferThreshold(){
        SafetyInvThresholdReq req = new SafetyInvThresholdReq();
        List<SafetyInvThresholdReq.Item> data = Lang.list();
        req.setData(data);
        data.add(new SafetyInvThresholdReq.Item().setZoneName("北京六区").setInstanceType("S5").setThresholdValue(0));
        data.add(new SafetyInvThresholdReq.Item().setZoneName("上海五区").setInstanceType("S5").setThresholdValue(0));
        thresholdTransferService.setSafetyInvThreshold(req, "pascalzhli");
    }
}
