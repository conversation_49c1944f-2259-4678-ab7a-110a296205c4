package cloud.demand.app.modules.order;

import cloud.demand.app.modules.order.dto.req.CycleElasticPerformanceTrendReq;
import cloud.demand.app.modules.order.dto.req.GlobalApprovalReq;
import cloud.demand.app.modules.order.dto.req.OrderAutoPreDeductReq;
import cloud.demand.app.modules.order.dto.req.OrderItemPlacementGroupModifyReq;
import cloud.demand.app.modules.order.dto.req.OrderQueryReq;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq.OrderSaveItemDTO;
import cloud.demand.app.modules.order.dto.req.OrderSupplyApiReq;
import cloud.demand.app.modules.order.dto.req.OrderUrgentDelayReq;
import cloud.demand.app.modules.order.dto.req.OrderUrgentDelayReq.UrgentDelayItem;
import cloud.demand.app.modules.order.dto.req.PreDeductDelayReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderElasticType;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderSourceEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.enums.OrderTypeEnum;
import cloud.demand.app.modules.order.enums.PplProductToOrderProductMapEnum;
import cloud.demand.app.modules.order.job.OrderCommonTask;
import cloud.demand.app.modules.order.service.OrderApiService;
import cloud.demand.app.modules.order.service.OrderCommonOperateService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderOperateService;
import cloud.demand.app.modules.order.service.OrderTagService;
import cloud.demand.app.modules.order.service.PerformanceTrackService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplAppliedSupplylDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDataBaseFrameworkEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDeloyTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupInfoVO;
import cloud.demand.app.web.model.common.Page;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import yunti.boot.security.TofUser;

@SpringBootTest
@Slf4j
public class OrderOperateTest {

    @Resource
    private OrderOperateService orderOperateService;

    @Resource
    private OrderCommonOperateService orderCommonOperateService;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private OrderCommonTask orderCommonTask;

    @Resource
    private PreDeductOrderService preDeductOrderService;

    @Resource
    private OrderApiService orderApiService;

    @Resource
    private PerformanceTrackService performanceTrackService;

    @Resource
    private OrderTagService orderTagService;

    private OrderSaveReq createSaveReq() {
        OrderSaveReq req = new OrderSaveReq();
        req.setOrderNumber(null);
        req.setWarZone("出海");
        req.setSubmitUser("dotyou");
        req.setProjectType("项目类型1");
        req.setProduct("CVM&CBS");
        req.setOrderType("ELASTIC");
        req.setElasticType(OrderElasticType.BY_MONTH.getTypeName());
        req.setIndustryDept("智慧行业一部");
        req.setEndElasticDate(null);
        req.setEndBuyDate(LocalDate.now().plusDays(44));
        req.setCustomerShortName("游修良");
        req.setDemandDetail("需求详情xxxx---");
        req.setCustomerUin("12345678");
        req.setCustomerName("游修良");
        req.setBeginElasticDate(null);
        req.setBeginBuyDate(LocalDate.now().plusDays(30));
        req.setArchitect("dotyou");
        req.setAppRole("CVM");
        req.setAppId("9876543");

        List<OrderSaveItemDTO> items = new ArrayList<>();
        req.setOrderItems(items);

        OrderSaveItemDTO item_1 = createItem();
//        item_1.setOrderNumberId("OE2405020001-001");
        item_1.setZoneName("长沙十区");
        items.add(item_1);

        OrderSaveItemDTO item_2 = createItem();
//        item_2.setOrderNumberId("OE2405020001-002");
        item_2.setZoneName("广州十区");
        items.add(item_2);
        return req;
    }

    private OrderSaveItemDTO createItem() {
        OrderSaveItemDTO item = new OrderSaveItemDTO();
        item.setBillType("包年包月");
        item.setZoneName("深圳一区");
        item.setTotalDisk(null);
        item.setTotalCore(100);
        item.setSystemDiskType(null);
        item.setSystemDiskStorage(null);
        item.setSystemDiskNum(null);
//        item.setPlacementGroup("");
//        item.setOtherZoneName(null);
//        item.setAlternativeInstanceType(null);
//        item.setOtherInstanceModel(null);
        item.setOrderNumberId(null);
        item.setOrderNumber(null);
        item.setInstanceType("S5");
        item.setInstanceNum(10);
        item.setInstanceModel("S5.xxxxxx");
        item.setDataDiskType(null);
        item.setDataDiskNum(null);
        item.setDataDiskStorage(null);
        return item;
    }

    @Test
    public void saveNewOrderDraft() {
        OrderSaveReq req = createDatabaseDraft();
        orderOperateService.saveNewOrderDraft(req);
    }

    private OrderSaveReq createDatabaseDraft() {
        OrderSaveReq req = new OrderSaveReq();
        req.setSubmitUser("dotyou");
        req.setProjectType("项目类型1");
        req.setOrderCategory(PplProductToOrderProductMapEnum.DATABASE.getOrderCategory());
        req.setProduct(Ppl13weekProductTypeEnum.DATABASE.getName());
        req.setAppRole(PplProductToOrderProductMapEnum.DATABASE.getAppRole());
        req.setOrderType(OrderTypeEnum.NEW.getCode());
        req.setIndustryDept("智慧行业二部");
        req.setWarZone("基础教育行业中心");
        req.setCustomerName("上海长宁区乐读线上教育培训学校");
        req.setCustomerShortName("乐读-好未来K9");
        req.setCustomerUin("100027202401");
        req.setAppId("1313601664");
        req.setBeginBuyDate(LocalDate.now().plusDays(30));
        req.setEndBuyDate(LocalDate.now().plusDays(44));
        req.setDemandDetail("需求详情xxxx---");
        req.setDemandScene("winback项目");
        req.setArchitect("oliverychen");

        OrderSaveItemDTO item = new OrderSaveItemDTO();
        item.setDatabaseName(PplDatabaseEnum.MySQL.getName());
        item.setRegion("ap-beijing");
        item.setRegionName("华北地区(北京)");
        item.setZone("ap-beijing-7");
        item.setZoneName("北京七区");
        item.setMoreThanOneAZ(false);
        item.setFrameworkType(PplDataBaseFrameworkEnum.CDB_THREE.getName());
        item.setDatabaseStorageType(PplDatabaseStorageEnum.CLOUD.getName());
        item.setDeployType(PplDeloyTypeEnum.UNIVERSAL.getName());
        req.setOrderItems(ListUtils.newArrayList(item));

        return req;
    }

    @Test
    public void modifyOrderDraft() {
        OrderSaveReq req = createSaveReq();
        req.setOrderNumber("OE2405020001");
        orderOperateService.modifyOrderDraft(req);
    }

    @Test
    public void submitDraft() {
        orderOperateService.submitDraft("OE2406030003", null);
    }

    @Test
    public void saveAndSubmitDraft() {
        OrderSaveReq req = createSaveReq();
        req.setOrderNumber(null);
        orderOperateService.saveAndSubmitDraft(req);
    }

    @Test
    public void approvalDemandInMainFlow() {
        GlobalApprovalReq req = new GlobalApprovalReq();
        req.setBizId("OE2406030003");
        req.setOrderNumber("OE2406030003");
        req.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        req.setApprovalResult(false);
        req.setApprovalResultName(null);
        req.setOrderLevelName("高优先级");
        req.setApprovalRemark("第一次审批，审批通过");
        req.setCurrentNodeCode(OrderNodeCodeEnum.node_order_approval.getCode());
        orderOperateService.approvalDemandInMainFlow(req);
    }


    @Test
    public void queryOrderOperateUser() {
        OrderInfoDO one = demandDBHelper.getOne(OrderInfoDO.class);
        orderCommonService.queryOrderOperateUser(IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE.getCode(), one);
    }

    @Test
    public void getPermissionByUserAndRole() {
        UserPermissionDto rogerslin = permissionService.getPermissionByUserAndRole(
                IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode()
                , "rogerslin");
    }

    private OrderSaveReq fromOrder(OrderDetailResp order) {
        OrderSaveReq req = new OrderSaveReq();
        req.setOrderCategory(order.getOrderCategory());
        req.setOrderType(order.getOrderType());
        req.setProduct(order.getProduct());
        req.setAppId(order.getAppId());
        req.setAppRole(order.getAppRole());
        req.setArchitect(order.getArchitect());
        req.setBeginBuyDate(order.getBeginBuyDate());
        req.setBeginElasticDate(order.getBeginElasticDate());
        req.setCustomerName(order.getCustomerName());
        req.setCustomerUin(order.getCustomerUin());
        req.setDemandDetail(order.getDemandDetail());
        req.setCustomerShortName(order.getCustomerShortName());
        req.setEndBuyDate(order.getEndBuyDate());
        req.setEndElasticDate(order.getEndElasticDate());
        req.setIndustryDept(order.getIndustryDept());
        req.setProjectType(order.getProjectType());
        req.setSubmitUser(order.getSubmitUser());
        req.setWarZone(order.getWarZone());
        req.setOrderNumber(order.getOrderNumber());
        req.setProjectName(order.getProjectName());

        List<OrderSaveItemDTO> details = new ArrayList<>();
        for (OrderItemDTO dto : order.getItemList()) {
            OrderItemDTO.buildDTO(dto);
            OrderSaveItemDTO item = new OrderSaveItemDTO();
            item.setOrderNumberId(dto.getOrderNumberId());
            item.setOrderNumber(dto.getOrderNumber());
            item.setInstanceType(dto.getInstanceType());
            item.setInstanceNum(dto.getInstanceNum());
            item.setInstanceModel(dto.getInstanceModel());
            item.setBillType(dto.getBillType());
            item.setDataDiskStorage(dto.getDataDiskStorage());
            item.setDataDiskType(dto.getDataDiskType());
            item.setDataDiskNum(dto.getDataDiskNum());
            item.setProduct(dto.getProduct());
            item.setSystemDiskNum(dto.getSystemDiskNum());
            item.setSystemDiskStorage(dto.getSystemDiskStorage());
            item.setSystemDiskType(dto.getSystemDiskType());
            item.setTotalCore(dto.getTotalCore());
            item.setZoneName(dto.getZoneName());
            item.setTotalDisk(dto.getTotalDisk());
            item.setAcceptGpu(dto.getAcceptGpu());
            item.setGpuType(dto.getGpuType());
            item.setOtherInstanceList(dto.getOtherInstanceList());
            item.setOtherZoneList(dto.getOtherZoneList());
            item.setPlacementGroupList(dto.getPlacementGroupList());
            item.setRegionName(dto.getRegionName());
            item.setTotalGpuNum(dto.getTotalGpuNum());
            details.add(item);
        }
        req.setOrderItems(details);
        return req;
    }

    @Test
    public void modifyOrder() {
        String orderNumber = "OE2405130006";
        // 主流程生效的订单信息
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        OrderSaveReq req = fromOrder(order);
        req.setDemandDetail("这是发起订单修改子流程之后的需求详情");
        req.setModifyRemark("测试修改订单子流程");
        req.getOrderItems().get(0).setZoneName("北京十区");
        orderOperateService.modifyOrder(req);
    }

    @Test
    public void modifyOrderApproval() {
        GlobalApprovalReq req = new GlobalApprovalReq();
        req.setBizId("OE2405130006");
        req.setOrderNumber("OE2405130006");
        req.setFlowCode(OrderFlowEnum.ORDER_UPDATE_DEMAND.getCode());
        req.setApprovalResult(true);
        req.setApprovalResultName(null);
        req.setApprovalRemark("同意此次修改订单");
        req.setCurrentNodeCode(OrderNodeCodeEnum.node_cloud_manage_approval.getCode());
        orderOperateService.modifyOrderApproval(req);
    }

    @Test
    public void urgentDelay() {
        OrderUrgentDelayReq req = new OrderUrgentDelayReq();
        req.setOrderNumber("OE2506030001");
        req.setReason("修改一下购买日期");
        List<UrgentDelayItem> items = new ArrayList<>();
        UrgentDelayItem item = new UrgentDelayItem();
        item.setZoneName("上海五区");
        item.setDeployType("通用型");
        item.setFrameworkType("CDB-三节点");
        item.setDatabaseStorageType("云盘版");
        item.setBeforeBeginBuyDate("2025-06-25");
        item.setBeforeEndBuyDate("2025-06-30");
        item.setBeginBuyDate("2025-09-11");
        item.setEndBuyDate("2025-09-21");
        items.add(item);
        req.setItems(items);
        orderOperateService.urgentDelay(req);
    }

    @Test
    public void urgentDelayApproval() throws InterruptedException {
        GlobalApprovalReq req = new GlobalApprovalReq();
        req.setBizId("OE2506030001");
        req.setOrderNumber("OE2506030001");
        req.setFlowCode(OrderFlowEnum.ORDER_URGENT_DELAY.getCode());
        req.setApprovalResult(true);
        req.setApprovalResultName(null);
        req.setApprovalRemark("同意此次加急延期");
        req.setCurrentNodeCode(OrderNodeCodeEnum.node_cloud_manage_approval.getCode());
        orderOperateService.modifyOrderApproval(req);
        Thread.sleep(1000 * 60);
    }

    @Test
    public void urgentDelayFlowInfo() {
        Object res = orderCommonService.urgentDelayFlowInfo("OE2405130006", "ED2-OE2405130006");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void orderModifyFlowInfo() {
        Object res = orderCommonService.orderModifyFlowInfo("OE2405130006", "M2-OE2405130006");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void deleteDraft() {
        orderCommonOperateService.deleteDraft("OE2405130009");
    }

    @Test
    public void pushToOrderClose() {
        orderCommonTask.pushToOrderClose();
    }

    @Test
    public void queryOneLog() {
        Object res = orderCommonService.queryOneLog(993L);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void updateOrderRiskLevelBySupplyPlan() {
        orderCommonOperateService.updateOrderRiskLevelBySupplyPlan("OE2506180002");
    }

    @Test
    public void hasDifference() {
        OrderItemDO item_1 = demandDBHelper.getByKey(OrderItemDO.class, 3149);
        OrderItemDO item_2 = demandDBHelper.getByKey(OrderItemDO.class, 3150);
        Object res = item_1.hasDifference(item_2);
        System.out.println(res);
    }

    @Test
    public void preDeductDelay() {
        PreDeductDelayReq req = new PreDeductDelayReq();
        req.setPreDeductItemIds(ListUtils.newArrayList(469L));
        req.setEndPreDeductDate(LocalDate.of(2025, 7, 8));
        req.setOrderNumber("OE2506150001");
        req.setRemark("测试预扣续期");
        preDeductOrderService.preDeductDelay(req);
    }

    @Test
    public void preDeductDelayForOrderDelay() {
        preDeductOrderService.preDeductDelayForOrderDelay("OE2407040005", 646L);
    }

    @Test
    public void autoPreDeductDelay() {
        preDeductOrderService.autoPreDeductDelay(LocalDate.now().minusDays(30));
    }

    @Test
    public void modifyAutoPreDeduct() {
        OrderAutoPreDeductReq req = new OrderAutoPreDeductReq();
        req.setAutoPreDeduct(false);
        req.setOrderNumber("OE2407110028");
        orderOperateService.modifyAutoPreDeduct(req);
    }

    @Test
    public void preDeductDeadlineNotice() {
        preDeductOrderService.preDeductDeadlineNotice(33);
    }

    @Test
    public void subscribe() {
        orderCommonOperateService.subscribe("OE2407040005");
    }

    @Test
    public void cancelSubscribe() {
        orderCommonOperateService.cancelSubscribe("OE2407040005");
    }

    @Test
    public void modifyPlacementGroup() {
        OrderItemPlacementGroupModifyReq req = new OrderItemPlacementGroupModifyReq();
        req.setOrderNumber("OE2407150001");
        req.setOrderNumberId("OE2407150001-002");
        req.setPlacementGroupList(ListUtils.newArrayList("ps-83749011", "ps-83749000"));
        orderOperateService.modifyPlacementGroup(req);
    }

    @Test
    public void testQuery() {
        OrderQueryReq req = new OrderQueryReq();
        req.setOrderNodeCode(Arrays.asList(OrderNodeCodeEnum.node_order_supply.getCode()
                , OrderNodeCodeEnum.node_order_following.getCode(), OrderNodeCodeEnum.node_order_close.getCode()));
        req.setOrderStatus(Arrays.asList(OrderStatusEnum.PROCESS.getCode(), OrderStatusEnum.FINISHED.getCode()));
        req.setOrderSource(OrderSourceEnum.PPL_TRANSFORM.getCode());
        List<OrderDetailResp> orderDetailRspList = orderCommonService.queryOrder(req);
        Map<String, OrderItemDTO> orderMap = orderDetailRspList.stream().flatMap(vo -> vo.getItemList().stream())
                .collect(Collectors.toMap(OrderItemDTO::getSourcePplId, v -> v));

        PplVersionGroupInfoVO one = demandDBHelper.getOne(PplVersionGroupInfoVO.class,
                "where id = ?", 14629L);
        Map<String, PplAppliedSupplylDO> pplIdToLockSupplyDO = new HashMap<>();

        updateLockResourceNum(one, pplIdToLockSupplyDO, orderMap);

        System.out.println(1);
    }

    private void updateLockResourceNum(PplVersionGroupInfoVO groupInfoVO,
            Map<String, PplAppliedSupplylDO> pplIdToLockSupplyDO, Map<String, OrderItemDTO> pplIdToOrderMap) {
        if (groupInfoVO.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())
                || groupInfoVO.getProduct().equals(Ppl13weekProductTypeEnum.BM.getName())) {
            return;
        }
        if (pplIdToLockSupplyDO.size() == 0 && pplIdToOrderMap.size() == 0) {
            return;
        }

        List<PplVersionGroupRecordItemDO> all = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                "where version_group_record_id = ?", groupInfoVO.getLastRecordId());
        List<PplVersionGroupRecordItemDO> updateList = new ArrayList<>();
        for (PplVersionGroupRecordItemDO pplVersionGroupRecordItemDO : all) {
            OrderItemDTO orderItemDTO = pplIdToOrderMap.get(pplVersionGroupRecordItemDO.getPplId());
            if (orderItemDTO != null) {
                // 更新已锁定资源量
                pplVersionGroupRecordItemDO.setIsLock(Boolean.TRUE);
                updateList.add(pplVersionGroupRecordItemDO);
                continue;
            }

            PplAppliedSupplylDO pplAppliedSupplylDO = pplIdToLockSupplyDO.get(
                    pplVersionGroupRecordItemDO.getPplId());
            if (pplAppliedSupplylDO != null) {
                // 更新已锁定资源量
                pplVersionGroupRecordItemDO.setIsLock(Boolean.TRUE);
                updateList.add(pplVersionGroupRecordItemDO);
            }

        }
        demandDBHelper.update(updateList);
    }

    @Test
    public void queryForApi() {
        TofUser user = new TofUser("ppl", "deptno_1183", "deptno_1183",
                ListUtils.newArrayList(new SimpleGrantedAuthority("no")));
        OrderQueryReq req = new OrderQueryReq();
        req.setBeginBuyDate("2024-07-01");
        req.setEndBuyDate("2024-07-31");
        Object res = orderApiService.queryOrderDetailList(req, user);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryOrderSupplyDetailListForApi() {
        TofUser user = new TofUser("ppl", "deptno_1183", "deptno_1183",
                ListUtils.newArrayList(new SimpleGrantedAuthority("no")));
        OrderSupplyApiReq req = new OrderSupplyApiReq();
        Page page = new Page();
        page.setSize(10);
        page.setStart(2);
        req.setPage(page);
        Object res = orderApiService.queryOrderSupplyDetailList(req, user);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void refreshConsensusDemandFollow() {
        performanceTrackService.refreshConsensusDemandFollow(LocalDate.of(2024, 6, 1));
    }

    @Test
    public void modifyOrderShareUin() {
        List<String> list = ListUtils.newArrayList("100007253163", "100024434558");
        orderOperateService.modifyOrderShareUin("OE2503030001", list);
    }

    @Test
    public void shareUinList() {
        Object res = preDeductOrderService.shareUinList("2167432492", "智慧行业一部");
        System.out.println(JSON.toJson(res));
    }

//    @Test
//    public void modifyOrderSubmitAndArchitect() {
//        List<String> list = ListUtils.newArrayList("ON2501220008");
//        orderCommonOperateService.modifyOrderSubmitAndArchitect(list, "dotyou", "oliverychen");
//    }

    @Test
    public void refreshCycleElasticConsensusDemandFollowByOrders() throws InterruptedException {
        List<String> orderNumberList = ListUtils.newArrayList("OE2405280002");
        performanceTrackService.refreshCycleElasticConsensusDemandFollowByOrders(orderNumberList);
        Thread.sleep(1000 * 60);
    }

    @Test
    public void queryCycleElasticPerformance() {
        Object res = performanceTrackService.queryCycleElasticPerformance("OE2504020002");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryCycleElasticPerformanceTrend() {
        CycleElasticPerformanceTrendReq req = new CycleElasticPerformanceTrendReq();
        req.setOrderNumber("OE2504020002");
        Object res = performanceTrackService.queryCycleElasticPerformanceTrend(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void genFullConsensusDemandByOrderNumber() {
        orderCommonOperateService.genFullConsensusDemandByOrderNumber("OE2505300001");
    }

    @Test
    public void tagConsensusOrder() {
        orderTagService.tagConsensusOrder("OE2506230006");
    }
}
