package cloud.demand.app.modules.order;

import cloud.demand.app.modules.flow.entity.FlowNodeRecordDO;
import cloud.demand.app.modules.flow.service.FlowService;
import cloud.demand.app.modules.order.dto.req.OrderFlowNodeValueSetReq;
import cloud.demand.app.modules.order.dto.req.OrderFlowStartReq;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.service.OrderFlowService;
import com.pugwoo.dbhelper.DBHelper;
import java.time.LocalDate;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class OrderFlowTest {

    @Resource
    private OrderFlowService orderFlowService;

    @Resource
    private FlowService flowService;

    @Resource
    private DBHelper demandDBHelper;

    private final String user_dot = "dotyou";

    @Test
    public void starMainFlow() {
        OrderFlowStartReq req = new OrderFlowStartReq();
        req.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        req.setFlowRemark("订单主流程测试");
        req.setFlowInitiator(user_dot);

        OrderInfoDO order = createOrder();
        req.setOrder(order);
        req.setFlowNo(order.getOrderNumber());
        req.setBizId(order.getOrderNumber());

        orderFlowService.startMainFlow(req);

        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("用户保存草稿");
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(order.getOrderNumber(),
                OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData.getNodeCode());
        valueSetReq.setBizId(order.getOrderNumber());
        valueSetReq.setNodeReturn(1);
        valueSetReq.setOperateUser(user_dot);
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);
    }

    private OrderInfoDO createOrder() {
        OrderInfoDO order = new OrderInfoDO();
        order.setAvailableStatus(OrderAvailableStatusEnum.AVAILABLE.getCode());
        order.setOrderNumber("OE2404240027");
        order.setOrderStatus(OrderStatusEnum.DRAFT.getCode());
        order.setOrderType("普通");
        order.setAppId("1111111111");
        order.setAppRole("CVM");
        order.setArchitect(user_dot);
        order.setBeginBuyDate(LocalDate.now().plusDays(30));
        order.setEndBuyDate(LocalDate.now().plusDays(44));
        order.setCustomerName("xxx");
        order.setCustomerShortName("x");
        order.setCustomerUin("1234567");
        order.setIndustryDept("智慧行业一部");
        order.setProduct("CVM");
        order.setSubmitUser(user_dot);
        order.setWarZone("游戏");
        order.setDemandDetail("第一次测试订单流程");
//        demandDBHelper.insert(order);
        return order;
    }

}
