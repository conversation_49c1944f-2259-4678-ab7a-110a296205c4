package cloud.demand.app.modules.order;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.order.dto.PreDeductGridSimpleDTO;
import cloud.demand.app.modules.order.dto.SupplyPlanMatchWayDTO;
import cloud.demand.app.modules.order.dto.SupplyPlanMatchWayDTO.MatchDetail;
import cloud.demand.app.modules.order.dto.req.BackupServiceLevelDetailReq;
import cloud.demand.app.modules.order.dto.req.ConsensusReq;
import cloud.demand.app.modules.order.dto.req.ConsensusReq.ConsensusItem;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq.PreDeductItemDTO;
import cloud.demand.app.modules.order.dto.req.GlobalApprovalReq;
import cloud.demand.app.modules.order.dto.req.QueryRoleUsersForOrderReq;
import cloud.demand.app.modules.order.dto.req.ServiceLevelQueryReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanDetailMatchCoreUpdateReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanSaveReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemWithSupplyDetail;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteBuyDTO;
import cloud.demand.app.modules.order.dto.resp.supply.process.MoveResp;
import cloud.demand.app.modules.order.dto.resp.supply.process.QuotaSupplyDetailResp;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanVersionDO;
import cloud.demand.app.modules.order.entity.PreDeductOrderItemDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderSupplyConsensusStatusEnum;
import cloud.demand.app.modules.order.enums.OrderSupplyPlanMatchTypeEnum;
import cloud.demand.app.modules.order.enums.PreDeductOrderStatusEnum;
import cloud.demand.app.modules.order.service.OrderCommonOperateService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderOperateService;
import cloud.demand.app.modules.order.service.OrderSatisfyRateService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.order.service.SupplyPlanOperateService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.dto.ppl_order_adjust.PplOrderAdjustDetailReq;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplOrderAdjustService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.thymeleaf.TemplateEngine;
import yunti.boot.client.JsonrpcClient;

@SpringBootTest
@Slf4j
public class SupplyPlanOperateTest {

    @Resource
    private SupplyPlanOperateService supplyPlanOperateService;

    @Resource
    private OrderOperateService orderOperateService;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private OrderCommonOperateService orderCommonOperateService;

    @Resource
    private JsonrpcClient jsonrpcClient;

    @Resource
    private PreDeductOrderService preDeductOrderService;

    @Resource
    private OrderSatisfyRateService orderSatisfyRateService;

    @Resource
    private TemplateEngine templateEngine;

    @Resource
    private Alert alert;

    @Resource
    private PplVersionService pplVersionService;

    @Resource
    private PplOrderAdjustService pplOrderAdjustService;

    @Test
    public void saveSupplyPlan() {
        SupplyPlanSaveReq req = new SupplyPlanSaveReq();
        req.setOrderNumber("OE2405100002");

        SupplyPlanMatchWayDTO way_1 = new SupplyPlanMatchWayDTO();
        way_1.setProduct("CVM&CBS");
        way_1.setOrderNumber("OE2405100002");
        way_1.setRemark("正常大盘满足1");
        way_1.setMatchType(OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());
        MatchDetail detail_1_1 = new MatchDetail();
        detail_1_1.setOrderNumberId("OE2405100002-001");
        detail_1_1.setSupplyRemark("方案明细备注xxx");
        detail_1_1.setConsensusBeginBuyDate(LocalDate.now().plusDays(10));
        detail_1_1.setSupplyBizId("66xxxxxxxxx");
        detail_1_1.setSupplyZoneName("深圳一区");
        detail_1_1.setSupplyInstanceModel("S5.4XLARGE32");
        detail_1_1.setSupplyCoreNum(160);
        detail_1_1.setSupplyRegionName("深圳");
        detail_1_1.setSupplyInstanceType("S5");
        detail_1_1.setSupplyInstanceNum(10);
        way_1.setDetails(ListUtils.newArrayList(detail_1_1));

        req.setWays(ListUtils.newArrayList(way_1));

        supplyPlanOperateService.saveSupplyPlan(req);
    }

    @Test
    public void submitSupplyPlan() {
        SupplyPlanSaveReq req = new SupplyPlanSaveReq();
        req.setOrderNumber("OE2405110003");
        supplyPlanOperateService.submitSupplyPlan(req);
    }


    @Test
    public void checkIsAllowSubmit() {
        SupplyPlanSaveReq req = new SupplyPlanSaveReq();
        req.setOrderNumber("OE2504290014");
        supplyPlanOperateService.checkIsAllowSubmit(req,null);
    }


    /**
     * 到达满足方式评估节点时，调用此方，使主流程从满足方式评估到交付供应（这里不会进行共识，内部生成的是无需共识的供应方案）
     */
    @Test
    public void supplyPlanFlowExec() {
        // 改这一个 orderNumber 就ok 了
        String orderNumber = "OE2405160007";

        // 供应方案第一步，CBS审批
        GlobalApprovalReq req = new GlobalApprovalReq();
        req.setBizId(orderNumber);
        req.setOrderNumber(orderNumber);
        req.setFlowCode(OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE.getCode());
        req.setApprovalResult(true);
        req.setApprovalResultName(null);
        req.setApprovalRemark("CBS审批通过");
        req.setCurrentNodeCode(OrderNodeCodeEnum.node_cbs_approval.getCode());
        supplyPlanOperateService.cbsApproval(req);

        // 第二步，生成无需共识的供应方案，保存提交，满足方式评估完成，到达交付供应
        OrderDetailResp order = orderCommonService
                .getOrderDetail(orderNumber, OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
        SupplyPlanSaveReq saveReq = new SupplyPlanSaveReq();
        saveReq.setOrderNumber(orderNumber);

        SupplyPlanMatchWayDTO way_1 = new SupplyPlanMatchWayDTO();
        way_1.setProduct(order.getProduct());
        way_1.setOrderNumber(orderNumber);
        way_1.setRemark("正常大盘满足");
        way_1.setMatchType(OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());

        List<MatchDetail> details = new ArrayList<>();
        for (OrderItemDO itemDO : order.getItemList()) {
            // 根据订单明细生成无需共识的供应方案明细
            MatchDetail detail_1_1 = new MatchDetail();
            detail_1_1.setOrderNumberId(itemDO.getOrderNumberId());
            detail_1_1.setSupplyRemark("方案明细备注xxx");
            detail_1_1.setConsensusBeginBuyDate(LocalDate.now().plusDays(10));
            detail_1_1.setSupplyBizId("66xxxxxxxxx");
            detail_1_1.setSupplyZoneName(itemDO.getZoneName());
            detail_1_1.setSupplyInstanceModel(itemDO.getInstanceModel());
            detail_1_1.setSupplyCoreNum(itemDO.getTotalCore());
            detail_1_1.setSupplyRegionName(itemDO.getRegionName());
            detail_1_1.setSupplyInstanceType(itemDO.getInstanceType());
            detail_1_1.setSupplyInstanceNum(itemDO.getInstanceNum());
            detail_1_1.setSupplyGpuType(itemDO.getGpuType());
            detail_1_1.setSupplyGpuNum(itemDO.getTotalGpuNum());
            details.add(detail_1_1);
        }
        way_1.setDetails(details);
        saveReq.setWays(ListUtils.newArrayList(way_1));
        // 保存提交供应方案
        supplyPlanOperateService.submitSupplyPlan(saveReq);
    }

    private SupplyPlanSaveReq createNeedConsensusPlan(String orderNumber) {
        OrderDetailResp order = orderCommonService
                .getOrderDetail(orderNumber, OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
        SupplyPlanSaveReq saveReq = new SupplyPlanSaveReq();
        saveReq.setOrderNumber(orderNumber);

        SupplyPlanMatchWayDTO way_1 = new SupplyPlanMatchWayDTO();
        way_1.setProduct(order.getProduct());
        way_1.setOrderNumber(orderNumber);
        way_1.setRemark("正常大盘满足");
        way_1.setMatchType(OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());

        List<MatchDetail> details = new ArrayList<>();
        for (OrderItemDO itemDO : order.getItemList()) {
            int notConsensusNum = itemDO.getInstanceNum() % 2 == 0
                    ? itemDO.getInstanceNum() / 2
                    : ((itemDO.getInstanceNum() / 2) + 1);
            int needConsensusNum = itemDO.getInstanceNum() - notConsensusNum;
            int oneInstanceCore = itemDO.getTotalCore() == null ? 0
                    : (itemDO.getTotalCore() / itemDO.getInstanceNum());
            int oneInstanceGpu = itemDO.getTotalGpuNum() == null ? 0
                    : (itemDO.getTotalGpuNum() / itemDO.getInstanceNum());

            // 根据订单明细生成无需共识的供应方案明细
            MatchDetail detail_1_1 = new MatchDetail();
            detail_1_1.setOrderNumberId(itemDO.getOrderNumberId());
            detail_1_1.setSupplyRemark("方案明细备注xxx");
            detail_1_1.setConsensusBeginBuyDate(LocalDate.now().plusDays(10));
            detail_1_1.setSupplyBizId("66xxxxxxxxx");
            detail_1_1.setSupplyZoneName(itemDO.getZoneName());
            detail_1_1.setSupplyInstanceModel(itemDO.getInstanceModel());
            detail_1_1.setSupplyCoreNum(notConsensusNum * oneInstanceCore);
            detail_1_1.setSupplyRegionName(itemDO.getRegionName());
            detail_1_1.setSupplyInstanceType(itemDO.getInstanceType());
            detail_1_1.setSupplyInstanceNum(notConsensusNum);
            detail_1_1.setSupplyGpuType(itemDO.getGpuType());
            detail_1_1.setSupplyGpuNum(notConsensusNum * oneInstanceGpu);
            details.add(detail_1_1);

            // 根据订单明细生成需要共识的供应方案明细
            MatchDetail detail_1_2 = new MatchDetail();
            detail_1_2.setOrderNumberId(itemDO.getOrderNumberId());
            detail_1_2.setSupplyRemark("方案明细备注xxx-待共识");
            detail_1_2.setConsensusBeginBuyDate(LocalDate.now().plusDays(12));
            detail_1_2.setSupplyBizId("biz-id-xxxxxxxxx");
            detail_1_2.setSupplyZoneName(itemDO.getZoneName());
            detail_1_2.setSupplyInstanceModel("test-instance-model");
            detail_1_2.setSupplyCoreNum(needConsensusNum * oneInstanceCore);
            detail_1_2.setSupplyRegionName(itemDO.getRegionName());
            detail_1_2.setSupplyInstanceType(itemDO.getInstanceType());
            detail_1_2.setSupplyInstanceNum(needConsensusNum);
            detail_1_2.setSupplyGpuType(itemDO.getGpuType());
            detail_1_2.setSupplyGpuNum(needConsensusNum * oneInstanceGpu);
            details.add(detail_1_2);
        }
        way_1.setDetails(details);
        saveReq.setWays(ListUtils.newArrayList(way_1));

        return saveReq;
    }

    @Test
    public void approvalForSupply() {
        GlobalApprovalReq req = new GlobalApprovalReq();
        req.setBizId("OE2405200001");
        req.setOrderNumber("OE2405200001");
        req.setFlowCode(OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE.getCode());
        req.setApprovalResult(true);
        req.setApprovalResultName(null);
        req.setApprovalRemark("CBS审批通过");
        req.setCurrentNodeCode(OrderNodeCodeEnum.node_cbs_approval.getCode());
        supplyPlanOperateService.cbsApproval(req);
    }

    @Test
    public void approvalForSupplyRefuse() {
        GlobalApprovalReq req = new GlobalApprovalReq();
        req.setBizId("OE2405130006");
        req.setOrderNumber("OE2405130006");
        req.setFlowCode(OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE.getCode());
        req.setApprovalResult(false);
        req.setApprovalResultName(null);
        req.setApprovalRemark("CBS拒绝，请重新填写CBS信息");
        req.setCurrentNodeCode(OrderNodeCodeEnum.node_cbs_approval.getCode());
        supplyPlanOperateService.cbsApproval(req);
    }

    @Test
    public void submitWaitConsensusSupplyPlan() {
        SupplyPlanSaveReq req = createNeedConsensusPlan("OE2405150001");
        supplyPlanOperateService.submitSupplyPlan(req);
    }

    @Test
    public void testApi() {

//        String url = "http://11.134.62.152/api/tcres/supply";
//        Map<String, Object> params = new HashMap<>();
//        params.put("subOrders", ListUtils.newArrayList("M202405311751434079"));
//
//        Object obj = jsonrpcClient.jsonrpc("queryOrderSupply")
//                .uri(url)
//                .noInjectServletRequest()
//                .apiKey("tcres")
//                .params(params)
//                .postForEntity(String.class);
        Object obj =  supplyPlanQueryService.callErpForSupplyProcess(
                ListUtils.newArrayList("M202405311751434079"), null);
        System.out.println(obj);
    }

    @Test
    public void batchConsensus() {
        ConsensusReq req = createConsensusReq("OE2406050002", true);
        supplyPlanOperateService.batchConsensus(req);
    }

    @Test
    public void consensusOver() {
        supplyPlanOperateService.consensusOver("OE2406050002");
    }

    private ConsensusReq createConsensusReq(String orderNumber, boolean consensusResult) {
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanVersionDO::getOrderNumber, orderNumber)
                .orderDesc("id");
        OrderSupplyPlanVersionDO version = demandDBHelper
                .getOne(OrderSupplyPlanVersionDO.class, where.getSql(), where.getParams());

        // 先获取数据库中当前版本所有满足方式
        List<OrderSupplyPlanDO> allSupplyPlan = supplyPlanQueryService.getAllSupplyPlan(version.getId(), orderNumber);
        List<Long> planIds = ListUtils.transform(allSupplyPlan, BaseDO::getId);

        // 获取数据库中当前版本的所有待共识供应方案明细
        where = new WhereContent()
                .andIn(OrderSupplyPlanDetailDO::getPlanId, planIds)
                .andEqual(OrderSupplyPlanDetailDO::getConsensusStatus,
                        OrderSupplyConsensusStatusEnum.wait_consensus.getCode());
        List<OrderSupplyPlanDetailDO> waitConsensusDetails = demandDBHelper
                .getAll(OrderSupplyPlanDetailDO.class, where.getSql(), where.getParams());

        ConsensusReq req = new ConsensusReq();
        req.setOrderNumber(orderNumber);
        req.setConsensusResult(consensusResult);
        List<ConsensusItem> items = new ArrayList<>();
        for (OrderSupplyPlanDetailDO detail : waitConsensusDetails) {
            ConsensusItem item_1 = new ConsensusItem();
            item_1.setPlanDetailId(detail.getId());
            item_1.setConsensusRemark(consensusResult ? "同意该方案" : "不同意此方案，请重新制定");
            items.add(item_1);
        }
        req.setItems(items);
        return req;
    }

    @Test
    public void supplyProgress() {
        Object res = supplyPlanOperateService.supplyProgressAndHandler("ON2411040004", null);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void planHistoryVersionList() {
        Object res = supplyPlanQueryService.planVersionList("OE2405130006");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryQuotaDetail() {
        // 这是生产环境地址
        String url = "http://11.143.57.98/delivery-transparency/api";
        Map<String, Object> params = new HashMap<>();
        params.put("quotaId", "Q2302131201089002-01");

        Map<String, Integer> page = new HashMap<>();
        page.put("start", 0);
        page.put("size", 1000);
        params.put("page", page);

        QuotaSupplyDetailResp obj = jsonrpcClient.jsonrpc("queryQuotaDetail")
                .uri(url)
                .noInjectServletRequest()
                .apiKey("tcres")
                .params(params)
                .postForObject(QuotaSupplyDetailResp.class);
        System.out.println(obj);
    }

    @Test
    public void queryApplyOrderRecord() {
        String url = "http://11.134.145.151/delivery-transparency/api";
        Map<String, Object> params = new HashMap<>();
        params.put("quotaId", "Q2302131201089002-01");

        val obj = jsonrpcClient.jsonrpc("queryApplyOrderRecord")
                .uri(url)
                .apiKey("tcres")
                .params(params)
                .noInjectServletRequest()
                .postForObject(Map.class);
        System.out.println(JSON.toJson(obj));
    }

    @Test
    public void querySmoveDetail() {
        // 这是生产环境地址
        String url = "http://11.134.145.151/delivery-transparency/api";
        Map<String, Object> params = new HashMap<>();
//        params.put("orderId", "M202303081044334412");
        params.put("orderId", "M202410221129186996");

        MoveResp obj = jsonrpcClient.jsonrpc("querySmoveDetail")
                .uri(url)
                .noInjectServletRequest()
                .apiKey("tcres")
                .params(params)
                .postForObject(MoveResp.class);
         System.out.println(obj);
    }

    @Test
    public void queryPlanProcessorByOrderRecord() {
        Object res = supplyPlanQueryService.queryPlanProcessorByOrderRecord("OE2405270051");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void getListDetailWithPlan() {
        Object res = supplyPlanQueryService.getListDetailWithPlan(ListUtils.newArrayList(250L, 249L));
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void beforeSubmitCheckForModifyPlan() {
        SupplyPlanSaveReq req = createModifyReq();
        List<OrderItemWithSupplyDetail> res = supplyPlanOperateService.beforeSubmitCheckForModifyPlan(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void submitPlanForModifyPlan() {
        SupplyPlanSaveReq req = createModifyReq();
        supplyPlanOperateService.submitPlanForModifyPlan(req);
    }

    private SupplyPlanSaveReq createModifyReq() {
        SupplyPlanSaveReq req = new SupplyPlanSaveReq();
        req.setOrderNumber("OE2406050002");

        SupplyPlanMatchWayDTO way_1 = new SupplyPlanMatchWayDTO();
        way_1.setProduct("CVM&CBS");
        way_1.setOrderNumber("OE2406050002");
        way_1.setRemark("正常大盘满足1");
        way_1.setMatchType(OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());
        MatchDetail detail_1_1 = new MatchDetail();
        detail_1_1.setOrderNumberId("OE2406050002-001");
        detail_1_1.setSupplyRemark("方案明细备注xxx");
        detail_1_1.setConsensusBeginBuyDate(LocalDate.now().plusDays(10));
        detail_1_1.setSupplyBizId(null);
        detail_1_1.setSupplyZoneName("硅谷二区");
        detail_1_1.setSupplyInstanceModel("S5.LARGE4");
        detail_1_1.setSupplyCoreNum(12);
        detail_1_1.setSupplyRegionName("美国西部(硅谷)");
        detail_1_1.setSupplyInstanceType("S5");
        detail_1_1.setSupplyInstanceNum(3);
        detail_1_1.setSupplyGpuNum(0);
        detail_1_1.setSupplyRegion("na-siliconvalley");
        detail_1_1.setSupplyZone("na-siliconvalley-2");
        detail_1_1.setSupplyOneMemory(4);
        detail_1_1.setSupplyOneCpuNum(4);
        detail_1_1.setSupplyOneGpuNum(0);

        MatchDetail detail_1_2 = new MatchDetail();
        detail_1_2.setOrderNumberId("OE2406050002-002");
        detail_1_2.setSupplyRemark("方案明细备注xxx");
        detail_1_2.setConsensusBeginBuyDate(LocalDate.now().plusDays(10));
        detail_1_2.setSupplyBizId(null);
        detail_1_2.setSupplyZoneName("广州二区");
        detail_1_2.setSupplyInstanceModel("SA5.LARGE8");
        detail_1_2.setSupplyCoreNum(16);
        detail_1_2.setSupplyRegionName("华南地区(广州)");
        detail_1_2.setSupplyInstanceType("SA5");
        detail_1_2.setSupplyInstanceNum(4);
        detail_1_2.setSupplyGpuNum(0);
        detail_1_2.setSupplyRegion("ap-guangzhou");
        detail_1_2.setSupplyZone("ap-guangzhou-2");
        detail_1_2.setSupplyOneMemory(8);
        detail_1_2.setSupplyOneCpuNum(4);
        detail_1_2.setSupplyOneGpuNum(0);

        way_1.setDetails(ListUtils.newArrayList(detail_1_1, detail_1_2));
        req.setWays(ListUtils.newArrayList(way_1));
        return req;
    }

    @Test
    public void createProductPreDeduct() {
        CreatePreDeductPlanReq req = new CreatePreDeductPlanReq();
        req.setOrderNumber("OE2406040001");
        req.setFlowRemark("测试产品预扣");
        req.setStartPreDeductDate(LocalDate.now().plusDays(1));
        req.setEndPreDeductDate(LocalDate.now().plusDays(7));
        List<PreDeductItemDTO> items = createPreDeductItemDTO(req.getOrderNumber());
        req.setItemList(items);
        preDeductOrderService.createProductPreDeduct(req);
    }

    private List<PreDeductItemDTO> createPreDeductItemDTO(String orderNumber) {
        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
        List<OrderSupplyPlanDetailWithPlanDTO> supplyList = supplyPlanQueryService.getListDetailWithPlanByVersionId(
                version.getId(), orderNumber);
        Map<String, OrderItemDO> itemDOMap = ListUtils.toMap(order.getItemList(), OrderItemDO::getOrderNumberId,
                Function.identity());
        OrderSupplyPlanDetailWithPlanDTO supplyDetail = supplyList.get(0);
        OrderItemDO itemDO = itemDOMap.get(supplyDetail.getOrderNumberId());
        List<PreDeductItemDTO> res = new ArrayList<>();
        PreDeductItemDTO deduct = new PreDeductItemDTO();
        deduct.setAcceptGpu(itemDO.getAcceptGpu());
        deduct.setBillType(itemDO.getBillType());
        deduct.setCpuNum(supplyDetail.getSupplyOneCpuNum());
        deduct.setGpuNum(supplyDetail.getSupplyOneGpuNum());
        deduct.setMemory(supplyDetail.getSupplyOneMemory());
        deduct.setGpuType(supplyDetail.getSupplyGpuType());
        deduct.setInstanceModel(supplyDetail.getSupplyInstanceModel());
        deduct.setInstanceType(supplyDetail.getSupplyInstanceType());
//        deduct.setAlternativeInstanceType();
        deduct.setDataDiskType(itemDO.getDataDiskType());
        deduct.setDataDiskStorage(itemDO.getDataDiskStorage());
        deduct.setNormalSupplyPlanDetailId(supplyDetail.getId());

//        deduct.setZoneName(supplyDetail.getSupplyZoneName());
//        deduct.setZone(supplyDetail.getSupplyZone());
        deduct.setZoneName("北京六区");
        deduct.setZone("ap-beijing-6");

        deduct.setSystemDiskStorage(itemDO.getSystemDiskStorage());
        deduct.setSystemDiskType(itemDO.getSystemDiskType());
        deduct.setRegionName(supplyDetail.getSupplyRegionName());
        deduct.setRegion(supplyDetail.getSupplyRegion());
//        deduct.setPlacementGroup(itemDO.getPlacementGroup());
//        deduct.setOtherZoneName();
        deduct.setProduct(itemDO.getProduct());
//        deduct.setOtherInstanceModel();
        deduct.setOrderNumber(itemDO.getOrderNumber());
        deduct.setSystemDiskNum(1);
        deduct.setDataDiskNum(itemDO.getDataDiskNum());

        int halfInstanceNum = itemDO.getInstanceNum() % 2 == 0 ? itemDO.getInstanceNum() / 2
                : ((itemDO.getInstanceNum() / 2) + 1);
        deduct.setTotalCore(halfInstanceNum * deduct.getCpuNum());

        int sysStorage = deduct.getSystemDiskStorage() == null ? 0 : deduct.getSystemDiskStorage();
        int dataStorage = deduct.getDataDiskStorage() == null ? 0 : deduct.getDataDiskStorage();
        int dataNum = deduct.getDataDiskNum() == null ? 0 : deduct.getDataDiskNum();
        deduct.setTotalDisk((sysStorage + dataStorage * dataNum) * halfInstanceNum);
        deduct.setTotalGpuNum(deduct.getGpuNum() == null ? null
                : BigDecimal.valueOf((long) halfInstanceNum * deduct.getGpuNum()));
        deduct.setInstanceNum(halfInstanceNum);
        res.add(deduct);
        return res;
    }

    @Test
    public void getAvailableDetailWithPlan() {
        Object res = supplyPlanQueryService.getAvailableSupplyDetailWithOrderItem("OE2406040001");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryRoleUsersForOrder() {
        QueryRoleUsersForOrderReq req = new QueryRoleUsersForOrderReq();
        req.setOrderNumber("OE2406040001");
        req.setAuthRoleCodes(ListUtils.newArrayList(
                IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE.getCode(),
                IndustryDemandAuthRoleEnum.PRODUCT_PRINCIPAL.getCode()));
        Object res = orderCommonService.queryRoleUsersForOrder(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryStockForOrderSatisfaction() {
        Object res = orderSatisfyRateService.queryStockForOrderSatisfaction(LocalDate.of(2024,8,21), 14);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void queryDemandForOrderSatisfaction() {
        Object res = orderSatisfyRateService.queryDemandForOrderSatisfaction(LocalDate.of(2024,8,21), 14);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void orderSatisfactionCalc() {
        orderSatisfyRateService.orderSatisfactionCalc(LocalDate.of(2025,6,17), 14);
    }

    @Test
    public void calcGpuSatisfyOneOrder() {
        orderSatisfyRateService.calcGpuSatisfyOneOrder("OE2503030001");
    }

    @Test
    public void calcGpuMatchRate() {
        orderSatisfyRateService.calcGpuMatchRate(LocalDate.now().minusDays(90));
    }

    @Test
    public void calcSupplyPlanDetailMatchCore() {
        orderSatisfyRateService.calcSupplyPlanDetailMatchCore("ON2411070004");
    }

    @Test
    public void modifySupplyPlanDetailMatchCore() {
        SupplyPlanDetailMatchCoreUpdateReq req = new SupplyPlanDetailMatchCoreUpdateReq();
        req.setReason("测试");
        req.setOrderNumber("OE2506090027");
        SupplyPlanDetailMatchCoreUpdateReq.Item item = new SupplyPlanDetailMatchCoreUpdateReq.Item();
        item.setMatchCore(BigDecimal.valueOf(30));
        item.setMatchInstanceNum(1);
        item.setSupplyPlanDetailId(1296L);
        List<SupplyPlanDetailMatchCoreUpdateReq.Item> items = new ArrayList<>();
        items.add(item);
        req.setItems(items);
        orderSatisfyRateService.modifySupplyPlanDetailMatchCore(req);
    }

    @Test
    public void querySatisfyModifiedRecord(){
        Object res = orderSatisfyRateService.querySatisfyModifiedRecord(530L);
        System.out.println(JSON.toJson(res));
    }

    private ServiceLevelQueryReq createServiceLevelQueryReq() {
        ServiceLevelQueryReq req = new ServiceLevelQueryReq();
        req.setInstanceTypes(ListUtils.newArrayList("S5", "SA5"));
        req.setRegionNames(ListUtils.newArrayList("硅谷", "弗吉尼亚"));
        req.setBeginMonth(YearMonth.of(2023, 7));
        req.setEndMonth(YearMonth.of(2023, 7));
        return req;
    }

    @Test
    public void officialWebsiteServiceLevelOverview() {
        ServiceLevelQueryReq req = new ServiceLevelQueryReq();
        req.setProduct(Ppl13weekProductTypeEnum.DATABASE.getName());
        req.setBeginMonth(YearMonth.of(2025,4));
        req.setEndMonth(YearMonth.of(2025,7));
        req.setDatabaseName(ListUtils.newArrayList(PplDatabaseEnum.TDSQLC.getName()));
        Object res = orderSatisfyRateService.officialWebsiteServiceLevelOverview(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void officialWebsiteBuyData() {
        ServiceLevelQueryReq req = new ServiceLevelQueryReq();
        req.setInstanceTypes(ListUtils.newArrayList("S5", "SA5"));
        req.setRegionNames(ListUtils.newArrayList("硅谷", "弗吉尼亚"));
        req.setBeginMonth(YearMonth.of(2023, 10));
        req.setEndMonth(YearMonth.of(2023, 10));

        req = createServiceLevelQueryReq();
        List<OfficialWebsiteBuyDTO> res = orderSatisfyRateService.officialWebsiteBuyData(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void officialWebsiteSoldOutData() {
        ServiceLevelQueryReq req = new ServiceLevelQueryReq();
        req.setInstanceTypes(ListUtils.newArrayList("S5", "SA5"));
        req.setRegionNames(ListUtils.newArrayList("硅谷", "弗吉尼亚"));
        req.setBeginMonth(YearMonth.of(2023, 10));
        req.setEndMonth(YearMonth.of(2023, 10));

        req = createServiceLevelQueryReq();
        Object res = orderSatisfyRateService.officialWebsiteSoldOutData(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void orderServiceLevelDetail() {
        ServiceLevelQueryReq req = new ServiceLevelQueryReq();
        req.setProduct(Ppl13weekProductTypeEnum.DATABASE.getName());
        req.setBeginMonth(YearMonth.of(2025, 6));
        req.setEndMonth(YearMonth.of(2025, 6));
//        req.setIndustryDeptList(ListUtils.newArrayList("智慧行业一部"));
//        req.setWarZones(ListUtils.newArrayList("方案架构四中心"));
//        req.setInstanceTypes(ListUtils.newArrayList("SA5"));
//        req.setRegionNames(ListUtils.newArrayList("北京"));
//        req.setOrderTypeList(ListUtils.newArrayList(OrderTypeEnum.ELASTIC.getCode()));
//        req.setElasticTypeList(ListUtils.newArrayList("日弹性"));
//        req.setOrderNodeCodeList(ListUtils.newArrayList("node_order_supply"));
        Object res = orderSatisfyRateService.orderServiceLevelDetail(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void orderServiceLevelOverview() {
        ServiceLevelQueryReq req = createServiceLevelQueryReq();
        req.setBeginMonth(YearMonth.of(2024, 10));
        req.setEndMonth(YearMonth.of(2024, 10));
        req.setInstanceTypes(null);
        req.setRegionNames(null);
        req.setIndustryDeptList(ListUtils.newArrayList("战略客户部"));
        req.setCustomerShortNames(ListUtils.newArrayList("拼多多"));
        Object res = orderSatisfyRateService.orderServiceLevelOverview(req);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void syncSimpleOrderServiceLevelSummary() {
        orderSatisfyRateService.syncSimpleOrderServiceLevelSummary();
    }

    @Test
    public void completePreDeduct() {
        preDeductOrderService.completePreDeduct();
    }

    @Test
    public void completePreDeductForOrder() {
        preDeductOrderService.completePreDeductForOrder("OE2407050002");
    }

    @Test
    public void cbsAuditOverTimeNotice() {
        supplyPlanOperateService.cbsAuditOverTimeNotice();
    }

    @Test
    public void industrySatisfyGapAlert() {
        orderSatisfyRateService.industrySatisfyGapAlert();
    }

    @Test
    public void globalSatisfyGapAlert() {
        orderSatisfyRateService.globalSatisfyGapAlert();
    }

    @Test
    public void deliverTrack() {
        Object res = supplyPlanQueryService.deliverTrack(104L);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void deliverTrackAndRiskForecast() {
        Object res = supplyPlanQueryService.deliverTrackAndRiskForecast(894L);//268 537
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void demandDetailForWaitSupply() {
        Object res = supplyPlanQueryService.demandDetailForWaitSupply("OE2501030001", null);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void genDemandConsensusDetailTest() {
        PplOrderAdjustDetailReq req = new PplOrderAdjustDetailReq();
        req.setVersionCode("2025-06-24");
        req.setProduct("CVM&CBS");
        req.setIndustryDept("智慧行业一部");
        req.setDemandType("NEW");
        req.setStartYearMonth("2025-06");
        req.setEndYearMonth("2025-09");
        pplOrderAdjustService.queryPplOrderDetail(req);
        //List<ConsensusDemandDetailDTO> result = supplyPlanQueryService.getAvailableConsensusDemandDetail(
         //       "OE2501030001");
        //System.out.println(result);
    }


    @Test
    public void getAvailableConsensusDemandDetail() {
        Object res = supplyPlanQueryService.getAvailableConsensusDemandDetail("OE2506030001");
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void preDeductItemForCreateChoose() {
        Object res = supplyPlanQueryService.preDeductItemForCreateChoose("OE2501060006",false);
        System.out.println(JSON.toJson(res));
    }

    @Test
    public void refreshSatisfyForNotSatisfyMatchOneOrder() {
        orderSatisfyRateService.refreshSatisfyForNotSatisfyMatchOneOrder("OE2506180002");
        System.out.println("OK");
    }

    @Test
    public void refreshSatisfyForNotSatisfyMatch() {
        OrderDetailResp order = orderCommonService.queryOrderDetail("OE2503180006");
//        orderSatisfyRateService.refreshSatisfyForNotSatisfyMatch(false);
        System.out.println("OK");
    }

    @Test
    public void createGridData() {
        String orderNumber = "ON2504140002";
        List<String> status = ListUtils.transform(PreDeductOrderStatusEnum.values(), PreDeductOrderStatusEnum::getCode);
        List<PreDeductOrderItemDO> deductList = preDeductOrderService.queryPreDeductItems(orderNumber, status);
        List<PreDeductGridSimpleDTO> gridList = new ArrayList<>();
        for (PreDeductOrderItemDO deductItemDO : deductList) {
            LocalDate begin = deductItemDO.getStartPreDeductDate();
            BigDecimal successRate = BigDecimal.ONE;
            BigDecimal buyRate = BigDecimal.ONE;
            while (!begin.isAfter(deductItemDO.getEndPreDeductDate())) {
                if (begin.isAfter(LocalDate.now())) {
                    break;
                }
                successRate = rateCreate();
                buyRate = rateCreate();
                gridList.add(createPreDeductGridSimpleDTO(deductItemDO, begin, successRate, buyRate));
                begin = begin.plusDays(1);
            }
        }
        DBList.ckcldStdCrpDBHelper.insertBatchWithoutReturnId(gridList);
    }

    private PreDeductGridSimpleDTO createPreDeductGridSimpleDTO(PreDeductOrderItemDO deductItemDO, LocalDate date,
            BigDecimal successRate, BigDecimal buyRate) {
        PreDeductGridSimpleDTO grid = new PreDeductGridSimpleDTO();
        grid.setImpDate(date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        grid.setPlanInstanceType(deductItemDO.getInstanceType());
        grid.setOrderNumber(deductItemDO.getOrderNumber());
        grid.setPlanZoneName(deductItemDO.getZoneName());

        int totalCpu = successRate.multiply(new BigDecimal(deductItemDO.getTotalCore())).intValue();
        int instanceNum = totalCpu / deductItemDO.getCpuNum();
        totalCpu = instanceNum * deductItemDO.getCpuNum();
        grid.setPeakTotalCpu(totalCpu);

        int buyCpu = buyRate.multiply(new BigDecimal(deductItemDO.getTotalCore())).intValue();
        int buyNum = buyCpu / deductItemDO.getCpuNum();
        buyCpu = buyNum * deductItemDO.getCpuNum();
        buyCpu = Math.min(buyCpu, totalCpu);
        grid.setPeakBuyCpu(buyCpu);

        grid.setPeakEmptyCpu(grid.getPeakTotalCpu() - grid.getPeakBuyCpu());
        grid.setGridGpu(deductItemDO.getGpuNum());
        grid.setGridCpuNum(deductItemDO.getCpuNum());
        return grid;
    }

    private BigDecimal rateCreate() {
        // 生成一个 大于等于 0 ，且小于等于 1 的随机数
        return BigDecimal.valueOf(Math.random());
    }

    @Test
    public void calcElasticOrderSatisfy() throws InterruptedException {
        orderSatisfyRateService.calcElasticOrderSatisfyOneOrder("OE2405280002");
        Thread.sleep(1000 * 60);
    }

    @Test
    public void calcCycleElasticOrderMonthSatisfy() {
        orderSatisfyRateService.calcCycleElasticOrderMonthSatisfy("ON2504140002");
    }

    @Test
    public void supplyCreateOverTimeNotice() {
        supplyPlanOperateService.supplyCreateOverTimeNotice();
    }

    @Test
    public void updateOrderConsensusByNumChange() {
        orderSatisfyRateService.updateOrderConsensusByNumChange("OE2506150001");
    }

    @Test
    public void backupServiceLevelDetail() {
        BackupServiceLevelDetailReq req = new BackupServiceLevelDetailReq();
        req.setUseCkData(true);
        req.setEndMonth(YearMonth.of(2025, 4));
        orderSatisfyRateService.backupServiceLevelDetail(req);
    }

}
