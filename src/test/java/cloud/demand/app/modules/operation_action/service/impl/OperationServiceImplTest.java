package cloud.demand.app.modules.operation_action.service.impl;

import cloud.demand.app.modules.operation_action.model.FilterReq;
import cloud.demand.app.modules.operation_action.model.InventoryDTO;
import cloud.demand.app.modules.operation_action.service.OperationService;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class OperationServiceImplTest {

    @Autowired
    OperationService operationService;

    @Test
    void listInventory() {
        FilterReq filter = new FilterReq();
        List<InventoryDTO> ls = operationService.listInventory(filter);
        ls.forEach(System.out::println);
    }
}