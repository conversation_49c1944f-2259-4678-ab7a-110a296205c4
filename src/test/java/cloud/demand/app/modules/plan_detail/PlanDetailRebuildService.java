package cloud.demand.app.modules.plan_detail;

import cloud.demand.app.modules.plan_detail.service.cvm.PlanDetailCVMInventoryService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class PlanDetailRebuildService {

    private final String statTime = "2022-09-03";

    @Resource
    PlanDetailCVMInventoryService planDetailCvmService;

    @Test
    public void rebuild(){
        planDetailCvmService.getAndSaveInventoryData(statTime);
    }

}
