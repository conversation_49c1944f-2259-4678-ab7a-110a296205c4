package cloud.demand.app.modules.plan_detail.service;

import cloud.demand.app.modules.cvmjxc.service.cvm.CalculateCVMIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.gpu.CalculateGPUIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.metal.CalculateMetalIndicatorService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class PlanCloudDetailServiceTest {

    @Autowired
    PlanCloudDetailService planCloudDetailService;
    @Resource
    private CalculateCVMIndicatorService newCvmService;
    @Resource
    private CalculateMetalIndicatorService newMetalService;
    @Resource
    private CalculateGPUIndicatorService newGpuService;
    @Test
    void getAndSaveCvmCloudDetail() {
       // planCloudDetailService.getAndSaveCvmCloudDetail("2023-12-18");
    }

    @Test
    void test() {
        String statTime = "2023-12-27";
//        planCloudDetailService.getAndSaveCvmCloudDetail(statTime);
//        planCloudDetailService.getAndSaveMetalCloudDetail(statTime);
//        planCloudDetailService.getAndSaveGpuCloudDetail(statTime);
//
//
//        newCvmService.calAndSave(statTime);
//        newMetalService.calAndSave(statTime);
//        newGpuService.calAndSave(statTime);

    }

}