package cloud.demand.app.modules.plan_detail;

import cloud.demand.app.modules.cvmjxc.service.CalculateService;
import cloud.demand.app.modules.cvmjxc.service.cos.COSDailyDiffDTO;
import cloud.demand.app.modules.cvmjxc.service.cos.CalculateCOSIndicatorService;
import cloud.demand.app.modules.plan_detail.service.cos.PlanDetailCOSService;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@SpringBootTest
public class COSJxcTest {

    @Resource
    PlanDetailCOSService planDetailCOSService;
    @Resource
    CalculateCOSIndicatorService calculateCOSIndicatorService;
    @Resource
    private CalculateService calculateService;

    private final String start = "2022-12-28";
    private final String end = "2023-01-04";

    @Test
    public void testPlanDetail(){
        planDetailCOSService.calculateAndSave(end);
        calculateCOSIndicatorService.calAndSave(end);
    }

    @Test
    public void testCOSJxc(){

        System.out.println(negativeNum(new BigDecimal(1)));
    }

    public BigDecimal negativeNum(BigDecimal decimal) {
        return decimal == null ? BigDecimal.ZERO : decimal.negate();
    }


    private BigDecimal add(BigDecimal... a){
        if (a == null){
            return BigDecimal.ZERO;
        }
        BigDecimal ret = BigDecimal.ZERO;
        for (BigDecimal bigDecimal : a) {
            ret.add(bigDecimal == null ? BigDecimal.ZERO : bigDecimal);
        }
        return ret;
    }

    @Test
    public void testCOSDiffDaily() {
        List<COSDailyDiffDTO> validDailyDiff =
                calculateCOSIndicatorService.getValidDailyDiff("2022-10-08");
        validDailyDiff.forEach(o -> System.out.println(JSON.toJson(o)));
    }

    @Test
    public void testAllJxc(){
//        calculateService.genJxcData(date);
    }

    public static void main(String[] args) {
//        BigDecimal num = 0.000000;
//        System.out.println(num);
//        System.out.println(Objects.equals(num, BigDecimal.ZERO));
    }
}


