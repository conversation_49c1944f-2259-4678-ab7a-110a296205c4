package cloud.demand.app.modules.plan_detail;

import cloud.demand.app.modules.cvmjxc.service.cbs.PlanCBSApiService;
import cloud.demand.app.modules.plan_detail.service.cbs.PlanDetailCBSService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;

@SpringBootTest
public class PlanDetailCBSTests {

    @Resource
    private PlanDetailCBSService planDetailCBSService;
    @Resource
    private PlanCBSApiService planCBSApiService;

    @Test
    public void testInventory() {
        planDetailCBSService.calculateAndSave2("2022-08-15");
    }

    @Test
    public void testPlanCBSApi() {
        BigDecimal physicTotalStorage = planCBSApiService.getPhysicTotalStorage("2022-07-27");
        System.out.println(physicTotalStorage);
    }

}
