package cloud.demand.app.modules.plan_detail;

import cloud.demand.app.modules.cvmjxc.service.cdb.CalculateCDBIndicatorService;
import cloud.demand.app.modules.plan_detail.service.PlanDetailService;
import cloud.demand.app.modules.plan_detail.service.cdb.PlanDetailCDBService;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

@SpringBootTest
public class CDBTest {

    @Autowired
    private PlanDetailCDBService planDetailCDBService;
    @Autowired
    private PlanDetailService planDetailService;
    @Autowired
    private CalculateCDBIndicatorService cdbIndicatorService;

    private final String statTime = "2022-03-20";

    @Test
    public void testPlanDetail() {
        planDetailCDBService.genCDBPlanDetailData(statTime);
    }

    @Test
    public void testPlanDetails() {
        planDetailService.genAllPlanDetailData(statTime);
    }

    @Test
    public void testJxc(){
        cdbIndicatorService.calAndSave(statTime);
    }

    @Test
    public void test() {
        Sum sum1 = new Sum(BigDecimal.valueOf(1), BigDecimal.valueOf(3),BigDecimal.valueOf(1), BigDecimal.valueOf(3),BigDecimal.valueOf(1));
//        Sum sum2 = new Sum(BigDecimal.valueOf(6), BigDecimal.valueOf(9));
//        Sum sum3 = new Sum(BigDecimal.valueOf(14), BigDecimal.valueOf(15));

//        Map<String, Object> map = new HashMap<>();
//        map.put("sum1", sum1);
//        map.put("sum2", sum2);
//        map.put("sum3", sum3);
//
//        BigDecimal result = ((BigDecimal) MVEL.eval("sum1.a + sum2.a + sum3.a", map));
//        System.out.println(result);

//        ((BigDecimal) MVEL.eval("a + b + b1 + b2 + b3", sum1));
    }

    @Data
    @AllArgsConstructor
    static class Sum{
        private BigDecimal a;
        private BigDecimal b;
        private BigDecimal b1;
        private BigDecimal b2;
        private BigDecimal b3;
    }
}
