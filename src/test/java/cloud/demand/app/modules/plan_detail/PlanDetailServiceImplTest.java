package cloud.demand.app.modules.plan_detail;

import cloud.demand.app.modules.plan_detail.service.*;
import cloud.demand.app.modules.plan_detail.service.cbs.PlanDetailCBSService;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class PlanDetailServiceImplTest {

    @Resource
    PlanInventoryDetailService planInventoryDetailService;
    @Resource
    PlanSaleDetailService planSaleDetailService;
    @Resource
    PlanOtherDetailService planOtherDetailService;
    @Resource
    PlanOversoldService planOversoldService;
    @Resource
    PlanVirtualCostDetailService virtualCostDetailService;
    @Resource
    MachineLaunchCloseService machineLaunchCloseService;
    @Resource
    PlanDetailService planDetailService;
    @Resource
    PlanDetailCBSService planDetailCBSService;

    final String date = "2023-04-05";

    @Test
    public void genPlanDetail(){

        planDetailService.genAllPlanDetailData("2022-12-05");

       // planDetailCBSService.calculateAndSave2(date);
    }

    @Test
    public void testGpu(){
        planInventoryDetailService.getAndSaveCvmInventoryData(date);
//        planSaleDetailService.getAndSaveCvmSaleData(date);
//        planOtherDetailService.getAndSaveCvmOtherData(date);
//        planOversoldService.getAndSaveOversoldData(date);
//        virtualCostDetailService.getAndSaveVirtualCostData(date);
//        machineLaunchCloseService.getAndSaveMachineLaunchCloseData(date);
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Person{
        String name;
    }

    @NoArgsConstructor
    @Data
    static class Student extends Person{
        Integer age;

        public Student(String name) {
            super(name);
        }


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TestGeneric<T>{
        private T msg;

        public T getT(){
            return this.msg;
        }

        public <T> T getT2(T integer){
            return integer;
        }
    }

    @Test
    public void test2(){
        TestGeneric<String> hello = new TestGeneric<>("hello");
        System.out.println(hello.getClass());
        System.out.println(hello.getT().getClass());
        System.out.println(hello.getT2(2).getClass());
//        System.out.println(hello.getT());
//        Integer t2 = hello.getT2(2);
//        System.out.println(t2);
    }

    private <T> T testGeneric(List<T> list){
        return ListUtils.isEmpty(list) ? null : list.get(0);
    }










}
