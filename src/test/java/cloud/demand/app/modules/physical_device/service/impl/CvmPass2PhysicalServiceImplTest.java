package cloud.demand.app.modules.physical_device.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CvmPass2PhysicalServiceImplTest {
    @Autowired
    CvmPass2PhysicalServiceImpl cvmPass2PhysicalService ;
    @Test
    void pass2() {
        cvmPass2PhysicalService.pass2();
    }
}