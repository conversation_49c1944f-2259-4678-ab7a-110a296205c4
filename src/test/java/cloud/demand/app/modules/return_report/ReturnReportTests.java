package cloud.demand.app.modules.return_report;

import cloud.demand.app.modules.return_report.service.QueryReturnReportService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class ReturnReportTests {

    @Autowired
    private QueryReturnReportService queryReturnReportService;

    @Test
    public void testGetStatTime() {
        System.out.println(queryReturnReportService.getLatestStatTime());
    }

}
