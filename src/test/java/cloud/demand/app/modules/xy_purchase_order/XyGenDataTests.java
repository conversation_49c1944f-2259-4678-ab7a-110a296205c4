package cloud.demand.app.modules.xy_purchase_order;

import cloud.demand.app.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.app.modules.xy_purchase_order.job.GenXyPurchaseOrderTask;
import cloud.demand.app.modules.xy_purchase_order.service.PurchaseOrderDetailService;
import cloud.demand.app.modules.xy_purchase_order.service.XyBaseInfoService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class XyGenDataTests {

    @Resource
    private GenXyPurchaseOrderTask genXyPurchaseOrderTask;
    @Resource
    private PurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private XyBaseInfoService xyBaseInfoService;

    private final String date = "2022-12-21";

    @Test
    public void test() {
        genXyPurchaseOrderTask.run();
    }

    @Test
    public void syncCk() {
        purchaseOrderDetailService.writeDataToClickhouse("2022-08-17");
    }

    @Test
    public void testBaseInfo() {
        List<CloudDemandCsigResourceViewCategoryDO> planProducts = xyBaseInfoService.getPlanProducts("");
        System.out.println(JSON.toJson(ListUtils.transform(planProducts, o -> o.getPlanProductName())));
    }

    @Test
    public void testPurchase(){
//        purchaseOrderDetailService.genPurchaseData(date);
        purchaseOrderDetailService.genPurchaseROrderData(date);

    }

    public static void main(String[] args) {
        String json = "{\"2019-11-04\": 200}";
        Map<String, Object> map =
                JSON.parseToMap(json);
        map.keySet().forEach(System.out::println);
    }
}
