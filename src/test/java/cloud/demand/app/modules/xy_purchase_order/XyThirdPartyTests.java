package cloud.demand.app.modules.xy_purchase_order;

import cloud.demand.app.modules.xy_purchase_order.model.DeliveryDeviceDTO;
import cloud.demand.app.modules.xy_purchase_order.model.ZoneAreaCountryDTO;
import cloud.demand.app.modules.xy_purchase_order.service.XyBaseInfoService;
import cloud.demand.app.modules.xy_purchase_order.service.XyThirdPartyService;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class XyThirdPartyTests {

    @Resource
    private XyThirdPartyService xyThirdPartyService;
    @Resource
    private XyBaseInfoService xyBaseInfoService;

    @Test
    public void test() throws Exception {
        List<DeliveryDeviceDTO> deliveryDeviceDTOS =
                xyThirdPartyService.queryDeliveryDevice("Q2206131034069001-01S9", DateUtils.parse("2021-04-04"));
        System.out.println(deliveryDeviceDTOS);
    }

    @Test
    public void test2() throws Exception {
        List<ZoneAreaCountryDTO> zones = xyBaseInfoService.getZoneAreaCountry();
        System.out.println(zones);
    }

}
