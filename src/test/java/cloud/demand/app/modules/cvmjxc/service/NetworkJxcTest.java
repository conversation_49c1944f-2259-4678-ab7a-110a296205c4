package cloud.demand.app.modules.cvmjxc.service;

import cloud.demand.app.modules.cvmjxc.service.cdb.CalculateCDBIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.network.CalculateNetworkIndicatorService;
import cloud.demand.app.modules.plan_detail.service.cdb.PlanDetailCDBService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class NetworkJxcTest {
    @Resource
    CalculateNetworkIndicatorService networkIndicatorService;
    @Resource
    PlanDetailCDBService cdbService;
    @Resource
    CalculateCDBIndicatorService cdbIndicatorService;

    private final String date = "2022-05-16";

    @Test
    public void test(){
        cdbService.genCDBPlanDetailData(date);
        cdbIndicatorService.calAndSave(date);
    }
}
