package cloud.demand.app.modules.cvmjxc.service;

import cloud.demand.app.entity.rrp.ReportCvmJxcDO;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.cvmjxc.model.ReportCvmJxcSumVO;
import cloud.demand.app.modules.cvmjxc.service.cbs.CalculateCBSIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.cmongo.CalculateCMongoIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.cos.CalculateCOSIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.crs.CalculateCRSIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.others.*;
import cloud.demand.app.modules.plan_detail.service.cos.PlanDetailCOSService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.json.JSON;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;

@SpringBootTest
public class CalculateServiceImplTest {

    @Autowired
    private CalculateInventoryService inventoryService;
    @Autowired
    private CalculateSaleService saleService;
    @Autowired
    private CalculateOtherService otherService;
    @Autowired
    private CalculateEnterService enterService;
    @Resource
    private CalculateCBSIndicatorService calculateCBSIndicatorService;
    @Resource
    private CalculateService calculateService;
    @Resource
    private CalculateCommonService calculateCommonService;
    @Resource
    private DBHelper crsDBHelper;
    @Resource
    private CalculateCRSIndicatorService crsIndicatorService;
    @Resource
    private CalculateCMongoIndicatorService cMongoIndicatorService;
    @Resource
    private PlanDetailCOSService planDetailCOSService;
    @Resource
    private CalculateCOSIndicatorService cosIndicatorService;
    @Resource
    private DBHelper rrpDBHelper;

    private final String date = "2023-01-08";

    @Test
    public void testCos(){
//        planDetailCOSService.calculateAndSave(date);
        cosIndicatorService.calAndSave(date);
    }

    @Test
    public void testCalAll() {
        calculateService.genJxcData(date);
    }

    @Test
    public void test() {
        calculateCommonService.genPhysicalResourceScaleIndicator(date);
    }

    @Test
    public void testIndicatorSum() {
        ReportCvmJxcSumVO sum = calculateCommonService.getJxcIndicatorSum("2022-01-01", "CVM", "C1");
        System.out.println(JSON.toJson(sum));
    }

    /**单测单独某个产品的物理资源规模*/
    @Test
    public void testPhysicalResource() {
        ReportCvmJxcDO p26 = calculateCommonService.genPhysicalResourceScaleIndicator("2022-09-26", ProductTypeEnum.CBS);
        ReportCvmJxcDO p27 = calculateCommonService.genPhysicalResourceScaleIndicator("2022-09-27", ProductTypeEnum.CBS);

        System.out.println("09-26:" + p26);
        System.out.println("09-27:" + p27);

    }

//    @Test
//    public void testInventory() {
//        inventoryService.getAndSaveInventoryData(date);
//    }

    @Test
    public void testCBSIndicator() {
        calculateCBSIndicatorService.calAndSave(date);
    }

    @Test
    public void testCrs() {
        crsIndicatorService.calAndSave(date);
    }

    @Test
    public void testCmongo(){
        cMongoIndicatorService.genCmongoBaseSnapshotData();
    }



//
//    @Test
//    public void test2(){
//        String startTime = "2022-01-01";
//        for (int i = 2; i < 12; i++) {
//            String month = i < 10 ? "0" + i : String.valueOf(i);
//            String nextMonthFirstDay = "2022-" + month + "-01";
//            String sql = "select distinct stat_time from report_cvm_jxc " +
//                    "   where deleted = 0 and stat_time between ? and ?";
//            List<String> dates = rrpDBHelper.getRaw(String.class, sql, startTime,
//                    DateUtils.addTime(DateUtils.parse(nextMonthFirstDay), Calendar.DATE, -1));
//            String sql2 = "select indicator_name, sum(logic_num) logic_num\n" +
//                    "from report_cvm_jxc where deleted = 0 and product_type = 'GPU' and indicator_name in ('总库存', '外部常规售卖', '内部申领')\n" +
//                    "and stat_time between ? and ?\n" +
//                    "group by indicator_name";
//            List<GpuDTO> all = rrpDBHelper.getAll(GpuDTO.class, sql2, startTime,
//                    DateUtils.addTime(DateUtils.parse(nextMonthFirstDay), Calendar.DATE, -1));
//            for (GpuDTO gpuDTO : all) {
//
//            }
//
//
//        }
//        rrpDBHelper.getAll("")
//    }

    @Data
    static class GpuDTO{
        @Column("indicator_name")
        private String indicatorName;
        @Column("logic_num")
        private BigDecimal value;
    }


}
