package cloud.demand.app.modules.cvmjxc.service.rebuild;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.cvmjxc.service.AllProductSummaryService;
import cloud.demand.app.modules.cvmjxc.service.cvm.CalculateCVMIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.gpu.CalculateGPUIndicatorService;
import cloud.demand.app.modules.cvmjxc.service.metal.CalculateMetalIndicatorService;
import cloud.demand.app.modules.cvmjxc.web.model.QueryAllProductSummaryReq;
import cloud.demand.app.modules.cvmjxc.web.model.QueryAllProductSummaryResp;
import cloud.demand.app.modules.plan_detail.service.PlanDetailService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class ExpandJxcTest {

    private final String date = "2022-10-18";
    @Resource
    CalculateCVMIndicatorService newCvmService;
    @Resource
    CalculateMetalIndicatorService newMetalService;
    @Resource
    CalculateGPUIndicatorService newGpuService;
    @Resource
    AllProductSummaryService allProductSummaryService;
    @Resource
    DictService dictService;
    @Resource
    PlanDetailService planDetailService;


    @Test
    public void testAll(){
         newCvmService.calAndSave(date);
         newMetalService.calAndSave(date);
         newGpuService.calAndSave(date);
         newGpuService.genGpuJxcByCardType(date);
    }

    @Test
    public void testCvm(){
        newCvmService.calAndSave(date);
    }

    @Test
    public void testMetal(){
        newMetalService.calAndSave(date);
    }

    @Test
    public void testGpu(){
        newGpuService.calAndSave(date);
    }

    @Test
    public void testSummary(){
        //  汇总表优化测试
        QueryAllProductSummaryReq all = new QueryAllProductSummaryReq();
        all.setStartDate("2022-09-18");
        all.setEndDate("2022-10-17");
        long startTime = System.currentTimeMillis();
        QueryAllProductSummaryResp resp = allProductSummaryService.queryAllProductSummary(all);
        System.out.println(resp);
        long endTime = System.currentTimeMillis();
        System.out.println("任务耗时：" + (endTime - startTime) + "ms");
    }

    @Test
    public void testPlanDetail(){
        //  PlanDetail生成测试
        planDetailService.genAllPlanDetailData(date);
    }


    @Test
    public void test5(){
        //  测试机型的CpuPlatform   Or  NetworkCardType
        System.out.println(dictService.getServerPartsCompositionByDeviceType("Y0-M10-10"));
    }
}
