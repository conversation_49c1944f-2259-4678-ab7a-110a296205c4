package cloud.demand.app.modules.cvmjxc.service;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.enums.IndicatorEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.cvmjxc.model.BusinessTypeDTO;
import cloud.demand.app.modules.cvmjxc.model.ReportJxcPaasVo;
import cloud.demand.app.modules.cvmjxc.service.impl.AllProductSummaryServiceImpl;
import cloud.demand.app.modules.cvmjxc.service.others.RrpBaseInfoService;
import cloud.demand.app.modules.cvmjxc.web.model.QueryAllProductSummaryResp;
import cloud.demand.app.modules.cvmjxc.web.model.QueryP2pSummaryResp;
import cloud.demand.app.modules.cvmjxc.web.model.QueryWeekReportSummaryReq;
import cloud.demand.app.modules.cvmjxc.web.model.QueryWeeklyGraphResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class RrpBaseInfoServiceTests {

    @Resource
    private RrpBaseInfoService rrpBaseInfoService;

    @Resource
    AllProductSummaryServiceImpl allProductSummaryService;

    @Test
    public void test() {
        List<BusinessTypeDTO> business = rrpBaseInfoService.getPlanProductToBusiness();
        System.out.println(JSON.toJson(business));

        String sql = rrpBaseInfoService.generateNotZysySQLCondition(business);
        System.out.println(sql);
    }

    @Test
    public void test2() {
        QueryWeekReportSummaryReq req = new QueryWeekReportSummaryReq();
        req.setId(10);
        req.setYear(2022);
        req.setMonth(10);
        req.setStartDate(LocalDate.parse("2022-10-20"));
        req.setEndDate(LocalDate.parse("2022-10-27"));
        req.setProduct(ProductTypeEnum.GPU);
//        List list = allProductSummaryService.getFixInitData(req);
//        list.forEach(System.out::println);

        QueryWeeklyGraphResp resp = allProductSummaryService.getWeeklyGraphBy(req);
        System.out.println(resp);
//        LocalDate m = LocalDate.parse("2022-07-31");
//        System.out.println(m);
//        System.out.println(m.minusMonths(1));
//        QueryP2pSummaryResp resp2 = allProductSummaryService.queryP2pReport(req);
//        System.out.println(JSON.toJson(resp2));
    }

    @Resource
    DBHelper rrpDBHelper;

    @Test
    public void test3() {
        String sql = ORMUtils.getSql("/sql/repo_show/jxc_cos.sql");
        String fsql = sql.replace("{date1}", "2022-10-20")
                .replace("{date2}", "2022-10-21").replace("{date3}", "2022-10-27");
        System.out.println(fsql);
        List datas = rrpDBHelper.getRaw(ReportJxcPaasVo.class, fsql);
        datas.forEach(System.out::println);
    }
}
