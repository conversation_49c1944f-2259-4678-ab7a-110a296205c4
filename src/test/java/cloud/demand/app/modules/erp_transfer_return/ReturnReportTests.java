package cloud.demand.app.modules.erp_transfer_return;

import cloud.demand.app.modules.erp_transfer_return.service.ErpBaseService;
import cloud.demand.app.modules.erp_transfer_return.service.ReturnReportService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class ReturnReportTests {

    @Resource
    private ReturnReportService returnReportService;
    @Resource
    private ErpBaseService erpBaseService;

    @Test
    public void moveCk() {
        returnReportService.mergeDataToClickhouse("2022-10-20");
    }

    @Test
    public void test(){
        System.out.println(erpBaseService.getAllBizGroup());

    }

}
