package cloud.demand.app.modules.yunti_demand.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.app.modules.yunti_demand.model.publicPoolReq;
import cloud.demand.app.modules.yunti_demand.service.YuntiDemand2CrpService;
import cloud.demand.app.web.model.common.Period;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: williamhhu
 * @createTime: 2023/12/06 17:09
 * @description:
 */
@SpringBootTest
class YuntiDemand2CrpServiceImplTest {

    @Resource
    private YuntiDemand2CrpService yuntiDemand2CrpService;

    @Test
    public void testDemand2Crp() {
        Period period = new Period();
        period.setStart("2023-01");
        period.setEnd("2023-02");
        System.out.println(yuntiDemand2CrpService.publicPool2Crp(new publicPoolReq(period)));
    }
}