package cloud.demand.app.modules.sop_device.service.impl;

import cloud.demand.app.modules.sop_device.domain.DeviceStockSupplyReq;
import cloud.demand.app.modules.sop_device.domain.DeviceStockSupplyRsp;
import cloud.demand.app.modules.sop_device.domain.DeviceStockSupplyRsp.Data;
import cloud.demand.app.modules.sop_device.domain.HedgingWrapperReq;
import cloud.demand.app.modules.sop_device.domain.IndexVal;
import cloud.demand.app.modules.sop_device.domain.IndexVal.ValItem;
import cloud.demand.app.modules.sop_device.domain.SupplyDemandHedgingResultDTO;
import cloud.demand.app.modules.sop_device.entity.SupplyDimEnums;
import cloud.demand.app.modules.sop_device.entity.SupplyIndexEnums;
import cloud.demand.app.modules.sop_device.entity.SupplyWrapper;
import cloud.demand.app.modules.sop_device.service.DataCreateJob;
import cloud.demand.app.modules.sop_device.service.DeviceDetailInfoService;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CalculateStockSupplyServiceImplTest {

    @Autowired
    DataCreateJob dataCreateJob;

    @Autowired
    CalculateStockSupplyServiceImpl calculateStockSupplyService;

    @Resource
    private DeviceDetailInfoService deviceDetailInfoService;


    @Resource
    private DeviceDetailInfoServiceImpl deviceDetailInfoServiceImpl;

    @Resource
    private DBHelper demandDBHelper;

    @Test
    public void t1() {
        DeviceStockSupplyReq req = new DeviceStockSupplyReq();

        req.setStartVersion(1691547616l);
        req.setEndVersion(1691547616l);
        req.setGroupByDim(Lists.newArrayList(SupplyDimEnums.dimErpBiz));

        DeviceStockSupplyRsp rsp = calculateStockSupplyService.queryDeviceHedgingResult2(req);

        System.out.println(JSON.toJsonFormatted(rsp));
    }

    @Test
    public void t11() {
        List<SupplyDemandHedgingResultDTO> raw =
                demandDBHelper.getAll(SupplyDemandHedgingResultDTO.class, "where ver_num=?", "1691547616");
        dataCreateJob.saveToCk(raw, "1691547616");
        long end = System.currentTimeMillis();
    }


    @Test
    public void twwtWrite() {
        HedgingWrapperReq hedgingWrapperReq = new HedgingWrapperReq(1692152173L);
        deviceDetailInfoService.queryByHttp(hedgingWrapperReq);
        List<SupplyDemandHedgingResultDTO> raw =
                demandDBHelper.getAll(SupplyDemandHedgingResultDTO.class, "where ver_num=?", "1678692078");
        System.out.println("size" + raw.size());
        dataCreateJob.saveToCk(raw, "1679367746");
        System.out.println("done");
    }


    /**
     * 1678692078|
     * 1678840597|
     * 1679367746|
     * 1689131171|
     * 1689245209|
     * 1689255003|
     * 1689300181|
     * 1689645255|
     * 1689670109|
     * 1689673829|
     * 1689714025|
     * 1689733391|
     * 1690191601|
     * 1690240165|
     * 1690254172|
     * 1690318820|
     * 1690357355|
     * 1690750828|
     * 1690837209|
     * 1690923618|
     * 1690942220|
     * 1691442010|
     * 1691528409|
     * 1691547616|
     * 1692046808|
     * 1692133219|
     * 1692152173|
     * 1692339097|
     * 1692345624|
     * 1692392427|
     * 1692565224|
     * 1692584998|
     */

    @Test
    public void t110() {

    }


    @Test
    public void test3() {
        DeviceStockSupplyReq req = new DeviceStockSupplyReq();
        req.setGroupByDim(Lists.newArrayList(SupplyDimEnums.dimDemandArea, SupplyDimEnums.dimErpBiz));

        List<A> ls = new ArrayList<>();
        A a1 = new A();
        a1.v = "1";
        a1.v1 = BigDecimal.valueOf(4.0);
        a1.v2 = BigDecimal.valueOf(5.0);
        ls.add(a1);
        A a2 = new A();
        a2.v = "2";
        a2.v1 = BigDecimal.valueOf(1.0);
        ls.add(a2);
        DeviceStockSupplyRsp lsp = dealWith(ls, req.getGroupByDim(),
                (o) -> o.v.equals("1"),
                (o) -> o.v.equals("2"));
        System.out.println(JSON.toJsonFormatted(lsp));
    }


    public <T extends SupplyWrapper> DeviceStockSupplyRsp dealWith(List<T> data,
            List<SupplyDimEnums> groupDim,
            Predicate<? super T> v1Filter,
            Predicate<? super T> v2Filter) {

        val gMap = ListUtils.groupBy(data, (o) -> Strings.join("@",
                ListUtils.transform(groupDim, (s) -> s.getDimVal(o))));

        List<Data> dataList = gMap.entrySet().stream().map(ent -> {
            //用第一个条数据的信息初始化 该行的维度信息
            Data d = Data.form(groupDim, ent.getValue().get(0));
            List<T> v1 = ListUtils.filter(data, v1Filter);
            List<T> v2 = ListUtils.filter(data, v2Filter);
            // 指标处理
            val items = Arrays.stream(SupplyIndexEnums.values()).map(idx -> {
                IndexVal val = new IndexVal();
                ValItem pre = v1.stream().map(t -> idx.getVal(t)).filter(Objects::nonNull).reduce(IndexVal::add)
                        .orElse(null);
                ValItem af = v2.stream().map(t -> idx.getVal(t)).filter(Objects::nonNull).reduce(IndexVal::add)
                        .orElse(null);
                val.setPre(pre);
                val.setAfter(af);
                val.setDim(ent.getKey() + "#" + idx.getName());
                val.setName(idx.getName());
                return val;
            }).collect(Collectors.toList());

            d.setIndexItems(items);
            return d;
        }).collect(Collectors.toList());
        return new DeviceStockSupplyRsp(dataList, null);
    }


    static class A implements SupplyWrapper {

        public String name = "lily";

        public String v;

        public BigDecimal v1;
        public BigDecimal v2;

        @Override
        public String getErpBizType() {
            return "分类一";
        }

        @Override
        public ValItem getReturnLeft() {
            return new ValItem(BigDecimal.valueOf(10.0));
        }

        @Override
        public ValItem getPurchaseForecastFinal() {
            return new ValItem(v1);
        }

        @Override
        public ValItem getPurchaseForecast() {
            return new ValItem(v2);
        }
    }

    static class B implements SupplyWrapper {

        public String type = "b";

        @Override
        public String getErpBizType() {
            return "分类一";
        }

        @Override
        public ValItem getReturnLeft() {
            return new ValItem(BigDecimal.valueOf(10.0));
        }

        @Override
        public ValItem getPurchaseForecast() {
            return new ValItem(BigDecimal.valueOf(5.0));
        }

        @Override
        public ValItem getTotalLeft() {
            return SupplyWrapper.super.getTotalLeft();
        }
    }

}