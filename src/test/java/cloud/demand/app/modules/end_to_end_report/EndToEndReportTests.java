package cloud.demand.app.modules.end_to_end_report;

import cloud.demand.app.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.end_to_end_report.model.dto.ProductDemandPredictRateDTO;
import cloud.demand.app.modules.end_to_end_report.model.req.EndToEndReportQueryReq;
import cloud.demand.app.modules.end_to_end_report.model.vo.ProductDemandVersionItemVO;
import cloud.demand.app.modules.end_to_end_report.service.EndToEndReportGenDataService;
import cloud.demand.app.modules.end_to_end_report.service.EndToEndReportProductDemandService;
import cloud.demand.app.modules.end_to_end_report.service.EndToEndRrpService;
import cloud.demand.app.modules.end_to_end_report.service.EndToEndThirdPartService;
import cloud.demand.app.modules.xy_purchase_order.service.XyBaseInfoService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class EndToEndReportTests {

    @Autowired
    private EndToEndReportProductDemandService endToEndReportProductDemandService;
    @Autowired
    private EndToEndReportGenDataService genDataService;
    @Autowired
    private EndToEndRrpService endToEndRrpService;
    @Resource
    private DictService dictService;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private EndToEndThirdPartService thirdPartService;

    @Test
    public void test() {
        List<ProductDemandVersionItemVO> data = endToEndReportProductDemandService.queryItem(
                "2023-01", "2023-12",
                new EndToEndReportQueryReq());

        System.out.println(data);
    }

    @Test
    public void testObsInvoke(){
        int year = 2022;
        genDataService.genDeviceIncreaseData(year);
        genDataService.genStockReturnDeviceDetail(year);

    }

    @Test
    public void testPredictRate() throws Exception {
        EndToEndReportQueryReq req = new EndToEndReportQueryReq();
        req.setProductType("CVM");
        req.setBeginYearMonth(DateUtils.parse("2022-05-01"));
        req.setEndYearMonth(DateUtils.parse("2023-05-01"));
        ProductDemandPredictRateDTO rate = endToEndRrpService.queryPredictRate(req);
        System.out.println(rate);
    }

    @Test
    public void testDictService(){
        System.out.println(dictService.getCountryChineseByCityName("曼谷"));
    }


    @Resource
    private XyBaseInfoService xyBaseInfoService;
    @Test
    public void testGetPlanProduct(){
        List<CloudDemandCsigResourceViewCategoryDO> cvm = xyBaseInfoService.getPlanProducts("CVM");
//        List<CloudDemandCsigResourceViewCategoryDO> cvm = xyBaseInfoService.getPlanProducts("裸金属");
//        List<CloudDemandCsigResourceViewCategoryDO> cvm = xyBaseInfoService.getPlanProducts("CVM");

    }

    @Test
    public void testInvokeThirdPart(){
        EndToEndReportQueryReq req = new EndToEndReportQueryReq();
        req.setProductType("CVM");
        req.setBeginYearMonth(DateUtils.parse("2022-01-01"));
        req.setEndYearMonth(DateUtils.parse("2023-01-01"));

        thirdPartService.queryIndustryForecastPredicate(req, null);
    }
}
