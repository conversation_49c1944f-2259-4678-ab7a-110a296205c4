package cloud.demand.app.modules.repo_show;

import cloud.demand.app.modules.repo_show.service.DecisionViewService;
import cloud.demand.app.modules.repo_show.service.QueryRepoShowService;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;

@SpringBootTest
public class RepoShowTest {

    @Resource
    QueryRepoShowService repoShowService;
    @Resource
    DecisionViewService decisionViewService;

    private final String date = "2022-11-30";

    @Test
    public void test(){
        System.out.println(repoShowService.queryAllProductRepoShow(date));
    }

    public static void main(String[] args) {
        String start = "2022-09";
        String end = "2022-10";
        Date startDate = DateUtils.parse(start, "yyyy-MM");
        Date endDate = DateUtils.parse(end, "yyyy-MM");
        System.out.println(startDate);
        System.out.println(endDate);
    }

    @Test
    public void test2(){
        repoShowService.genNetworkInvCostInfo("2022-11-01");
    }

    @Test
    public void test3(){
        repoShowService.syncYgInventory(DateUtils.parse("2022-11-02"));
    }

    @Test
    public void test5(){
        decisionViewService.genCompanyInventory();
    }


    @Test
    public void test4(){
        decisionViewService.genCompanyInventory();
    }



}
