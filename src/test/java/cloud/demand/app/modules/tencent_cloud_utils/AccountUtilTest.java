package cloud.demand.app.modules.tencent_cloud_utils;

import cloud.demand.app.common.utils.CSVUtils;
import cloud.demand.app.common.utils.ObjUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdTxyAppidInfoCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.DiffWarZone;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuProductConfigDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuRegionZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil.AccountInfoDTO;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil.InstanceInfoDTO;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil.NewWarZoneDTO;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil.WarZoneDTO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import erp.base.cloudapi.Secret;
import erp.base.cloudapi.V3Api;
import erp.base.cloudapi.V3Request;
import erp.base.cloudapi.V3Response;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.client.JsonrpcClient;


@SpringBootTest
@Slf4j
public class AccountUtilTest {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private DictService dictService;

    @Resource
    private DBHelper prodReadOnlyCkStdCrpDBHelper;

    static ExecutorService executor = Executors.newFixedThreadPool(10);

    private MultipartFile getFile(String path) throws IOException {
        File file = ResourceUtils.getFile("classpath:" + path);
        return new MockMultipartFile(file.getName(), Files.newInputStream(file.toPath()));
    }

    @Test
    public void queryUinAccountInfoTest() {
        pplDictService.queryInfoByUin("************");
    }

    @Test
    public void queryWarZoneTest() {
        List<PplOrderDO> pplOrderDOS = demandDBHelper.getRaw(PplOrderDO.class,
                "select * from ppl_order where industry_dept = '智慧行业一部'"
                        + " and customer_uin is not null and customer_uin != '' group by customer_uin");
        List<DiffWarZone> list = new ArrayList<>();
        for (PplOrderDO pplOrderDO : pplOrderDOS) {
            NewWarZoneDTO warZoneDTO = AccountUtil.queryUinWarZoneNew(pplOrderDO.getCustomerUin(),
                    CommonUtil.defaultSecret());
            WarZoneDTO warZone = AccountUtil.queryUinWarZone(pplOrderDO.getCustomerUin(), CommonUtil.defaultSecret());
            if (!warZone.getLevelOneWarZoneName().equals(warZoneDTO.getZoneName())) {
                DiffWarZone diffWarZone = new DiffWarZone();
                diffWarZone.setCustomerUin(pplOrderDO.getCustomerUin());
                diffWarZone.setCustomerShortName(pplOrderDO.getCustomerShortName());
                diffWarZone.setBeforeWarZone(warZone.getLevelOneWarZoneName());
                diffWarZone.setAfterWarZone(warZoneDTO.getZoneName());
                list.add(diffWarZone);
            }
//            log.info("{}", JSON.toJson(warZoneDTO));
        }
        demandDBHelper.insertBatchWithoutReturnId(list);

    }

    public void queryWarZoneTestV2(){
        List<PplOrderDO> pplOrderDOS = demandDBHelper.getRaw(PplOrderDO.class,
                "select * from ppl_order where industry_dept = '智慧行业一部'"
                        + " and customer_uin is not null and customer_uin != '' group by customer_uin limit 1");
        for (PplOrderDO pplOrderDO : pplOrderDOS) {
            NewWarZoneDTO warZoneDTO = AccountUtil.queryUinWarZoneNew(pplOrderDO.getCustomerUin(),
                    CommonUtil.defaultSecret());
            WarZoneDTO warZone = AccountUtil.queryUinWarZone(pplOrderDO.getCustomerUin(), CommonUtil.defaultSecret());
            if (!warZone.getLevelOneWarZoneName().equals(warZoneDTO.getZoneName())) {
                DiffWarZone diffWarZone = new DiffWarZone();
                diffWarZone.setCustomerUin(pplOrderDO.getCustomerUin());
                diffWarZone.setCustomerShortName(pplOrderDO.getCustomerShortName());
                diffWarZone.setBeforeWarZone(warZone.getLevelOneWarZoneName());
                diffWarZone.setAfterWarZone(warZoneDTO.getZoneName());
            }
            log.info("{}", JSON.toJson(warZoneDTO));
        }
    }

    /**
     * 通过cid查询集团
     */
    @Test
    public void queryGroupInfo() {
        List<String> cidList = new ArrayList<>();
        cidList.add("48342b029e5597da22cb7bc8eea26812");
        String s = AccountUtil.queryGroupInfo(CommonUtil.defaultSecret(), cidList);
    }

    @Test
    public void initPplGpuRegionZoneData() {

        List<PplGpuRegionZoneDO> group_by_region = demandDBHelper.getAll(PplGpuRegionZoneDO.class, "group by region");
        List<String> regions = group_by_region.stream().map(PplGpuRegionZoneDO::getRegion).collect(Collectors.toList());
        Map<String, PplGpuProductConfigDO> gpuConfigMap = pplDictService.queryGpuConfigMap();
        List<String> instanceTypeList = new ArrayList<>();
        gpuConfigMap.forEach((k, v) -> {
            instanceTypeList.add(k);
        });

        Map<String, String> regionMap = dictService.getRegionMap();
        List<String> regionList = new ArrayList<>();
        regionMap.forEach((k, v) -> {
            if (!regions.contains(k)) {
                regionList.add(k);
            }
        });
        Map<String, String> zoneMap = dictService.getZoneMap();

        List<InstanceInfoDTO> allList = new ArrayList<>();
        for (String region : regionList) {
            for (String instanceType : instanceTypeList) {
                List<InstanceInfoDTO> list = AccountUtil.queryInstanceList(region, null,
                        instanceType,
                        CommonUtil.defaultSecret());
                if (list != null && !CollectionUtils.isEmpty(list)) {
                    for (InstanceInfoDTO instanceInfoDTO : list) {
                        PplGpuRegionZoneDO gpuRegionZoneDO = new PplGpuRegionZoneDO();
                        gpuRegionZoneDO.setRegion(region);
                        gpuRegionZoneDO.setRegionName(regionMap.get(region));
                        gpuRegionZoneDO.setZone(instanceInfoDTO.getZone());
                        gpuRegionZoneDO.setZoneName(zoneMap.get(instanceInfoDTO.getZone()));

                        gpuRegionZoneDO.setInstanceType(instanceType);
                        gpuRegionZoneDO.setInstanceModel(instanceInfoDTO.getInstanceType());
                        gpuRegionZoneDO.setCpuNum(instanceInfoDTO.getCpuCore());
                        gpuRegionZoneDO.setGpuNum(instanceInfoDTO.getGpuNum());
                        gpuRegionZoneDO.setGpuCount(instanceInfoDTO.getGpuCount());
                        gpuRegionZoneDO.setMemory(instanceInfoDTO.getMemory());
                        gpuRegionZoneDO.setGpuType(gpuConfigMap.get(instanceInfoDTO.getInstanceFamily()).getGpuType());
                        gpuRegionZoneDO.setGpuProductType(
                                gpuConfigMap.get(instanceInfoDTO.getInstanceFamily()).getGpuProductType());
                        demandDBHelper.insert(gpuRegionZoneDO);
                    }
                }
            }

        }

    }

    @Test
    public void querySingleInstanceListTest() {
        String region = "ap-nanjing";
        String instanceType = "PTX1";
        Map<String, PplGpuProductConfigDO> gpuConfigMap = pplDictService.queryGpuConfigMap();
        Map<String, String> regionMap = dictService.getRegionMap();
        Map<String, String> zoneMap = dictService.getZoneMap();
        List<InstanceInfoDTO> list = AccountUtil.queryInstanceList(region, null,
                instanceType,
                CommonUtil.defaultSecret());
        for (InstanceInfoDTO instanceInfoDTO : list) {
            PplGpuRegionZoneDO gpuRegionZoneDO = new PplGpuRegionZoneDO();
            gpuRegionZoneDO.setRegion(region);
            gpuRegionZoneDO.setRegionName(regionMap.get(region));
            gpuRegionZoneDO.setZone(instanceInfoDTO.getZone());
            gpuRegionZoneDO.setZoneName(zoneMap.get(instanceInfoDTO.getZone()));

            gpuRegionZoneDO.setInstanceType(instanceType);
            gpuRegionZoneDO.setInstanceModel(instanceInfoDTO.getInstanceType());
            gpuRegionZoneDO.setCpuNum(instanceInfoDTO.getCpuCore());
            gpuRegionZoneDO.setGpuNum(instanceInfoDTO.getGpuNum());
            gpuRegionZoneDO.setGpuCount(instanceInfoDTO.getGpuCount());
            gpuRegionZoneDO.setMemory(instanceInfoDTO.getMemory());
            gpuRegionZoneDO.setGpuType(gpuConfigMap.get(instanceInfoDTO.getInstanceFamily()).getGpuType());
            gpuRegionZoneDO.setGpuProductType(
                    gpuConfigMap.get(instanceInfoDTO.getInstanceFamily()).getGpuProductType());
            demandDBHelper.insert(gpuRegionZoneDO);
        }
        log.info("{}", JSON.toJson(list));
    }

    @Test
    public void queryIndustryTree() throws Exception {
        V3Request v3Request = new V3Request("clouddc", "DescribeIndustryTrees", "2018-08-30", null,
                ImmutableMap.of());
        v3Request.getService().setHost("clouddc.internal.tencentcloudapi.com");
        V3Api v3Api = new V3Api();
        IndustryTreeResp resp = v3Api.sendAndResponse(v3Request, CommonUtil.defaultSecret(), IndustryTreeResp.class)
                .get();
        String json = resp.jsonString;
        log.info("{}", json);
        BufferedWriter out = new BufferedWriter(new FileWriter("temp-industryTree.json"));
        out.write(json);
        out.close();
    }

    /**
     * 测试从appid拿uin
     */
    @Test
    public void testAppId2Uin() {
        List<String> appIdList = demandDBHelper.getRaw(String.class, "select distinct appid from ES0101");

        List<DwdTxyAppidInfoCfDO> all = prodReadOnlyCkStdCrpDBHelper.getAll(DwdTxyAppidInfoCfDO.class,
                "where appid in (?) ",
                appIdList);

        Map<Long, DwdTxyAppidInfoCfDO> collect = all.stream()
                .collect(Collectors.toMap(DwdTxyAppidInfoCfDO::getAppid, v -> v));

        collect.forEach((k, v) -> {
            demandDBHelper.executeRaw(
                    "update ES0101 set customer_short_name = ?"
                            + " where appid = ?",
                    v.getCustomerShortName(), k);
        });


    }

//    @Resource
//    PplDictService pplDictService;

    @Resource
    JsonrpcClient jsonrpcClient;

    @Test
    public void getData() throws ExecutionException, InterruptedException {
        List<List<String>> listFromCsv = CSVUtils.getListFromCsv("yaobu.csv");

        List<String> header = listFromCsv.remove(0);

//        ExecutorService executors = Executors.newFixedThreadPool(100);

        List<String> appids = listFromCsv.stream().map((o) -> o.get(0)).collect(Collectors.toList());
        appids = appids.stream()
                .distinct()
                .map((o) -> o.trim())
                .collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(appids, 200);

//        HashMap<String, String> appId2UinMap = partitions.stream()
//                .map((o) -> {
//                    HashMap<Object, Object> params = new HashMap<>();
//                    params.put("appid", o);
//                    JsonrpcReq req = jsonrpcClient.jsonrpc("baseGetAppId2UinMapData")
//                            .uri("http://11.134.157.75/cloud-demand-app/ppl13week?api_key=no")
//                            .id(UUID.randomUUID().toString())
//                            .params(params);
//                    Map<String, String> map = req.postForObject(Map.class);
//                    return map;
//                })
//                .reduce(new HashMap<>(),
//                        (r, m) -> {
//                            r.putAll(m);
//                            return r;
//                        },
//                        (r1, r2) -> r1);

        List<List<String>> ret = Lang.list();

        ExecutorService executors = Executors.newFixedThreadPool(100);

        ArrayList<Future<List<String>>> list = Lang.list();
        for (String appid : appids) {

            Future<List<String>> income_sales_desc = executors.submit(() -> {
                AccountInfoDTO accountInfoDTO = null;
                try {
                    accountInfoDTO =
                            AccountUtil.queryUinAccountInfo(appid, CommonUtil.defaultSecret(),
                                    AccountUtil.FULL_ACCOUNT_INFO_KEY_LIST);
                } catch (Exception ignore) {

                }

                String cid = "";
                String gid = "";
                String customerName = "";
                String customerShortName = "";
                String organizationName = "";
                String incomeSalesDesc = "";
                String industryName = "";
                String incomeIndustry1Name = "";
                String incomeIndustry2Name = "";
                String businessLineName = "";
                String regSrcTxt = "";
                if (accountInfoDTO != null) {

                    cid = accountInfoDTO.getField("account_info.cid", String.class);
                    gid = accountInfoDTO.getField("account_info.mdmid", String.class);

                    customerName = accountInfoDTO.getField("customer_info.name", String.class);
                    customerShortName = accountInfoDTO.getField("customer_info.sname", String.class);
                    organizationName = accountInfoDTO.getField("organization_info.organization_name", String.class);
                    incomeSalesDesc = accountInfoDTO.getField("income_sales_desc", String.class);
                    industryName = accountInfoDTO.getField("register_industry.industry_name", String.class);
                    incomeIndustry1Name = accountInfoDTO.getField("t_income_industry.t_income_industry1_name",
                            String.class);
                    incomeIndustry2Name = accountInfoDTO.getField("t_income_industry.t_income_industry2_name",
                            String.class);
                    businessLineName = accountInfoDTO.getField("income_industry.business_line_name",
                            String.class);
//                    regSrcTxt = accountInfoDTO.getField("register_info.reg_src_txt",
//                            String.class);
                }
                List<String> one = Lang.list();
                one.add(appid);
                one.add(appid);

                one.add(cid);
                one.add(gid);

                one.add(customerName);
                one.add(customerShortName);
                one.add(organizationName);
                one.add(incomeSalesDesc);
                one.add(industryName);
                one.add(incomeIndustry1Name);
                one.add(incomeIndustry2Name);
                one.add(businessLineName);
                one.add(regSrcTxt);
                return one;
            });
            list.add(income_sales_desc);
        }

        List<String> headers = Lang.list("appid", "uin", "cid", "gid", "customerName", "customerShortName",
                "organizationName",
                "incomeSalesDesc", "industryName", "incomeIndustry1Name", "incomeIndustry2Name", "businessLineName",
                "regSrcTxt");

        int cnt = 0;
        for (Future<List<String>> listFuture : list) {
            if (cnt++ % 500 == 0) {
                System.out.println("****************************" + cnt);
            }
            List<String> strings = listFuture.get();
            ret.add(strings);
        }

        ArrayList<List<String>> output = Lang.list(headers);
        output.addAll(ret);

        CSVUtils.writeCsv("/Users/<USER>/Desktop/data4.csv", output);
    }

    @Test
    public void testGetCustomerType1() {
//        AccountInfoDTO accountInfoDTO =
//                AccountUtil.queryUinAccountInfo("************", CommonUtil.defaultSecret(),
//                        AccountUtil.FULL_ACCOUNT_INFO_KEY_LIST);
        NewWarZoneDTO warZoneDTO = AccountUtil.queryUinWarZoneNew("************",
                CommonUtil.defaultSecret());
    }

    @Test
    public void testGetCustomerType() {

        Map<String, String> data = AccountUtil.baseGetAppId2UinMapData(Lang.list("************"));

        AccountInfoDTO accountInfoDTO =
                AccountUtil.queryUinAccountInfo("************", CommonUtil.defaultSecret(),
                        AccountUtil.FULL_ACCOUNT_INFO_KEY_LIST);
        Integer customerType = accountInfoDTO.getField("account_info.customer_type", Integer.class);
        System.out.println(customerType); // 0 个人用户， 1企业
    }

    @Test
    public void testAll(){
        log.info("1.testGetInfo -- start");
        testGetInfo();
        log.info("1.testGetInfo -- end");
        log.info("2.queryGroupInfo -- start");
        queryGroupInfo();
        log.info("2.queryGroupInfo -- end");
        log.info("3.testGetCorpInfo -- start");
        testGetCorpInfo();
        log.info("3.testGetCorpInfo -- end");
        log.info("4.queryWarZoneTestV2 -- start");
        queryWarZoneTestV2();
        log.info("4.queryWarZoneTestV2 -- end");
    }

    @Test
    public void testWriteIndustry() {
        List<Long> appid = demandDBHelper.getRaw(Long.class, "select appid from tmp_appid_industry where industry=''");
        ListUtils.forEach(appid, id -> {
            Map<String, String> appId2UinMap =
                    AccountUtil.getAppId2UinMap(ListUtils.newList(String.valueOf(id)));
            if (appId2UinMap != null && !appId2UinMap.isEmpty()) {
                String uin = appId2UinMap.get(id.toString());
                if (StringTools.isNotBlank(uin)) {
                    AccountInfoDTO accountInfoDTO =
                            AccountUtil.queryUinAccountInfo(uin, CommonUtil.defaultSecret(),
                                    ListUtils.newList("register_industry"));
                    String industry = accountInfoDTO.getField("register_industry.industry_name", String.class);
                    if (StringTools.isNotBlank(industry)) {
                        demandDBHelper.executeRaw("update tmp_appid_industry set industry=? where appid=?",
                                industry, id);
                    }
                }
            }
        });
    }

    @Test
    public void testWriteAppid3() {
        List<Long> uins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info2 where app_id is null");
        ListUtils.forEach(uins, uin -> {
            AccountInfoDTO accountInfoDTO =
                    AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                            ListUtils.newList("register_info"));
            String appId = accountInfoDTO.getField("register_info.appId", String.class);
            if (StringTools.isNotBlank(appId)) {
                demandDBHelper.executeRaw("update tmp_uin_info2 set app_id=? where uin=?",
                        appId, uin);
            }
        });
    }

    @Test
    public void testWriteCustomerName3() {
        List<Long> uins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info2 where customer_name=''");
        ListUtils.forEach(uins, uin -> {
            AccountInfoDTO accountInfoDTO =
                    AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                            ListUtils.newList("customer_info"));
            String sname = accountInfoDTO.getField("customer_info.sname", String.class);
            if (StringTools.isNotBlank(sname)) {
                demandDBHelper.executeRaw("update tmp_uin_info2 set customer_name=? where uin=?",
                        sname, uin);
            }
        });
    }

    @Test
    public void testWriteIndustry3() {
        List<Long> uins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info2 where industry=''");
        ListUtils.forEach(uins, uin -> {
            AccountInfoDTO accountInfoDTO =
                    AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                            ListUtils.newList("register_industry"));
            String industry = accountInfoDTO.getField("register_industry.industry_name", String.class);
            if (StringTools.isNotBlank(industry)) {
                demandDBHelper.executeRaw("update tmp_uin_info2 set industry=? where uin=?",
                        industry, uin);
            }
        });
    }

    @Test
    public void testWriteIndustry2() {
        List<Long> uins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info where industry=''");
        ListUtils.forEach(uins, uin -> {
            AccountInfoDTO accountInfoDTO =
                    AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                            ListUtils.newList("register_industry"));
            String industry = accountInfoDTO.getField("register_industry.industry_name", String.class);
            if (StringTools.isNotBlank(industry)) {
                demandDBHelper.executeRaw("update tmp_uin_info set industry=? where uin=?",
                        industry, uin);
            }
        });
    }

    @Test
    public void testWriteCid2() {
        List<Long> uins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info where cid=''");
        ListUtils.forEach(uins, uin -> {
            AccountInfoDTO accountInfoDTO =
                    AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                            ListUtils.newList("account_info"));
            String cid = accountInfoDTO.getField("account_info.cid", String.class);
            if (StringTools.isNotBlank(cid)) {
                demandDBHelper.executeRaw("update tmp_uin_info set cid=? where uin=?",
                        cid, uin);
            }
        });
    }

    @Test
    public void testGetInfo() {
        AccountInfoDTO accountInfoDTO =
                AccountUtil.queryUinAccountInfo("**********", CommonUtil.defaultSecret(),
                        AccountUtil.FULL_ACCOUNT_INFO_KEY_LIST);
        System.out.println("2023-02-21");
        System.out.println(accountInfoDTO);
    }

    @Test
    public void testGetCorpInfo() {
        String cid = "904483638c53fa7b69cacb5de6852ba2";
        AccountUtil.CorpInfoDTO corpInfoDTO = AccountUtil.queryCorpCustomer(cid, CommonUtil.defaultSecret());
        System.out.println(corpInfoDTO);
    }

    @Test
    public void testWriteIndustryDept() {
        List<Long> appid = demandDBHelper.getRaw(Long.class,
                "select appid from tmp_appid_industry where industry_dept=''");
        ListUtils.forEach(appid, id -> {
            Map<String, String> appId2UinMap =
                    AccountUtil.getAppId2UinMap(ListUtils.newList(String.valueOf(id)));
            if (appId2UinMap != null && !appId2UinMap.isEmpty()) {
                String uin = appId2UinMap.get(id.toString());
                if (StringTools.isNotBlank(uin)) {
                    AccountInfoDTO accountInfoDTO =
                            AccountUtil.queryUinAccountInfo(uin, CommonUtil.defaultSecret(),
                                    //AccountUtil.FULL_ACCOUNT_INFO_KEY_LIST);
                                    ListUtils.newList("organization_info"));
                    String industryDept = accountInfoDTO.getField("organization_info.organization_name", String.class);
                    if (StringTools.isNotBlank(industryDept)) {
                        demandDBHelper.executeRaw("update tmp_appid_industry set industry_dept=? where appid=?",
                                industryDept, id);
                    }
                }
            }
        });
    }


    @Test
    public void testWriteIndustryDept3() {
        List<Long> uins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info2 where industry_dept=''");
        ListUtils.forEach(uins, uin -> {
            AccountInfoDTO accountInfoDTO =
                    AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                            ListUtils.newList("organization_info"));
            String industryDept = accountInfoDTO.getField("organization_info.organization_name", String.class);
            if (StringTools.isNotBlank(industryDept)) {
                demandDBHelper.executeRaw("update tmp_uin_info2 set industry_dept=? where uin=?",
                        industryDept, uin);
            }
        });
    }

    @Test
    public void testWriteIncomeSalesDesc3() {
        List<Long> uins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info2 where income_sales_desc=''");
        ListUtils.forEach(uins, uin -> {
            AccountInfoDTO accountInfoDTO =
                    AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                            ListUtils.newList("income_sales_desc"));
            String incomeSalesDesc = accountInfoDTO.getField("income_sales_desc", String.class);
            if (StringTools.isNotBlank(incomeSalesDesc)) {
                demandDBHelper.executeRaw("update tmp_uin_info2 set income_sales_desc=? where uin=?",
                        incomeSalesDesc, uin);
            }
        });
    }

    @Test
    public void testWriteWarZone() {
        List<Long> appid = demandDBHelper.getRaw(Long.class, "select appid from tmp_appid_industry where war_zone=''");
        ListUtils.forEach(appid, id -> {
            Map<String, String> appId2UinMap =
                    AccountUtil.getAppId2UinMap(ListUtils.newList(String.valueOf(id)));
            if (appId2UinMap != null && !appId2UinMap.isEmpty()) {
                String uin = appId2UinMap.get(id.toString());
                if (StringTools.isNotBlank(uin)) {
                    WarZoneDTO warZoneDTO = AccountUtil.queryUinWarZone(uin, CommonUtil.defaultSecret());
                    String warZone = warZoneDTO == null ? "" : warZoneDTO.getLevelOneWarZoneName();
                    if (StringTools.isNotBlank(warZone)) {
                        demandDBHelper.executeRaw("update tmp_appid_industry set war_zone=? where appid=?",
                                warZone, id);
                    }
                }
            }
        });
    }

    public void addSales() throws Exception {
        // 读取excel
        MultipartFile multipartFile = getFile("excel/uin-source.xlsx");
        List<UinWrap> result = new ArrayList<>();
        EasyExcel.read(multipartFile.getInputStream(), UinWrap.class, new AnalysisEventListener<UinWrap>() {
            @Override
            public void invoke(UinWrap data, AnalysisContext context) {
                if (ObjUtils.allFieldIsNull(data)) {
                    log.info("读到第一个空行，结束");
                    throw new ExcelAnalysisStopException();
                }
                result.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet(2).headRowNumber(1).doRead();
        // 并发获取主销售
        ExecutorService executors = Executors.newFixedThreadPool(100);
        Secret secret = CommonUtil.defaultSecret();
        List<String> fields = ListUtils.newList("customer_info");
        List<Future<WriteUinWrap>> futures = new ArrayList<>();
        for (UinWrap uinWrap : result) {
            Future<WriteUinWrap> f = executors.submit(() -> {
                String uin = uinWrap.uin;
                if (StringUtils.isBlank(uin)) {
                    return null;
                }
                AccountInfoDTO dto = AccountUtil.queryUinAccountInfo(uin, secret, fields);
                WriteUinWrap writeUinWrap = new WriteUinWrap();
                writeUinWrap.mainSales = dto.getField("customer_info.business_manager", String.class);
                writeUinWrap.checkUin = uin;
                return writeUinWrap;
            });
            futures.add(f);
        }
        List<WriteUinWrap> resp = new ArrayList<>();
        for (Future<WriteUinWrap> f : futures) {
            WriteUinWrap writeUinWrap = f.get();
            if (writeUinWrap == null) {
                writeUinWrap = new WriteUinWrap();
                writeUinWrap.checkUin = "";
                writeUinWrap.mainSales = "";
            }
            resp.add(writeUinWrap);
        }
        FileWriter fw = new FileWriter("temp-sales.csv");
        PrintWriter pw = new PrintWriter(fw);
        for (WriteUinWrap r : resp) {
            pw.println(r.checkUin + "," + r.mainSales);
        }
        pw.flush();
        pw.close();
    }

    @Data
    public static class UinWrap {

        @ExcelProperty(index = 7)
        private String uin;
    }

    @Data
    public static class WriteUinWrap {

        @ExcelProperty("UIN")
        private String checkUin;
        @ExcelProperty("主销售")
        private String mainSales;
    }

    @Data
    static class IndustryTreeResp extends V3Response {

        @JsonAlias("JsonString")
        private String jsonString;
    }

    /////////////////////////////////// tmp_uin_info3

    @Test
    public void tmp_uin_info3() {
        List<Long> totalUins = demandDBHelper.getRaw(Long.class, "select uin from tmp_uin_info3");

        List<List<Long>> lists = ListUtils.groupByNum(totalUins, 100);
        for (List<Long> uins : lists) {
            List<TmpUinInfo4DO> result = ListUtils.transform(uins, uin -> {
                TmpUinInfo4DO d = new TmpUinInfo4DO();
                d.setUin(uin);

                AccountInfoDTO accountInfoDTO =
                        AccountUtil.queryUinAccountInfo(uin.toString(), CommonUtil.defaultSecret(),
                                ListUtils.newList("income_sales_desc", "register_info", "customer_info",
                                        "register_industry", "organization_info", "account_info"));

                String appId = accountInfoDTO.getField("register_info.appId", String.class);
                d.setAppId(NumberUtils.parseLong(appId));

                String sname = accountInfoDTO.getField("customer_info.sname", String.class);
                d.setCustomerName(sname);

                String industry = accountInfoDTO.getField("register_industry.industry_name", String.class);
                d.setIndustry(industry);

                String industryDept = accountInfoDTO.getField("organization_info.organization_name", String.class);
                d.setIndustryDept(industryDept);

                String incomeSalesDesc = accountInfoDTO.getField("income_sales_desc", String.class);
                d.setIncomeSalesDesc(incomeSalesDesc);

                Integer customerType = accountInfoDTO.getField("account_info.customer_type", Integer.class);
                if (customerType != null) {
                    d.setCustomerType(customerType.toString());
                }

                return d;
            });

            demandDBHelper.insertBatchWithoutReturnId(result);
        }
    }


    @Data
    @ToString
    @Table("tmp_uin_info4")
    public static class TmpUinInfo4DO {

        @Column(value = "uin")
        private Long uin;

        @Column(value = "app_id")
        private Long appId;

        /**
         * 客户名称<br/>Column: [customer_name]
         */
        @Column(value = "customer_name")
        private String customerName;

        /**
         * 行业<br/>Column: [industry]
         */
        @Column(value = "industry")
        private String industry;

        /**
         * 行业部门<br/>Column: [industry_dept]
         */
        @Column(value = "industry_dept")
        private String industryDept;

        /**
         * <br/>Column: [income_sales_desc]
         */
        @Column(value = "income_sales_desc")
        private String incomeSalesDesc;

        /**
         * <br/>Column: [customer_type]
         */
        @Column(value = "customer_type")
        private String customerType;

    }
}
