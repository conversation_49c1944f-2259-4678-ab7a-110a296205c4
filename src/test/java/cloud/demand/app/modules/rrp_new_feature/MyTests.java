package cloud.demand.app.modules.rrp_new_feature;

import cloud.demand.app.modules.rrp_new_feature.service.ProductStdCrpService;
import com.pugwoo.wooutils.json.JSON;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class MyTests {

    @Autowired
    private ProductStdCrpService productStdCrpService;

    @Test
    public void testCalendar2VersionMap() {
        Map r = productStdCrpService.buildLocalDate2VersionMap();
        log.info(JSON.toJson(r));
    }

}
