package cloud.demand.app.modules;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.dto.dict.ZoneFilterDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplIndustryPackageBaseDataDO;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryPackageBaseDataService;
import com.google.common.collect.ImmutableList;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mvel2.MVEL;
import org.nutz.lang.Lang;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class CommonTests {

    @Resource
    private DictService dictService;

    @Resource
    private PplIndustryPackageBaseDataService pplIndustryPackageBaseDataService;

    @Resource
    private DBHelper demandDBHelper;
    
    @Resource
    private PplDictService pplDictService;

    @Test
    public void test() {
        Integer deviceLogicCpuCore = dictService.getDeviceLogicCpuCore(
                "Y0-M10-30_RS2T", true);
        System.out.println(deviceLogicCpuCore);

        BigDecimal aaa = dictService.getDeviceCbsStore("aaa");
        System.out.println(aaa);

        BigDecimal bbb = dictService.getDeviceCbsStore("Y0-SW13-25G");
        System.out.println(bbb);
    }

    @Test
    public void fix(){
        List<PplIndustryPackageBaseDataDO> all = demandDBHelper.getAll(PplIndustryPackageBaseDataDO.class);
        pplIndustryPackageBaseDataService.fillInstanceModel(all,64, 256);
        demandDBHelper.update(all);
    }

    @Test
    public void test2() {
        assert dictService.getDeviceGpuCard("T0-GR11X-25G") == 16;
        assert dictService.getDeviceGpuCard("Y0-GF31-25G") == 0;

        assert dictService.getPlanDeviceLogicCore("Y0-DK52-25G") == 11200;
        assert dictService.getPlanDeviceLogicCore("Y0-M20-00") == 3200;

        assert dictService.getComputeType("T0-GR11X-25G").equals("GPU");
        assert dictService.getComputeType("Y0-GF31-25G").equals("CPU");
    }

    @Test
    public void testCampusToZone() {
        Map<String, Long> zoneIdByCampusName = dictService.getZoneIdByCampusName();
        System.out.println(JSON.toJson(zoneIdByCampusName));
    }

    @Test
    public void testMvel() {
        ZoneFilterDTO dto = new ZoneFilterDTO();
        dto.setRegionStrId(ImmutableList.of("1", "2", "3"));
        dto.setRegionName(ImmutableList.of());
        Object[] objects = new Object[]{dto};
        String str = "args[0].regionStrId + args[0].regionName + args[0].regionShortName";
        Map<String, Object> context = new HashMap<>();
        context.put("args", objects); // 类型是Object[]
        Object result = MVEL.eval(str, context);
        if (result != null) { // 返回结果为null等价于keyScript为空字符串
            System.out.println(result);
        }
    }

    public void test3(){
        String deviceType1 = "";
        String deviceType2 = "M1";
        String deviceType3 = "Y0-MS52A-25G";
        String deviceType4 = "Y0-MS52A-25G_FORMWAIT";
        ArrayList<String> devices = Lang.list(deviceType1, deviceType2, deviceType3, deviceType4);
        for (String device : devices) {
            System.out.println(device.split("_")[0]);
        }
    }

    @Test
    public void testDateUtils(){
        System.out.println(DateUtils.parse("2023-1", "yyyy-MM"));
    }
}
