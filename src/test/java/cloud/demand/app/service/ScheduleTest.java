package cloud.demand.app.service;


import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.ClassRelativeResourceLoader;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;

//@SpringBootTest
//@Slf4j
public class ScheduleTest {
    public static void main(String[] args) throws ClassNotFoundException {
        Map<String, List<ScheduleInfo>> allSchedule = getAllSchedule();
        System.out.println(allSchedule.size());
    }

    @SneakyThrows
    public static Map<String, List<ScheduleInfo>> getAllSchedule(){
        ClassPathScanningCandidateComponentProvider provider = new ClassPathScanningCandidateComponentProvider(false) {
            protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {

                return beanDefinition.getMetadata().isIndependent() && !beanDefinition.getMetadata().isAnnotation();
            }
        };
        provider.setResourceLoader(new ClassRelativeResourceLoader(CkDBUtils.class));
        Map<String, List<ScheduleInfo>> map = new LinkedHashMap<>();
        Set<BeanDefinition> candidateComponents = provider.findCandidateComponents("cloud.demand.app.modules");
        if (!CollectionUtils.isEmpty(candidateComponents)){
            for (BeanDefinition component : candidateComponents) {
                Class<?> aClass = Class.forName(component.getBeanClassName());
                List<ScheduleInfo> ls = new ArrayList<>();
                for (Field declaredField : aClass.getDeclaredFields()) {
                    Scheduled scheduled = declaredField.getAnnotation(Scheduled.class);
                    if (scheduled!=null){
                        ScheduleInfo scheduleInfo = new ScheduleInfo();
                        scheduleInfo.setScheduled(scheduled);
                        scheduleInfo.setClassName(aClass.getName());
                        scheduleInfo.setMethodName(declaredField.getName());
                        scheduleInfo.setTaskLog(declaredField.getAnnotation(TaskLog.class));
                        scheduleInfo.setASynchronized(declaredField.getAnnotation(Synchronized.class));
                        ls.add(scheduleInfo);
                    }
                }
                if (!ls.isEmpty()){
                    map.put(aClass.getName(),ls);
                }
            }
        }
        return map;
    }

    @Data
    public static class ScheduleInfo{
        private String className;
        private String methodName;
        private TaskLog taskLog;
        private Synchronized aSynchronized;
        private Scheduled scheduled;
    }
}
