package cloud.demand.app.service;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Map;

@SpringBootTest
@Slf4j
public class GroupTest {

    @Resource
    PplDictService pplDictService;
    @Resource
    DictService dictService;
    @Resource
    YunxiaoAPIService yunxiaoAPIService;

    @Test
    public void test() {
//        String[] names = new String[]{
//
//        };
//        for (String name : names) {
//            List<String> r = pplDictService.queryIndustryDept(name);
//            System.out.println(name + "," + r.get(0) + "," + r.get(1));
//        }

//        BaseDataDTO<MainInstanceTypeDTO> rawResult = pplDictService.queryMainInstanceType();
//        List<MainInstanceTypeDTO> container = rawResult.getData();
//        Set<String> zoneNames = new HashSet<>();
//        Set<String> instanceFamilies = new HashSet<>();
//        if (!CollectionUtils.isEmpty(container)) {
//            for (MainInstanceTypeDTO dto : container) {
//                if (!StringUtils.isBlank(dto.getZoneName())) {
//                    zoneNames.add(dto.getZoneName());
//                }
//                if (!StringUtils.isBlank(dto.getInstanceFamily())) {
//                    instanceFamilies.add(dto.getInstanceFamily());
//                }
//            }
//        }
//        log.info("zoneNames: {}", zoneNames);
//        log.info("instanceFamilies: {}", instanceFamilies);
         pplDictService.syncYunxiaoInstanceConfig();
        // Map<String, Set<String>> map = pplDictService.queryDeviceType2InstanceTypeSetMap();
        // log.info("{}", JSON.toJson(map));
        Map<String, String> map = dictService.getCsigDeviceTypeToInstanceTypeMap();
        log.info("{}", JSON.toJson(map));
    }
}
