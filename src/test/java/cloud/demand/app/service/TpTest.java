package cloud.demand.app.service;

import cloud.demand.app.modules.common.service.SyncOuterService;
import cloud.demand.app.modules.p2p.ppl13week.dto.RiskZoneDTO;
import cloud.demand.app.modules.sop.domain.http.SopDataVersionRes;
import cloud.demand.app.modules.sop.http.domain.idc_rm.IdcSupplyRiskReq;
import cloud.demand.app.modules.sop.http.service.IdcrmHttpService;
import cloud.demand.app.modules.sop.http.service.SopApiHttpService;
import cloud.demand.app.modules.sop.util.MDCUtil;
import cloud.demand.app.modules.sop.util.SpanUtil;
import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.Tracer;
import java.util.List;
import java.util.Random;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class TpTest {
    @Resource
    SpanUtil spanUtil;
    @Resource
    Tracer tracer;

    @Resource
    protected SopApiHttpService sopService;

    @Resource
    SyncOuterService syncOuterService;

    @Resource
    IdcrmHttpService idcrmHttpService;

    @Test
    public void test() {
        Random random = new Random();
        String logTraceIdPrefix = "crp@sop@ads@" + random.nextInt(100);
        // 开启一个 SPAN 新建调用链
        Span span = spanUtil.createSpan(logTraceIdPrefix);
        // MDC 设置新 traceId
        String logTraceId = logTraceIdPrefix + "@" + span.context().toTraceId();
        try (Scope scope = spanUtil.active(span)) {
            MDCUtil.setTraceId(logTraceId);
            Span span1 = tracer.activeSpan();
            log.info("equals: {}", span.equals(span1));
            List<SopDataVersionRes> allHedgingDataVersion = sopService.getAllHedgingDataVersion();
            log.info("logTraceId: {}", logTraceId);
        } finally {
            span.finish();
        }
    }

    @Test
    public void testIdc() {
//        List<CloudZoneDO> cloudZoneDOS = syncOuterService.syncCLoudZone();

        List<RiskZoneDTO> objects = idcrmHttpService.checkIdcSupplyRisk(new IdcSupplyRiskReq());
        System.out.println(1);
    }
}
