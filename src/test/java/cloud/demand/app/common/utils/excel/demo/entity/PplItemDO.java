package cloud.demand.app.common.utils.excel.demo.entity;

import cloud.demand.app.common.excel.checker.WhetherOrNotChecker;
import cloud.demand.app.common.excel.convert.WhetherOrNotConverter;
import cloud.demand.app.common.excel.core.annotation.DotExcelField;
import cloud.demand.app.common.utils.excel.demo.checker.DiscountChecker;
import cloud.demand.app.common.utils.excel.demo.constant.TestGroupConstant;
import java.math.BigDecimal;

public class PplItemDO {

    /**
     *  包销时长（月）（仅产品=GPU，且卡型=A800、H800时展示包销时长必填）
     */
    @DotExcelField(group = {TestGroupConstant.DEFAULT_GPU_IMPORT}, excelColumnName = "包销时长(月)")
    private Integer saleDuration;

    /**
     *  是否CPQ（GPU相关）
     */
    @DotExcelField(group = {TestGroupConstant.DEFAULT_GPU_IMPORT}, excelColumnName = "是否CPQ",
            converter = WhetherOrNotConverter.class, valueCheckers = WhetherOrNotChecker.class)
    private Boolean cpq;

    /**
     *  申请折扣(折)（如果是否CPQ=是，则申请折扣必填）
     */
    @DotExcelField(group = {TestGroupConstant.DEFAULT_GPU_IMPORT}, excelColumnName = "申请折扣(折)",
            valueCheckers = DiscountChecker.class)
    private BigDecimal applyDiscount;

    public Integer getSaleDuration() {
        return saleDuration;
    }

    public void setSaleDuration(Integer saleDuration) {
        this.saleDuration = saleDuration;
    }

    public Boolean getCpq() {
        return cpq;
    }

    public void setCpq(Boolean cpq) {
        this.cpq = cpq;
    }

    public BigDecimal getApplyDiscount() {
        return applyDiscount;
    }

    public void setApplyDiscount(BigDecimal applyDiscount) {
        this.applyDiscount = applyDiscount;
    }
}
