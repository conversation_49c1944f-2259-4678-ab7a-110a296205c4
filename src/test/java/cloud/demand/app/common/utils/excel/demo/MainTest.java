package cloud.demand.app.common.utils.excel.demo;

import cloud.demand.app.common.excel.DotExcelReadUtil;
import cloud.demand.app.common.excel.DotExcelReadUtil.DotExcelBuilder;
import cloud.demand.app.common.excel.checker.WhetherOrNotChecker;
import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ExcelFieldInfo;
import cloud.demand.app.common.excel.core.ExcelGroupFieldHelper;
import cloud.demand.app.common.excel.core.ExtendInterfaceCreatorImpl;
import cloud.demand.app.common.excel.core.ReadResult;
import cloud.demand.app.common.utils.excel.demo.checker.DiscountChecker;
import cloud.demand.app.common.utils.excel.demo.checker.MoreInfoVoChecker;
import cloud.demand.app.common.utils.excel.demo.constant.TestExcelGroupEnum;
import cloud.demand.app.common.utils.excel.demo.entity.PplItemDO;
import cloud.demand.app.common.utils.excel.demo.vo.MoreInfoVo;
import cloud.demand.app.common.utils.excel.demo.vo.PplItemVO;
import com.alibaba.excel.converters.AutoConverter;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

public class MainTest {

    private static final String path = "src/test/resources/excel/dot导入测试.xlsx";

    /**
     *   准备工作，此处测试用例手动注册 <br/>
     *   spring 自动注册的代码实现已完成 {@link cloud.demand.app.common.config.DotExcelAutoConfig}
     */
    @BeforeAll
    public static void before() {
        // 先注册指定的相关扩展接口的创建器，不注册默认就是 ExtendInterfaceCreatorImpl
        // 如果有自行扩展的创建器，可在使用此方法注册来替换掉默认值
        // 在项目中也可以在spring启动时自动注册，后续可以与上面的字段注册用同一个 spring starter 来完成
        ExcelGroupFieldHelper.registerExtendInterfaceCreator(new ExtendInterfaceCreatorImpl());

        // 这里先统一手动注册excel字段信息
        // 在项目中可用使用spring扫描在项目启动时自动注册所有，后续可以写一个 spring starter 来完成
        ExcelGroupFieldHelper.registerExcelFieldClass(PplItemDO.class);

    }


    /**
     *   使用excel组的统一模版(之前通过注解注册的excel字段信息{@link  PplItemDO})来完成解析
     */
    @Test
    public void test_1() {
        ReadResult<PplItemVO> result = DotExcelReadUtil.read(TestExcelGroupEnum.DEFAULT_GPU_IMPORT, //所属的 excel 组，会根据这个组取获取所有此组的对应注册字段信息
                PplItemVO.class, // 需要返回的数据类型
                path,
                2 // 表头所在的行，用于获取到表头之后，根据注册字段信息的映射关系找到此表头对应的 java 字段信息
        );
        println(result);
    }

    /**
     *   使用 builder 模式来完成更个性化的解析
     */
    @Test
    public void test_2() {
        // 先使用基础参数构建
        DotExcelBuilder<MoreInfoVo> builder = DotExcelReadUtil.createBuilder(MoreInfoVo.class, // 需要返回的数据类型
                TestExcelGroupEnum.DEFAULT_GPU_IMPORT, // 所属的 excel 组，会根据这个组取获取所有此组的对应注册字段信息
                2 // 表头所在的行，用于获取到表头之后，根据注册字段信息的映射关系找到此表头对应的 java 字段信息
        );
        // 用于获取 getter方法
        MoreInfoVo demo = new MoreInfoVo();
        MoreInfoVoChecker customChecker = new MoreInfoVoChecker();
        // 注册一个字段映射信息，虽然在 PplItemDO 中已经通过注解指定了 ApplyDiscount 的字段映射信息，但业务代码中手动注册的优先级更高，
        // 此处会覆盖掉之前自动注册的 ApplyDiscount 的字段映射信息，仅在当前解析过程中覆盖，不影响其他地方。
        // 可指定 ExcelFieldInfo 中更多属性来增强对某个字段的解析、校验，此处简单示例将另一表头数据赋值给 ApplyDiscount
        builder.registerFieldInfo(new ExcelFieldInfo(demo::getApplyDiscount, "单台内存(GB)", new DiscountChecker()))
                // 由于之前自动注册的的字段映射关系中没有 zoneName 的信息，可在此处手动添加(仅会添加到当前解析过程中,如需要全局应用还是使用注解自动注册)
                .registerFieldInfo(new ExcelFieldInfo(demo::getZoneName, "可用区"))
                // 如果不想改表某个字段的全局的数据转换器，只是针对当前业务需要改变一下，可用在此处进行该字段的数据转换器注册并覆盖掉原有的转换器
                .registerFieldConvertByGetter(demo::getSaleDuration, new AutoConverter())
                // 如果不想改表某个字段的全局的数据校验器，只是针对当前业务需要改变一下，可用在此处进行该字段的数据校验器注册并覆盖掉原有的校验器
                .registerValueCheckerByGetter(demo::getSaleDuration, new WhetherOrNotChecker())
                // 此处针对当前业务注册一个行数据校验器，在实际项目中建议通过 需要校验的数据的类型 或者 excel组 获取对应行数据校验器(做一个统一的管理)
                .registerRowChecker(customChecker)
                // 注册一个结果集校验器
                .registerResultChecker(customChecker)
                // 添加上下文，可在解析过程中进行数据校验时获取(ParseContext.getCustomContext())，一般用于解析业务个性化问题
                .registerContext(MoreInfoVoChecker.CONTEXT_KEY_DEPT, "智慧行业一部")
                .excelPath(path);

        // 根据 builder 进行excel解析
        ReadResult<MoreInfoVo> result = DotExcelReadUtil.read(builder);
        println(result);
    }

    public static void println(ReadResult<?> result) {
        System.out.println("************ check result ***************");
        for (ErrorMessage error : result.getErrors()) {
            System.out.println(error);
        }
        System.out.println("************ result data ***************");
        for (Object datum : result.getData()) {
            System.out.println(datum);
        }
    }

}
