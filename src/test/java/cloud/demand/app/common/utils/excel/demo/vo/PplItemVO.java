package cloud.demand.app.common.utils.excel.demo.vo;

import java.math.BigDecimal;

public class PplItemVO {

    /**
     *  包销时长（月）（仅产品=GPU，且卡型=A800、H800时展示包销时长必填）
     */
    private Integer saleDuration;

    /**
     *  是否CPQ（GPU相关）
     */
    private Boolean cpq;

    /**
     *  申请折扣(折)（如果是否CPQ=是，则申请折扣必填）
     */
    private BigDecimal applyDiscount;

    public Integer getSaleDuration() {
        return saleDuration;
    }

    public void setSaleDuration(Integer saleDuration) {
        this.saleDuration = saleDuration;
    }

    public Boolean getCpq() {
        return cpq;
    }

    public void setCpq(Boolean cpq) {
        this.cpq = cpq;
    }

    public BigDecimal getApplyDiscount() {
        return applyDiscount;
    }

    public void setApplyDiscount(BigDecimal applyDiscount) {
        this.applyDiscount = applyDiscount;
    }

    @Override
    public String toString() {
        return "PplItemVO{"
                + "saleDuration=" + saleDuration
                + ", cpq=" + cpq
                + ", applyDiscount=" + applyDiscount
                + '}';
    }
}
