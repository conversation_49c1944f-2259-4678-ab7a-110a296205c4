package cloud.demand.app.common.utils.excel.demo.checker;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import java.math.BigDecimal;
import java.util.List;

/**
 *   折扣校验，范围值[0,10]
 */
public class DiscountChecker implements ExcelColumnValueChecker {

    @Override
    public void checkValue(int rowIndex, int columnIndex, String columnName, Object value, List<ErrorMessage> errors,
            ParseContext<?> context) {
        if (value == null) {
            return;
        }
        BigDecimal discount = (BigDecimal) value;
        if ((BigDecimal.TEN.compareTo(discount) < 0
                || BigDecimal.ZERO.compareTo(discount) > 0)) {
            String msg = "申请折扣范围值[0,10]，当前值:【" + discount + "】";
            errors.add(new ErrorMessage(rowIndex, columnIndex, columnName, msg));
        }
    }
}
