package cloud.demand.app.common.utils;

import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

//@SpringBootTest
@Slf4j
public class YunXiaoUtilTest {

    @Resource
    PplDictService pplDictService;

    @Test
    public void test() {
        pplDictService.syncYunxiaoInstanceConfig();
    }
}
