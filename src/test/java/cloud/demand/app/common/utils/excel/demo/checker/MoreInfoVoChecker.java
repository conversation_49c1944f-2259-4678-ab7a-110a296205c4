package cloud.demand.app.common.utils.excel.demo.checker;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.checker.ExcelResultDataAfterConvertChecker;
import cloud.demand.app.common.excel.core.checker.ExcelRowDataAfterConvertChecker;
import cloud.demand.app.common.utils.excel.demo.vo.MoreInfoVo;
import java.util.List;

public class MoreInfoVoChecker implements ExcelRowDataAfterConvertChecker<MoreInfoVo>,
        ExcelResultDataAfterConvertChecker<MoreInfoVo> {

    public static final String CONTEXT_KEY_DEPT = "industryDept";

    @Override
    public <R extends MoreInfoVo> void checkResultDataAfterConvert(List<R> resultData, List<ErrorMessage> errors,
            ParseContext<R> context) {
        // 对 MoreInfoVo 的集合数据进行校验
        System.out.println(" do MoreInfoVoChecker checkResultDataAfterConvert ");
        System.out.println("resultData:" + resultData);
    }

    @Override
    public <R extends MoreInfoVo> void checkRowDataAfterConvert(int rowIndex, R rowData, List<ErrorMessage> errors,
            ParseContext<R> context) {
        System.out.println(" do MoreInfoVoChecker checkRowDataAfterConvert ");
        // 对 MoreInfoVo 的单行数据进行校验
        Object dept = context.getCustomContext().get(CONTEXT_KEY_DEPT);
        // 可以根据上下文的 dept 进行一些个性化的校验
        System.out.println("dept: " + dept + " ,rowIndex: " + rowIndex + "  ,rowData:" + rowData);
    }
}
