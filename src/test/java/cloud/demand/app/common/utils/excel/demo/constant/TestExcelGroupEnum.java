package cloud.demand.app.common.utils.excel.demo.constant;

import cloud.demand.app.common.excel.core.ExcelGroup;

/**
 *   excel 组 枚举
 */
public enum TestExcelGroupEnum implements ExcelGroup {
    PPL_ITEM_DRAFT_TEST(TestGroupConstant.PPL_ITEM_DRAFT_TEST,
            "ppl草稿单测试模版组",""),

    DEFAULT_GPU_IMPORT(TestGroupConstant.DEFAULT_GPU_IMPORT,
            "ppl","excel/inner_process/default_gpu_import.xlsx");

    private final String code;

    private final String remark;

    private final String templatePath;

    TestExcelGroupEnum(String code, String remark, String templatePath) {
        this.code = code;
        this.remark = remark;
        this.templatePath = templatePath;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public String getTemplatePath() {
        return templatePath;
    }

}
