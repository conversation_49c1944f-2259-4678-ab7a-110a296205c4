




@commonUrl = {{local}}/cloud-demand-app/ppl13week-forecast?api_key=no\n{{contentType}}

@cookieUrl = {{local}}/cloud-demand-app/ops?api_key=no\n{{contentType}}


###
POST {{cookieUrl}}
Cookie: RIO_TOKEN=sdfdsf

{
  "method": "cookie",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}

### 
POST {{commonUrl}}

{
    "method": "queryForecastSchemaDataList",
    "jsonrpc": "2.0",
    "params": { 
        "taskIds": [44],
        "versionKind": "系统版本",
        "filter":{
        }
        
    },
    "id": "666666"
}


###
POST {{commonUrl}}


{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16850899603360.6747964973460026",
  "method": "queryForecastSchemaDataList",
  "params": {
    "taskIds": [
      46
    ],
    "versionKind": "系统版本",
    "filter": {
      "customerTypes": [
        "中长尾"
      ],
      "ginsFamilies": [],
      "regionNames": [],
      "sourceType": [
        "INDUSTRY",
        "INNER"
      ]
    },
    "isInc": true,
    "groupDim": "客户类型",
    "timeIndex": 1
  }
}


###
POST {{commonUrl}}

{
  "method": "queryForecastCategory",
  "jsonrpc": "2.0",
  "params": { 


    
  },
  "id": "666666"
}

### 
POST {{commonUrl}}

{
  "method": "queryForecastList",
  "jsonrpc": "2.0",
  "params": { 

    "category": "DEFAULT"

    
  },
  "id": "666666"
}




###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16893192330980.9019860672211428",
  "method": "queryForecastSchemaDataList",
  "params": {
    "taskIds": [
      42,
      61
    ],
    "versionKind": "系统版本",
    "filter": {
      "customerTypes": [
        "中长尾"
      ],
      "ginsFamilies": [],
      "regionNames": [],
      "sourceType": [
        "INNER",
        "INDUSTRY"
      ],
      "timePeriod": {
        "start": {
          "year": 2022,
          "month": 12
        },
        "end": {
          "year": 2024,
          "month": 6
        }
      }
    },
    "isInc": true,
    "groupDim": "客户类型",
    "timeIndex": 1
  }
}


{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16897344878440.2445410939108177",
  "method": "queryForecastFitnessGraph",
  "params": {
    "taskIds": [
      601
    ],
    "versionKind": "系统版本",
    "filter": {
      "customerTypes": [
        "中长尾"
      ],
      "ginsFamilies": [],
      "regionNames": [],
      "sourceType": [
        "INDUSTRY_INNER"
      ],
      "timePeriod": {
        "start": {
          "year": 2023,
          "month": 1
        },
        "end": {
          "year": 2023,
          "month": 12
        }
      }
    },
    "isInc": true,
    "groupDim": "客户类型",
    "timeIndex": 1
  }
}

[{"planProductName":"腾讯云媒体处理","region":"ap-guangzhou","regionName":"广州","planProductID":326,"smallCPU":24432,"bigCPU":57904},{"planProductName":"腾讯云媒体处理","region":"na-ashburn","regionName":"弗吉尼亚","planProductID":326,"smallCPU":10614,"bigCPU":1920},{"planProductName":"腾讯云媒体处理","region":"ap-singapore","regionName":"新加坡","planProductID":326,"smallCPU":53408,"bigCPU":736},{"planProductName":"腾讯云媒体处理","region":"ap-tianjin","regionName":"天津","planProductID":326,"smallCPU":72,"bigCPU":65104},{"planProductName":"腾讯云媒体处理","region":"ap-bangkok","regionName":"曼谷","planProductID":326,"smallCPU":2752,"bigCPU":576},{"planProductName":"腾讯云媒体处理","region":"na-siliconvalley","regionName":"硅谷","planProductID":326,"smallCPU":2816,"bigCPU":1344},{"planProductName":"腾讯云媒体处理","region":"ap-hongkong","regionName":"中国香港","planProductID":326,"smallCPU":5392,"bigCPU":1152},{"planProductName":"腾讯云媒体处理","region":"ap-chongqing","regionName":"重庆","planProductID":326,"smallCPU":9843,"bigCPU":10496},{"planProductName":"腾讯云媒体处理","region":"ap-shijiazhuang-ec","regionName":"石家庄EC","planProductID":326,"smallCPU":0,"bigCPU":null},{"planProductName":"腾讯云媒体处理","region":"ap-tokyo","regionName":"东京","planProductID":326,"smallCPU":2880,"bigCPU":0},{"planProductName":"腾讯云媒体处理","region":"ap-seoul","regionName":"首尔","planProductID":326,"smallCPU":1936,"bigCPU":0},{"planProductName":"腾讯云媒体处理","region":"ap-nanjing","regionName":"南京","planProductID":326,"smallCPU":1752,"bigCPU":9280},{"planProductName":"腾讯云媒体处理","region":"ap-beijing","regionName":"北京","planProductID":326,"smallCPU":13632,"bigCPU":22400},{"planProductName":"腾讯云媒体处理","region":"ap-mumbai","regionName":"孟买","planProductID":326,"smallCPU":1600,"bigCPU":0},{"planProductName":"腾讯云媒体处理","region":"ap-chengdu","regionName":"成都","planProductID":326,"smallCPU":6320,"bigCPU":3680},{"planProductName":"腾讯云媒体处理","region":"eu-frankfurt","regionName":"法兰克福","planProductID":326,"smallCPU":15680,"bigCPU":7680},{"planProductName":"腾讯云媒体处理","region":"ap-shanghai","regionName":"上海","planProductID":326,"smallCPU":73856,"bigCPU":104640}] 

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16897428524420.5777807056660584",
  "method": "queryForecastTrendGraph",
  "params": {
    "taskIds": [
      121
    ],
    "versionKind": "系统版本",
    "filter": {
      "customerTypes": [
        "中长尾"
      ],
      "ginsFamilies": [],
      "regionNames": [],
      "sourceType": [
        "INNER"
      ],
      "timePeriod": {
        "start": {
          "year": 2022,
          "month": 7
        },
        "end": {
          "year": 2023,
          "month": 6
        }
      }
    },
    "isInc": true,
    "groupDim": "客户类型",
    "timeIndex": 1
  }
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16897533905110.24330918432752835",
  "method": "queryLatestForecastSummary",
  "params": {
    "taskIds": [
      135,
      138
    ],
    "versionKind": "系统版本",
    "filter": {}
  }
}


###
POST {{commonUrl}}

{
  "method": "createSplitVersion",
  "jsonrpc": "2.0",
  "params": { 
    "taskIds": [1,2],
    "outputVersionName": "outputVersionName",
    "outputVersionType": "SYSTEM"
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "method": "queryOutputVersionType",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "method": "querySplitVersion",
  "jsonrpc": "2.0",
  "params": { 
    "taskIds": [1,2]
  },
  "id": "666666"
}

###
POST {{commonUrl}}
###
POST https://exp-crp.woa.com/cloud-demand-app/ppl13week-forecast
cookie: RIO_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ0b2Y0YXV0aCIsInN1YiI6IjIwMzkzMyIsImV4cCI6MTY5MDg5MjE0OCwibmFtZSI6ImZpcmVmbHljaGVuIiwiZW5jIjoiNUk1TWMxK0lxejlNV2pya0lNNjEwT081WWxyZ2ZmeDVzN0FpSWhQb3JGbktzaWZka01rMWkxR1V4SW5PQXFOeW1BNEdJT0NveXhmUDlwZCtoMmdVOTlkYVlLOXNRQTZGZTBzdFRRTlFvNHFJZ0h5Y1YwS2RMSW5lVzRMVDdwWlkxb2pYc0w1R0x0NjdvOXpTdmljeVFjbXdWTVRQWFFRMndoNUk0QzVRWFc5MlpDMW9xMVlDYzkrRWQvQnZ5RVJhakI0dHovVVVZWDZGT2ZsQ3AwVTdQR3d2TWJPdHRXRVVRdFQ2OVRzQXE5UjhkbkZVYUtweFA5MjZFalhQTzdZSk5yeVRFYzlCa0RRdVRFTXpGWTlRdm4rc2FpZk4yNHArclhpOUllWmcyd0ZQeEZ2Y0JZRjZJRFdhYmUrWDhZZllLc2ttc0lsL2t1U2ZiTm43SWoyV0dqaEJ3SWlmd0hsQ3RhYlpwRUVVWGNKcmFTanJtSzVsUExlai9ySDdJLzVGeUhTMGlYQkErOUM4Wm9DaDMvNExZcndhajVtMjQxWXVEemdtZVVDcnQzams2RFhNbVRUd1Ard1YwVzhVNkowZEYyQXQra1dUSzNIL05oa1FZNjg5R21XWW1wUGlKRkhOK21VdmorQ2wxcTU3NTBvTzRzdy9KSmpNMjEzaTdsRHpjbE1lV05HWEwyWXdkWG8vcDc2Sy9VemdpOXRVbDcxYWJIeVByUThaN2Y4ZU9jcXFrdHlNUURpVGxBdEh5NVF2SUVHVkxaWCt1emlQUmU1ekR0T29DYkUvekFHcXphWUdZZGhaRmRxemdobmdBOExzd1hicnU0U21JaUxUNGlCZFRadWhBOTFHWFU2Zm9SVCtVRXRjUWNMRjVvZWJycHVrTHdsRVp1VWVzamRCQXJmR1lkcGVVeU9LcmZqaDhLUU5FbjZLaUFxZWJYVFRvNkhXQWlsaldGMUhxZk5qN3lvVnZmTmxOMkRSaWMveXRSNjNTSXNVbzZEMk5ML1ljTDVBSEMyQkpkY01IQ09IcXhPUVFvQkg1SC9TMUsva2h3MHB6b2lSaEtGV0xWcHZldjFKa25NN1lJcXpBWEx4cHJ5WTJ1Ni9NTloxN1JCZjl1b25RL1l6ZVd3TmxTRjl2RjQ5bUx5MTBpZysrd0ZHbG5qZ3lPQ29MYVpETTdMNldWVnBBeEdlUloxUGJXS1Q5LzFvSTM0VVoxL2lLNjFVT1pLbUVicE1CcDlWV1pPQWFPSytCZGlxSXQ0aW52WEhiT0JDWkU5MUI3SEcwS0NqQUpmazkzdjFIWXhYSU9vUjBiQjNyMExObHZMYlRoYXNSdUdPQ0Q5SlpPL3l6bjE1UnFYdjdlbS9WYU9KMUhtdzV4VzZkL3RDSEZvR0FIbmlwTEYzMDU5bHloTEFrQ2s0a3dwOHBkMGFsaDQwTVpxa0NyTHJNMnZnd01MQlNETGNQVjV2UWxSQjhhOG5JMEhKT3JMcTNrbFRCZTlIVHJRcVVUT1RvenQxelFFUWpKMDJaSVpoN1hacm9QdHVkRkVFbE1WcVQ4V2JMQT09IiwianRpIjoiMDdybW1jbm1uZThscmMwYXc4dDlzYiIsImlhdCI6MTY5MDgwNTc0OSwidiI6IjIwMjIxMCJ9.OTwFdKNg1tglqeNl11pm1VETfLeEULpWgoKpA8pHhNU
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16907755954620.639697380101643",
  "method": "queryForecastSchemaInfo",
  "params": {
    "taskIds": [
      147
    ]
  }
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16907858746900.10347485751045227",
  "method": "createTransForm",
  "params": {
    "outputVersionIds": [
      26
    ],
    "startYearMonth": "2023-01",
    "endYearMonth": "2023-06",
    "versionCode": "V_20230802",
    "isAppend": false
  }
}

###
curl --request POST \
  --url 'http://localhost:80/cloud-demand-app/cookie?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"method": "querySplitVersion","jsonrpc": "2.0","params": {"taskIds": [1,2]},"id": "666666"}'

###

###
POST https://exp-crp.woa.com/cloud-demand-app/ppl13week-forecast
Content-Type: application/json
Cookie: x_host_key_access_https=7fe49079fb2eb0d36c2f8075be7f113f73849683_s; x-client-ssid=18967a066e2-9e8dd05387882d8cfd22eb02af3b210936556ba4; x-tofapi-host-key=18967a0670e-9a9fb131b2c534c1438a6754430fa2b04b1e02e0; x_host_key_access=7fe49079fb2eb0d36c2f8075be7f113f73849683_s; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22fireflychen%22%2C%22first_id%22%3A%2218968e95bb817-07d9951ae639738-1b525634-2007040-18968e95bb9ae7%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218968e95bb817-07d9951ae639738-1b525634-2007040-18968e95bb9ae7%22%7D; km_u=27e32eb5c9ecb488cc9d115db48013bd55c632f76c220c233f39de12efbf6f4faef46da9c5f68f09; t_uid=fireflychen; km_uid=fireflychen; x-imp-host-key=1896949822a-81acb469a4a84c7da425c1218ee13238193474d1; tkex_csrf_token=gj1fp8whc1; DiggerTraceId=c9391910-2622-11ee-a16c-c9c6b56c5911; tapdsession=3620706e67d8b94e275acf22376be73d; t_u=87e402cd39dc12e2%7Ce5cafb1332ba53b0; _t_uid=1001417360; bk_uid=fireflychen; bk_ticket=BvqTGmOtkLmKXTS2-QUDAMcg4elioPbtKwjLKnfaWaY; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6IlhVNG5ENjE0NGdON3h2N1ZZbnlNSTVTNm8yMExUNnFLIiwiaXNzIjoiMTAuOTkuMTUuMzkiLCJpYXQiOiIyMDIzLTA3LTI0VDIyOjM5OjM4Ljg1MDkyODY2NSswODowMCIsImF1ZCI6IjEwLjIxLjE4LjMxIiwiaGFzaCI6IkJCQzA2RjcxQzYwODMyQjUxQjFFRjAyNUNBRkY5MTFCNDBENEM1MEZCODlENDQ3REQzMzZDNDU2NjcyQTRCMDkiLCJuaCI6IjQ1RTkzNjJCRDc2Mjk2NEQ4NUJFNkI3MzZBOTFERTQyMzdEMEUzNDVDNjRERTdFRkZCRjQ5ODQ4QjYwNDgzRDUifQ; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6Imc5dnVzcGdMa2x6b3A4cDFqd1dZeEJyTE9pdk1MSXRlIiwiaXNzIjoiMTAuOTkuMTUuMzkiLCJpYXQiOiIyMDIzLTA3LTI1VDIzOjA5OjIxLjgzMDk0MTExOSswODowMCIsImF1ZCI6IjExMy45Ny4zMy4xOTgiLCJoYXNoIjoiMkRFNjgwMTE0ODgyODI5OUMzNTk3MTE5QUQ4QTZGNDRDQ0FFMUNBQTAxRENEQkJDMkNDQzBDNUQ1MTE3QzNCQSIsIm5oIjoiQkQyMDkyMjYzOTY5NjkyQUZDMUExMTRCOTY5RTZDNDJFMDkwNzQ5MzFDMkUyM0MyRUY5NDE3OTQ5MjU1QjE3NyJ9; RIO_TOKEN_HTTPS=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ0b2Y0YXV0aCIsInN1YiI6IjIwMzkzMyIsImV4cCI6MTY5MDM4NDE2MSwibmFtZSI6ImZpcmVmbHljaGVuIiwiZW5jIjoidXVLSWY4dkJKUHp2b29WOXM5WlZkQThlZW1ad3k2UklONkVxUE9YVVRYeC9uMWc5NTlSWHJDTmszSDc2RmNRS01pem9KWUF2M3FGQTlEbVVFWjZHS01aWjZHWVMxbW5rZXFJUDVtaDl1OE5LNGxIdVRKWDB0Z1dKU1NKQnVJenV6U0dkU2hvVVczZk0xNGxwek95cG9lVUFQQWd2TE9PSzl2M2M4UVFOTkJuRUY2VXc5SDgvQWxDbWVIcHpGVEJnZS94bVQyd3BKTEROdUhXWUV4NE1oMEZXSUZ5cHVySWZKZ2ZSNEtDSWlVWFJiK0FzZ2psQkxuQmJmcnRESnRaLzFTRXBWdmQ4VjNTd0o4QzZqMkxUclNmdUdBNGpSRUF3ZUF1OHNsbXhDM2lYeVhkMk9OTVQ5ZERkZzM2YTJJRlNOd2JSWDEzTkhhMi8vZyt5c2k3UGpRZ0RjUk1EenRUeGkzaXZ6YzFMUDFnemFaK01iRm1QK3pqRDdOYm5PMG1sL1hHdkVwRFViQzRyQktWK0d1RVJENkV2WjBMRWVVN2NjMkVOZXYvNFlMLy9pdHIyMDhRMDdmcUVsWkdkWSt2blpOTDRRdUtqSXowbjlQNERCZFpYeGh4dytSMkFETXJ1VTloNXR0RVZtR1doL09aWHFoclJvQXlTaDhyeFFDTDRZT2ozRHJpWHIveEdQcGlOVWhGOGVIcVkyaXFOdGh5WENhNXczUGhsY0pkbmJkcitoV0dNbjRuOHlKMndVY3lnbHFDNTZRdCtpYUY3WXg1SGhoaXlNNEFqTVRiNnJ2UlFOcU92b3NPa2V1cGx2S3BuRVJxbU02OG5vcm1vZlVzOWptRDJhY0xhSExPblRuSDJ0MEU3VnBGdEhTTTljWkcwcHFCNWVGNzVmd2szRjJ1QzhBTFA4eVlrRGVzL3J2TDgybjhjUGVXT2pQcFd0T1dkRzZsU2ZpeTBXeGhWLy8rS1FFUllhWlVIY2ZXZEVUM3pXdmNZNi8xWlVINEp2ZGVWL0M5TjFuNlBKSWQ5UkdkeEJxV3k0OUpOdUFBVFMrenZzS0prdVFCVFZGU2FDYldWMnBWbXhpMThBZXl3Q2FTMmZmUHRHUkZJYUF5WGQwanhDWnRvenBoVExDSHpEcEc2VEI0U2tsbHh4cmhJTEFxNExqZ01WdHFaWEk5OTJZd0NHMTYzS0RYcWpYbWVKdk55ditzWldSMEk0QzF4NFQvRDZrc2RjalRaTlhjbmJJS1A5ZTd0ZEVQQVUxU2hHMEU0dm1IN0Z2WDBPc3p5TGVyY0J5b3MxOUphRUlwZUlhKzRIQ2dnWXRFZmVpdzNLREx3SFFWMnRQc0xOeWY4bjk4T1p6dTFuSkVucWl4YkVoV05JMmVoNUhQR0JBbHZubVRzK01iM21JSGlpaW5FZmZXS2RWaG1IL1lvdzJHT2VtaW4zOFRhbm9YckdQaDdGdFVZbDV0RnBHYVFYWnk3ajJBUU5xb1o0M0JuR0RZPSIsImp0aSI6IjA3cmpsaDAyamRibzRuMGd5YzloYmgiLCJpYXQiOjE2OTAyOTc3NjEsInYiOiIyMDIyMTAifQ.v4QsdbrmOi_dQOpaSS7ZfSJhikhyczaUZ5z9zD1_85Y; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6ImViZ1N4aHFXUHJZNG9qZ2gwbXRnYXRIVXVQQzBwMmhHIiwiaXNzIjoiMTAuOTkuMjA4LjU2IiwiaWF0IjoiMjAyMy0wNy0yNlQxOTo1Nzo0MS41ODMzNDg3ODErMDg6MDAiLCJhdWQiOiIxMTMuOTcuMzMuMTk4IiwiaGFzaCI6IkMxMjREN0IyNURFRTZEM0Q1Njk2NTQ5MUExMTAxNUM0Mzk0Njc4MkIzMjc4OTVFMDhENkY4Mjk5NzdCQ0E5MEYiLCJuaCI6IjM5QzNEM0Y1RDdCMkM1QkNGNjkzMjhCMDdCODQyQzc1MzE3MEJGQ0MwQzkzQUZBOThDQTgwMjExRkRDMjUyMzAifQ; RIO_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ0b2Y0YXV0aCIsInN1YiI6IjIwMzkzMyIsImV4cCI6MTY5MDQ1OTA2MSwibmFtZSI6ImZpcmVmbHljaGVuIiwiZW5jIjoiSm5XWXpPZkRPaHVBK21OUGtickEvenBPWVowazRjdk1reDJyWGs1dW1PYllEY3NRZElSdVd2VWRoZlk5eG5EcWs1SWNxM0lNUzRrUWRBOVh1V3V3UWYxRkVMSmJqZkRPMmViOTBSTnJSOE1FcW9CajlkeUN0ZlVoY2xnYzB2MW4rbVl1YytKVS9JREREVkwvN3YzaElFMVRkZlI0VkFQVVJrKzlhWSsyWVJidzkxN2xnRUcyMlgyaXVqdmVOc1lJTjdTdVcrcU5xcTF1TDYrUEorOHAwVG03aWpwaXN2WWs2cTZQWlNDT1RMYjFreVU3Z29WS3V6VkdzREFMNW9VM0txM3FvZlE2WXpuL09pR2JUdG5wZnFpYmN6UXU5WFFpMVZadUhMc3dSelVsMmhhK0wwTFkxcEUxMC9aa3ppUmhDak1PSUNaRmRFZXpLZHdaTkhDaVhQemZ0TndYNnpQbnBmOHU1QkJIR1RQdmk4NDgvaFJvTWpTdXloZkNoSjE5RFhhK3BUM0lSZHRuMEYxZElYRUZEWDJmWmZqL3NiN1pQdmJHb1c0dDM3L3JIUmRnV1A0Y082WmtFSS93U25hK2JGVjByWUVzd25qTS9PdEFwM2I4UC9Hb0d3VWpZb0hzdFA1YVZ5REFBRHI3Vm9YSHFma0RrVXNzdG5MRVJLWVVHRHJtU3YxYVpoV2w2SmRid2RDd2RoWXV5MFBOR2NzQUl2Yk5JdTF3TXZSdko2STV3cjlKYWxjMS91c3ovWEdXSk5PM3BPN3RNTGVjYm9VRGxlbDZRZExBbFpGNkt3aXZBWHFCa0JKbmlzZ3U1UUovU3dJdFA2R0ZDNFVFK3RvU3lNc1RSUGVlb25tMzNDVEJQQVFtZFlOUVpmeVUzTnJlZFFleHZXcllML1ZrUlNEWUZlTGZjRVFrWXhoMFRyNEZHSW1xSyttVEI4MERFY2thNHFkNFgrK2orKzFzVmpFakN4VFVpc3ZpdFBoUkVWdEhKZnlaV1NUbEdsdkViUXh0Y2dpK0VkUHdDNDZPbFlacGFydXZaWHN1Z0V3ZXBDWENzOEJNMlZQaUphNkluZFpCd2ZVNnVzaTVqUTFhVTJhQUFzOEpOeFUyK0pCajVpYWNLK0FJL0t6OXpRc0daZ29ydC9uN2Y5dFVOVlI5cmJPMXE3OGJmdVBNWGZDdVdCRWlNWkxzcFN0SWZ5bUxiNFRQZWNQVjhTcGdQS3E2TGtYbnQwR0F3MUJudzFlMGhRRmtpc0hCd3NBa05nZEJOMlE5N0NIYlliMmQ4RmFvd2IrbHozTWx2ZHF2QjFnNll0RE5BZTU0NE8xYkovTWdUSUxsWWdmN3E0ekNJTm1PZ1J1bVpUdXZiN3FLeVhpdkI5bmU0eHIwWXAzWlFwZ0RCWjV5b1lNZmtRQkY3cVYrL1NXODBwRW9kWDZDcVlmYmNFZDVVQ0s3WHNxZU0zdzc1QmwrRit1TUh2aVpxaHpYdUlOOXJnQ203NjVuWU5oL3hTNWoxeXU4dERuWFBhaVZsK2lhdWV0KzIvZXBvSGt5Wm5zNkZLNEVOZz09IiwianRpIjoiMDdyazFpeGpqZGJvNG4wZ3ozN2h0eSIsImlhdCI6MTY5MDM3MjY2MSwidiI6IjIwMjIxMCJ9.CBtQe0uHIeBSKIRZyQKH83AYow8tK-UtWrbB1GKtQQM

{"jsonrpc":"2.0","id":"cloud-demand-app-16904263143940.6762397450040674","method":"createTransForm","params":{"taskIds":[95],"startYearMonth":"2023-08","endYearMonth":"2023-09","versionCode":"V_20230726","isAppend":"覆盖"}}