



@commonUrl = {{local}}/cloud-demand-app/ppl13week?api_key=no\n{{contentType}}


### 需求概览
POST {{commonUrl}}

{
    "method": "queryGpuGroupSummary",
    "jsonrpc": "2.0",
    "params": {
       "versionCode": "演示本本01"
    },
    "id": "666666"
}

### rsp
{
 "industryDept": "",
 "status": "",
 "statusName": "",
 "demandNum": "", //gpu 卡，cpu 核
 "operator":""
}


### 开始对冲
POST {{commonUrl}}

{
    "method": "startNewGpuStockSupply",
    "jsonrpc": "2.0",
    "params": {
       "versionCode": "演示本本01"
    },
    "id": "666666"
}

### rsp
{
 "data":"success"
}

### 查询接口状态

POST {{commonUrl}}

{
  "method": "queryGpuStockSupplyStatus",
  "jsonrpc": "2.0",
  "params": {
    "versionCode": "56"
  },
  "id": "666666"
}

### rsp

Long stockId;
String status;
String operator;
String createTime;


### 
### 导入gpu 对冲
POST /cloud-demand-app/ppl13week/uploadAndSaveGpuSupply?api_key=no HTTP/1.1
Host: localhost
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="params"

{"versionCode":"测试版本-5-6"}
--boundary
Content-Disposition: form-data; name="file"; filename="t.xlsx"

< /Users/<USER>/Desktop/gpu-test.xlsx
--boundary--


### 导出空白模版
POST {{commonUrl}}

{
  "method": "downloadGpuSupplyTemplate",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}


### 导出版本的明细
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16822195484900.2566992979637295",
  "method": "exportGpuPplVersionItemStockSupplyExcel",
  "params": {  "supplyGpuId": 7}
}


{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16822195484900.2566992979637295",
  "method": "exportGpuPplVersionItemStockSupplyExcel",
  "params": {  "supplyGpuId": 7}
}


{"df":"中文"}

{
  "df": "中文"
}





###
POST {{commonUrl}}

{
    "method": "platformProductConfigList",
    "jsonrpc": "2.0",
    "params": { 
        "pageNumber":1,
        "pageSize": 20
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16820471637560.41388629539744826",
  "method": "exportPplVersionGpuItemExcelWithStockSupply",
  "params": {
    "data": [
      {
        "creator": "oliverychen",
        "importType": null,
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": "EMPTY",
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2304187601",
        "year": 2023,
        "month": 7,
        "yearMonth": "2023-07",
        "submitUser": "oliverychen",
        "industry": "汽车",
        "industryDept": "云运营管理部",
        "customerShortName": "蔚来科技安徽",
        "customerUin": "100020102315",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2304187601-001",
        "status": "VERSION_IMPORT",
        "statusName": "版本提交导入",
        "demandScene": "新项目上线",
        "projectName": null,
        "billType": "包年包月",
        "winRate": 100,
        "beginBuyDate": "2023-07-01",
        "endBuyDate": "2023-07-30",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "北京",
        "zoneName": "北京六区",
        "instanceType": "H800",
        "isRecommendedInstanceType": false,
        "instanceModel": "H800.48XLARGE2048",
        "instanceNum": 2,
        "instanceModelCoreNum": 192,
        "coreNum": 192,
        "instanceModelRamNum": 2048,
        "alternativeInstanceType": [],
        "totalCoreNum": 384,
        "totalDiskNum": 0,
        "applyInstanceNum": null,
        "applyTotalCore": null,
        "yunxiaoOrderIds": null,
        "systemDiskType": null,
        "systemDiskStorage": null,
        "systemDiskNum": 1,
        "dataDiskType": null,
        "dataDiskStorage": null,
        "dataDiskNum": null,
        "cvmSupply": [],
        "cbsSupply": [],
        "gpuSupply": [
          {
            "matchType": "采购满足",
            "count": 16,
            "unit": "卡"
          }
        ],
        "gpuSupplyDbData": [
          {
            "id": 11,
            "deleted": false,
            "createTime": "2023-04-21 10:16:27",
            "updateTime": "2023-04-21 10:16:27",
            "supplyGpuId": 6,
            "pplId": "PN2304187601-001",
            "regionName": "广州",
            "zoneName": "广州三区",
            "matchType": "BUY",
            "matchInstanceType": "GN10Xp.10XLARGE160",
            "hostType": "df",
            "hostNum": 1,
            "gpuNum": 4,
            "countryName": "国内",
            "planProductName": "腾讯云CVM",
            "region": "ap-guangzhou",
            "zone": "ap-guangzhou-3",
            "remark": "12"
          },
          {
            "id": 12,
            "deleted": false,
            "createTime": "2023-04-21 10:16:27",
            "updateTime": "2023-04-21 10:16:27",
            "supplyGpuId": 6,
            "pplId": "PN2304187601-001",
            "regionName": "广州",
            "zoneName": "广州三区",
            "matchType": "BUY",
            "matchInstanceType": "GN10Xp.10XLARGE160",
            "hostType": "df",
            "hostNum": 1,
            "gpuNum": 12,
            "countryName": "国内",
            "planProductName": "腾讯云CVM",
            "region": "ap-guangzhou",
            "zone": "ap-guangzhou-3",
            "remark": "12"
          }
        ],
        "supplyData": null,
        "product": "GPU(裸金属&CVM)",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "groupStatus": "COMD_APPROVE",
        "bizScene": "AI计算场景",
        "bizDetail": "大型规模训练",
        "serviceTime": "长期稳定使用",
        "gpuNum": 8,
        "isAcceptAdjust": false,
        "acceptGpu": null,
        "gpuProductType": "CVM_GPU",
        "gpuType": "H800",
        "totalGpuNum": 16,
        "uneditable": true,
        "type": "not-edit"
      }
    ],
    "startYearMonth": "202305",
    "endYearMonth": "202307",
    "industryDept": "云运营管理部",
    "product": "GPU(裸金属&CVM)"
  }
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16820479615740.6344950030264511",
  "method": "downloadGpuSupply",
  "params": {
    "supplyGpuId": 6
  }
}

###
POST {{commonUrl}}

{
  "method": "queryGpuPhysicalServerList",
  "jsonrpc": "2.0",
  "params": { 

    "groupId": 2108
    
  },
  "id": "666666"
}


###
POST http://crp.woa.com/cloud-demand-app/ppl13week-forecast/queryForecastSchemaSummary?api_key=no
{{contentType}}

{
  "method": "queryLatestForecastSummary",
  "jsonrpc": "2.0",
  "params": { 
    "schemaId": 16,
    "versionKind": "系统版本",
    "filter": {}
    
  },
  "id": "666666"
}
###

curl --request POST \
  --url 'http://crp.woa.com/cloud-demand-app/ppl13week-forecast/queryLatestForecastSummary?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"method": "queryLatestForecastSummary","jsonrpc": "2.0","params": {"schemaId": 16,"versionKind": "系统版本","filter": {}},"id": "666666"}'


curl --request POST \
  --url 'http://localhost:80/cloud-demand-app/ppl13week?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"method": "queryLatestForecastSummary","jsonrpc": "2.0","params": {},"id": "666666"}'
###



# 
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16836007252460.7076558131521524",
  "method": "exportGpuPplVersionItemExcel",
  "params": {
    "versionCode": "测试版本-5-6"
  }
}