

@commonUrl = {{local}}/cloud-demand-app/ppl13week-forecast?api_key=no\n{{contentType}}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16853405407190.8363027313801066",
  "method": "queryAdjustParams",
  "params": {
    "taskIds": [
      42,
      46
    ]
  }
}


###
POST {{commonUrl}}

{
  "method": "queryForecastList",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16812170675070.12059437183994781",
  "method": "queryForecastFitnessGraph",
  "params": {
    "schemaId": 12,
    "versionKind": "系统版本",
    "filter": {
      "customerTypes": [
        "中长尾"
      ],
      "ginsFamilies": [],
      "regionNames": [],
      "timePeriod": {
        "start": {
          "year": 2019,
          "month": 7
        },
        "end": {
          "year": 2020,
          "month": 6
        }
      }
    },
    "isInc": true,
    "groupDim": "客户类型",
    "timeIndex": 1
  }
}




###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16802521654270.6654569000998867",
  "method": "queryForecastFitnessGraph",
  "params": {
    "schemaId": 1,
    "versionKind": "系统版本",
    "filter": {
      "customerTypes": [
        "中长尾"
      ],
      "ginsFamilies": [],
      "regionNames": [],
      "timePeriod": {
        "start": {
          "year": 2019,
          "month": 7
        },
        "end": {
          "year": 2020,
          "month": 6
        }
      }
    },
    "isInc": true,
    "groupDim": "客户类型",
    "timeIndex": 2
  }
}

###
POST {{commonUrl}}

{
    "method": "downloadAdjust",
    "jsonrpc": "2.0",
    "params": { 
        "taskId" : 1
    },
    "id": "666666"
}

###
POST /cloud-demand-app/ppl13week-forecast/uploadAndSaveAdjust?api_key=no HTTP/1.1
Host: localhost
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="params"

{}
--boundary
Content-Disposition: form-data; name="file"; filename="t.xlsx"

< /Users/<USER>/Desktop/dddd.xlsx
--boundary--



#### 
curl --location --request POST 'http://localhost/cloud-demand-app/ppl13week-forecast?api_key=no' \
--header 'Content-Type: application/json' \
--data-raw '{
    "method": "downloadAdjust",
    "jsonrpc": "2.0",
    "params": { 
        "taskId" : 6
    },
    "id": "666666"
}
'



###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16832879236130.15327341874135691",
  "method": "queryAdjustSummary",
  "params": {
    "taskIds": [
      25,
      24
    ],
    "sourceType": [
      "INDUSTRY",
      "INNER"
    ]
  }
}



###
POST {{commonUrl}}

{
    "method": "queryAdjustSummary",
    "jsonrpc": "2.0",
    "params": { 
        "taskIds": [14,13],
        "startYearMonth": "2019-04",
        "endYearMonth": "2023-05"
    },
    "id": "666666"
}


###

POST {{commonUrl}}

{
    "method": "queryAdjustDetail",
    "jsonrpc": "2.0",
    "params": { 
        "taskId": 1,
         "startYearMonth": "2020-04",
         "endYearMonth": "2020-05",
         "regionName": ["广州", "上海"],
         "type" : ["NEW"]
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "queryAdjustParams",
    "jsonrpc": "2.0",
    "params": { 
        "taskId": 836
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "deleteAdjustByTaskId",
    "jsonrpc": "2.0",
    "params": { 
        "taskId": 1
    },
    "id": "666666"
}



###
POST {{commonUrl}}

{
    "method": "queryTransFormStatus",
    "jsonrpc": "2.0",
    "params": { 
        "versionCode": "test-relevant-data2"
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "createTransForm",
    "jsonrpc": "2.0",
    "params": { 
        "versionCode": "test-relevant-data2"
    },
    "id": "666666"
}



### 
POST {{commonUrl}}

{
  "method": "createForecastComputeWeekMax",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "finishTransForm",
    "jsonrpc": "2.0",
    "params": { 
        "pplResultId" : 3
    },
    "id": "666666"
}


###

GET http://localhost:80/cloud-demand-app/ops/predictArima?alg1=ARIMA&alg2=0,1,6&inputId=1000


curl --request GET \
  --url 'http://localhost:80/cloud-demand-app/ops/predictArima?alg1=ARIMA&alg2=0%2C1%2C6&inputId=10' \
###
GET http://**************:80/cloud-demand-app/ops/