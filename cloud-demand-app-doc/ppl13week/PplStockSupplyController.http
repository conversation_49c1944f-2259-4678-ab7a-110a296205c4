


###

curl -X 'GET' \
  'http://api.yunxiao.vstation.woa.com/beacon/resource-match-plan/detail?planId=plan-63a1be5b22' \
  -H 'accept: */*' \
  -H 'random: 1387' \
  -H 'appkey: ak.comd' \
  -H 'timestamp: 1672026159' \
  -H 'signature: 50fc10aa4cc21e399a19faf50d94844a997ee2a0'


### CBS 库存对冲

curl -XPOST -d '{"Action":"SaveCbsStockPlan","items":[{"uin":"1111111","region":"ap-shanghai","zone":"ap-shanghai-4","demandDate":"2022-12-07","demandType":"NEW","priority":1,"label":"A","disks":[{"diskType":"CLOUD_PREMIUM","diskSize":100,"diskNum":5},{"diskType":"CLOUD_SSD","diskSize":100,"diskNum":10}]}]}' https://qcbs.production.polaris/api/v3  \
  -H "Host: qcbs.woa.com" \
  -H "Content-Type: application/json" \
  -H "Cookie: OUTFOX_SEARCH_USER_ID_NCOO=571398844.6106354; _ga=GA1.2.1471584386.1655295578; pgv_pvid=462467818; t_uid=fireflychen; t_u=87e402cd39dc12e2|e5cafb1332ba53b0; _gcl_au=1.1.686072155.1666696468; x_host_key_access_https=f5a4d18224e4d562581a7d6de710d1e0e5e96666_s; x-client-ssid=184a37107ec-9e47de974238ffe467a7e80caa138968a2d6052a; x-tofapi-host-key=184a3710855-5aaf0c466cb0f2f96560ac64d1ce51cf3bde3ad3; fileDownload=true; x_host_key_access=f5a4d18224e4d562581a7d6de710d1e0e5e96666_s; km_u=27e32eb5c9ecb488d962bb07a044bab661213e472537744c6c41bcf8d696d4d2ca4144850ee4d7a5; km_uid=fireflychen; DiggerTraceId=edceeb90-711b-11ed-af60-55e38939a213; x-imp-host-key=184d734f919-424448c0ebd43dae27aab572f1f3c6f67b6ac18c;tapdsession=2f5ddbf1b777026d1f4e9c5fd79b2aa8; bk_uid=fireflychen; bk_ticket=BvqTGmOtkLmKXTS2-QUDAHi3-fJFXFneZ4-RskXYmlY; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IlJndmZlTjN1azA2NFZUdjI2WmtKRUt2QkNOUFRUMG9FIiwiaXNzIjoiMTAuOTkuMjA4LjU2IiwiaWF0IjoiMjAyMi0xMi0wN1QxMDowNzoxNS43ODg0OTY0ODUrMDg6MDAiLCJhdWQiOiIzMC4yMC4xOTkuMjYiLCJoYXNoIjoiNEYzNjUzOTM1Q0JGMkFGREMwMDMwOTMyNDlDOUFCMUI0NjBBOTA1MjI2MTQyMTQ1RkIwMTNDMEEyMjZFMDE5RiIsIm5oIjoiOTIyN0I3NzE5MjU5NTkxQTJBOUI2NUE2QzZGMkRFMkI3MURFNjg4NEU1QTE5MTZEMUEzMEE5OTMyQkE1NTAzMCJ9; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IlJndmZlTjN1azA2NFZUdjI2WmtKRUt2QkNOUFRUMG9FIiwiaXNzIjoiMTAuOTkuMjA4LjU2IiwiaWF0IjoiMjAyMi0xMi0wN1QxMDowNzoxNS43ODg0OTY0ODUrMDg6MDAiLCJhdWQiOiIzMC4yMC4xOTkuMjYiLCJoYXNoIjoiNEYzNjUzOTM1Q0JGMkFGREMwMDMwOTMyNDlDOUFCMUI0NjBBOTA1MjI2MTQyMTQ1RkIwMTNDMEEyMjZFMDE5RiIsIm5oIjoiOTIyN0I3NzE5MjU5NTkxQTJBOUI2NUE2QzZGMkRFMkI3MURFNjg4NEU1QTE5MTZEMUEzMEE5OTMyQkE1NTAzMCJ9; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6IlJndmZlTjN1azA2NFZUdjI2WmtKRUt2QkNOUFRUMG9FIiwiaXNzIjoiMTAuOTkuMjA4LjU2IiwiaWF0IjoiMjAyMi0xMi0wN1QxMDowNzoxNS43ODg0OTY0ODUrMDg6MDAiLCJhdWQiOiIzMC4yMC4xOTkuMjYiLCJoYXNoIjoiNEYzNjUzOTM1Q0JGMkFGREMwMDMwOTMyNDlDOUFCMUI0NjBBOTA1MjI2MTQyMTQ1RkIwMTNDMEEyMjZFMDE5RiIsIm5oIjoiOTIyN0I3NzE5MjU5NTkxQTJBOUI2NUE2QzZGMkRFMkI3MURFNjg4NEU1QTE5MTZEMUEzMEE5OTMyQkE1NTAzMCJ9"
  -H "Accessname: crp_access" \
  -H "Timestamp: 1670393651779" \
  -H "Signature: 004267f1d3f87c40a31a9b7403c5ae94891759cfb1df0695611674dca58b2cbdc8cfa2bd023b6103566e86fc4763f28989b39337d4aa4c00545b0959a313a230"



###

curl --request POST \
  --url 'http://localhost:80/cloud-demand-app/ppl13week?api_key=no' \
  --header 'content-type: application/json' \
  --header 'jsessionid=ab8c694ca9facbf413bde1fe3849d0c4; x-imp-host-key=1810340b275-285e78719249e1b8c76b532e2346321cb48d63c2; x_host_key_access_https=16393241ab4c5269ec8a6ab9e97a33368f28fb22_s; x-client-ssid=1810347526c-0ae3a11620a9cb1093e3a9f1594a64e900e2e9cd; x-tofapi-host-key=181034756f4-059180adb5f24fc3d42e40f3292bd7c4878684ee; x_host_key_access=16393241ab4c5269ec8a6ab9e97a33368f28fb22_s; _ra=_ra1653621999979.0.9178285812706408; x-host-key-ngn=18103a392d5-5898bfa7af23e136934557b625aadb39a5383a08; x-host-key-front=18103a3971e-64d1248e757a8634b829e0e33b4b133b28bcb158; blueking_language=zh-cn; t_u=87e402cd39dc12e2|e5cafb1332ba53b0; t_uid=fireflychen; x_host_key=18105e63522-27d091af295bd903748a95917ac6e656975d502e; diggertraceid=7fef7c20-e174-11ec-ba2f-ff5f180f38e8; outfox_search_user_id_ncoo=571398844.6106354; _ga=ga1.2.1471584386.1655295578; _gcl_au=1.1.194929219.1655295579; paas_perm_sessionid=80mknjes08ud2d5a5vqvhvhb295oq6to; x-host-key-oaback_https=09f2528e23a70e8027ad8797c65948be678c2b9a; erp_username=fireflychen; erp_real_username=fireflychen; paas_perm_csrftoken=xfts4mrmw3efskobyeijdpc9cs33nlnnkbwm1mf6n7hnsh7n1k390b2qtedtnmhs; tapdsession=ec2162b1028240494245caa384489e42; _t_uid=1001417360; x-host-key-oaback=181cc0fbe2b-45f2dee9a3aadabb61417dacbf70ea6e4c2d9fb7; tkex_csrf_token=kmz10unjpe; rio_tcoa_ticket=tof: TOF4TeyJ2IjoiNCIsInRpZCI6IjRpS0ZrVlBOd1lsR21yc0tKcXZabnFtMEhCQW9UbDJKIiwiaXNzIjoiMTAuOTkuMTUuMzkiLCJpYXQiOiIyMDIyLTA3LTA2VDEwOjExOjIwLjEyNzUzMDE5MyswODowMCIsImF1ZCI6IjEwLjg5LjMuNjkiLCJoYXNoIjoiMkY3QjhEMzlGN0M4QTAxNTI0MUU2M0Y1RDJBNzFDODkzQzc3QjhDRjhDOEFBRkFBRDQ5OTBCOUZDRkU2QTk1RCIsIm5oIjoiRjgwMTNCODhCRjFEODREOEZFQkRCREE5NzMyNTRGRjdGQzdEMkFGRTdDMjI0QUNCRkFGQzgxNTREOUJDRkI3RSJ9; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IjRpS0ZrVlBOd1lsR21yc0tKcXZabnFtMEhCQW9UbDJKIiwiaXNzIjoiMTAuOTkuMTUuMzkiLCJpYXQiOiIyMDIyLTA3LTA2VDEwOjExOjIwLjEyNzUzMDE5MyswODowMCIsImF1ZCI6IjEwLjg5LjMuNjkiLCJoYXNoIjoiMkY3QjhEMzlGN0M4QTAxNTI0MUU2M0Y1RDJBNzFDODkzQzc3QjhDRjhDOEFBRkFBRDQ5OTBCOUZDRkU2QTk1RCIsIm5oIjoiRjgwMTNCODhCRjFEODREOEZFQkRCREE5NzMyNTRGRjdGQzdEMkFGRTdDMjI0QUNCRkFGQzgxNTREOUJDRkI3RSJ9; tgw_l7_route=5d03973d6420824458c72ed04cba482b' \
  --header 'user-agent: vscode-restclient' \
  --data '{"method": "queryPplStockSupplyView","jsonrpc": "2.0","params": {"dimensions": ["industryDept"],"versionCode" : "test","type": "CVM","pplId":  "df;df;dsf;pplid;ppl1,df,pplid4"},"id": "666666"}'


@commonUrl = {{local}}/cloud-demand-app/ppl13week?api_key=no\n{{contentType}}

###
POST {{commonUrl}}
cookie: {{cookie}}

{
  "method": "queryPplStockSupplyView",
  "jsonrpc": "2.0",
  "params": {

    "dimensions": ["industryDept"],
    "versionCode" : "test",
    "type": "CVM",
    "pplId":  "df;df;dsf;pplid;ppl1,df,pplid4"

  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "method": "queryPplStockSupplyViewDimensions",
  "method": "queryPplStockSupplyBuyView",
  "method": "queryPplStockSupplyView",
  "jsonrpc": "2.0",
  "params": {

    "dimensions": ["industryDept"],
    "versionCode" : "test",
    "type": "CVM",
    "pplId":  "df;df;dsf;pplid;ppl1,df,pplid4"

  },
  "id": "666666"
}



###
POST {{commonUrl}}

{
  "method": "queryPplStockSupplyViewDimensions",
  "jsonrpc": "2.0",
  "params": {
    "versionCode": "56"
  },
  "id": "666666"
}



###
POST {{commonUrl}}
user-agent: vscode-restclient
Cookie: JSESSIONID=E1DF67261A998A5EE1DB3C2B0981FDDF; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6Ill1SEZDQW9DbmFXQVF6eDhOMDg0S1hpVHhIZ3I5dEhTIiwiaXNzIjoiMTAuOTkuMTUuMzYiLCJpYXQiOiIyMDIyLTEyLTE1VDE1OjAyOjEwLjA4MjI1NDEyNCswODowMCIsImF1ZCI6IjEwLjkxLjQwLjcxIiwiaGFzaCI6Ijc5MDMxRkU5NEY3RTUyQzM3M0ZDQTUyOTUxQzYwMkM1OTVDMzdCOEU3OUQyNjU5NzhGODIwMEZGNzUyNTJERTciLCJuaCI6IjAyMzM1QzJBMUVCMEY2NEQwNDVBMDYyNDdBMDU4NkEwREUxRDY1NDY1M0E1QzdDMDc0MUVCNDM1MkUyQUQyMTUifQ; ERP_USERNAME=oliverychen; ERP_REAL_USERNAME=oliverychen

{
  "method": "completeCvmStockSupply",
  "jsonrpc": "2.0",
  "params": {
    "planId": "plan-63acfe014c",
    "results": [
      {
        "planId": "plan-63acfe014c",
        "matchType": "buy",
        "matchCount": 4,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-002",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT5",
        "hostCount": 1,
                "remark": "teset remark"

      },
      {
        "planId": "plan-63acfe014c",
        "matchType": "move",
        "matchCount": 2,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-002",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT6",
        "hostCount": 2
      },
      {
        "planId": "plan-63acfe014c",
        "matchType": "satisfy",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-002",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3
      },
       {
        "planId": "plan-63acfe014c",
        "matchType": "FAIL",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-002",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3,
                "remark": "teset remark"

      },

      {
        "planId": "plan-63acfe014c",
        "matchType": "buy",
        "matchCount": 4,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT5",
        "hostCount": 1
      },
      {
        "planId": "plan-63acfe014c",
        "matchType": "move",
        "matchCount": 2,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT6",
        "hostCount": 2
      },
      {
        "planId": "plan-63acfe014c",
        "matchType": "satisfy",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3,
                "remark": "teset remark"

      },
       {
        "planId": "plan-63acfe014c",
        "matchType": "FAIL",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211080006-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3,
        "remark": "teset remark"
      }
    ]
  },
  "id": "666666"
}

###

POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16723057527750.8943918352266527",
  "method": "queryVersionGroupView",
  "params": {
    "groupId": 122,
    "type": "REGION",
    "startDate": "",
    "endDate": "",
    "demandType": "RETURN",
    "resourceType": "CORE",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}
###

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16723021591440.6979288205297123",
  "method": "saveVersionGroupItem",
  "params": {
    "groupId": 122,
    "editItems": [
      {
        "creator": "",
        "customerType": "WIN_BACK",
        "customerTypeName": "新增客户",
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2212050019",
        "year": 2023,
        "month": 2,
        "yearMonth": "2023-02",
        "submitUser": null,
        "industry": "",
        "industryDept": "运营管理部",
        "customerShortName": "测试客户名字",
        "customerUin": null,
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212050019-001",
        "status": "VALID",
        "statusName": "未预约",
        "demandScene": "winback新增",
        "projectName": "测试项目名称",
        "billType": "CDH",
        "winRate": 100,
        "beginBuyDate": "2023-02-01",
        "endBuyDate": "2023-02-10",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "成都",
        "zoneName": "随机可用区",
        "instanceType": "S5",
        "isRecommendedInstanceType": true,
        "instanceModel": "S5.8XLARGE64",
        "instanceNum": 100,
        "instanceModelCoreNum": 32,
        "coreNum": 32,
        "instanceModelRamNum": 64,
        "alternativeInstanceType": [],
        "totalCoreNum": 3200,
        "totalDiskNum": null,
        "systemDiskType": null,
        "systemDiskStorage": null,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 50,
        "dataDiskNum": 50,
        "cvmSupply": [],
        "cbsSupply": [],
        "supplyData": null,
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "type": "delete",
        "editType": "delete"
      },
      {
        "creator": "",
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2212050020",
        "year": 2023,
        "month": 2,
        "yearMonth": "2023-02",
        "submitUser": null,
        "industry": "技术服务",
        "industryDept": "运营管理部",
        "customerShortName": "李易沾",
        "customerUin": "100000677658",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212050020-001",
        "status": "VALID",
        "statusName": "未预约",
        "demandScene": "存量扩容",
        "projectName": "测试项目名称",
        "billType": "CDH",
        "winRate": 100,
        "beginBuyDate": "2023-02-01",
        "endBuyDate": "2023-02-10",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "成都",
        "zoneName": "随机可用区",
        "instanceType": "S5",
        "isRecommendedInstanceType": true,
        "instanceModel": "S5.8XLARGE64",
        "instanceNum": 100,
        "instanceModelCoreNum": 32,
        "coreNum": 32,
        "instanceModelRamNum": 64,
        "alternativeInstanceType": [],
        "totalCoreNum": 3200,
        "totalDiskNum": null,
        "systemDiskType": null,
        "systemDiskStorage": null,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 50,
        "dataDiskNum": 50,
        "cvmSupply": [],
        "cbsSupply": [],
        "supplyData": null,
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "type": "delete",
        "editType": "delete"
      },
      {
        "creator": "",
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2212050020",
        "year": 2023,
        "month": 2,
        "yearMonth": "2023-02",
        "submitUser": null,
        "industry": "技术服务",
        "industryDept": "运营管理部",
        "customerShortName": "李易沾",
        "customerUin": "100000677658",
        "demandType": "RETURN",
        "demandTypeName": "退回需求",
        "pplId": "PN2212050020-002",
        "status": "VALID",
        "statusName": "未预约",
        "demandScene": "客户业务收缩",
        "projectName": "测试项目名称",
        "billType": "CDH",
        "winRate": 100,
        "beginBuyDate": "2023-02-01",
        "endBuyDate": "2023-02-10",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "成都",
        "zoneName": "随机可用区",
        "instanceType": "S5",
        "isRecommendedInstanceType": true,
        "instanceModel": "S5.8XLARGE64",
        "instanceNum": 100,
        "instanceModelCoreNum": 32,
        "coreNum": 32,
        "instanceModelRamNum": 64,
        "alternativeInstanceType": [],
        "totalCoreNum": 3200,
        "totalDiskNum": null,
        "systemDiskType": null,
        "systemDiskStorage": null,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 50,
        "dataDiskNum": 50,
        "cvmSupply": [],
        "cbsSupply": [],
        "supplyData": null,
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "type": "delete",
        "editType": "delete"
      },
      {
        "creator": "",
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2212050020",
        "year": 2023,
        "month": 2,
        "yearMonth": "2023-02",
        "submitUser": null,
        "industry": "技术服务",
        "industryDept": "运营管理部",
        "customerShortName": "李易沾",
        "customerUin": "100000677658",
        "demandType": "ELASTIC",
        "demandTypeName": "弹性需求",
        "pplId": "PN2212050020-003",
        "status": "VALID",
        "statusName": "未预约",
        "demandScene": "其他",
        "projectName": "测试项目名称",
        "billType": "按量计费",
        "winRate": 100,
        "beginBuyDate": "2023-02-01",
        "endBuyDate": "2023-02-10",
        "beginElasticDate": "00:01:00",
        "endElasticDate": "10:01:00",
        "regionName": "成都",
        "zoneName": "随机可用区",
        "instanceType": "S5",
        "isRecommendedInstanceType": true,
        "instanceModel": "S5.8XLARGE64",
        "instanceNum": 100,
        "instanceModelCoreNum": 32,
        "coreNum": 32,
        "instanceModelRamNum": 64,
        "alternativeInstanceType": [],
        "totalCoreNum": 3200,
        "totalDiskNum": null,
        "systemDiskType": null,
        "systemDiskStorage": null,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 50,
        "dataDiskNum": 50,
        "cvmSupply": [],
        "cbsSupply": [],
        "supplyData": null,
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "type": "delete",
        "editType": "delete"
      },
      {
        "creator": "kaijiazhang",
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2212290011",
        "year": 2022,
        "month": 12,
        "yearMonth": "2022-12",
        "submitUser": "kaijiazhang",
        "industry": "游戏",
        "industryDept": "运营管理部",
        "customerShortName": "I-GAME(IEG)",
        "customerUin": "770967167",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212290011-001",
        "status": "VERSION_IMPORT",
        "statusName": "版本提交导入",
        "demandScene": "存量扩容",
        "projectName": null,
        "billType": "包年包月",
        "winRate": null,
        "beginBuyDate": "2022-12-30",
        "endBuyDate": "2023-01-11",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "圣保罗",
        "zoneName": "圣保罗一区",
        "instanceType": "S5",
        "isRecommendedInstanceType": false,
        "instanceModel": "S5.8XLARGE128",
        "instanceNum": 62,
        "instanceModelCoreNum": 32,
        "coreNum": 32,
        "instanceModelRamNum": 128,
        "alternativeInstanceType": [],
        "totalCoreNum": 1984,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 100,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 100,
        "dataDiskNum": 1,
        "cvmSupply": [],
        "cbsSupply": [],
        "supplyData": null,
        "product": "CVM&CBS",
        "note": "推动PUBGM从第三方云回迁，预计年底",
        "affinityType": null,
        "affinityValue": null,
        "type": "delete",
        "editType": "delete"
      }
    ],
    "insertData": [
      {
        "creator": null,
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": "其他",
        "customerName": "I-GAME(IEG)",
        "customerSource": "区域直销",
        "pplOrder": null,
        "year": 2022,
        "month": 12,
        "yearMonth": "2022-12",
        "submitUser": null,
        "industry": "游戏",
        "industryDept": null,
        "customerShortName": "I-GAME(IEG)",
        "customerUin": "770967167",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": null,
        "status": "VERSION_IMPORT",
        "statusName": "版本提交导入",
        "demandScene": "存量扩容",
        "projectName": null,
        "billType": "包年包月",
        "winRate": null,
        "beginBuyDate": "2022-12-30",
        "endBuyDate": "2023-01-11",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "圣保罗",
        "zoneName": "圣保罗一区",
        "instanceType": "S5",
        "isRecommendedInstanceType": false,
        "instanceModel": "S5.8XLARGE128",
        "instanceNum": 62,
        "instanceModelCoreNum": 32,
        "coreNum": null,
        "instanceModelRamNum": 128,
        "alternativeInstanceType": null,
        "totalCoreNum": 1984,
        "totalDiskNum": 12400,
        "systemDiskType": "高性能",
        "systemDiskStorage": 100,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 100,
        "dataDiskNum": 1,
        "cvmSupply": null,
        "cbsSupply": null,
        "supplyData": null,
        "product": "CVM&CBS",
        "note": "推动PUBGM从第三方云回迁，预计年底",
        "affinityType": null,
        "affinityValue": null,
        "type": "insert",
        "editType": "insert"
      }
    ],
    "industryDept": "运营管理部"
  }
}


###

{
  "method": "completeCvmStockSupply",
  "jsonrpc": "2.0",
  "params": {
    "planId": "plan-639aedc9f1",
    "results":[
      {
        "planId": "plan-639aedc9f1",
        "matchType": "buy",
        "matchCount": 4,
        "matchInstanceType":"S5.4XLARGE64",
        "label": "PN2212050002-004",
        "region": "ap-guangzhou",
        "zone":"ap-guangzhou-01",
        "hostType":"IT5",
        "hostCount": 1
      },

      {
        "planId": "plan-639aedc9f1",
        "matchType": "stock",
        "matchCount": 2,
        "matchInstanceType":"S5.4XLARGE64",
        "label": "",
        "region": "ap-guangzhou",
        "zone":"ap-guangzhou-01",
        "hostType":"IT6",
        "hostCount": 2
      },

      {
        "planId": "plan-639aedc9f1",
        "matchType": "satisfy",
        "matchCount": 5,
        "matchInstanceType":"S5.4XLARGE64",
        "label": "PN2212050002-004",
        "region": "ap-guangzhou",
        "zone":"ap-guangzhou-01",
        "hostType":"IT3",
        "hostCount": 3
      }


    ]

    },
  "id": "666666"
}


###
POST {{commonUrl}}

{
  "method": "queryStockSupplyStatus",
  "jsonrpc": "2.0",
  "params": {
    "versionCode": "56"
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "method": "startNewStockSupply",
  "jsonrpc": "2.0",
  "params": {
    "versionCode": "test-optimise-5"
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16738683675550.1386340239504642",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 469,
    "versionCode": "test-optimise-5",
    "resourceType": "CORE",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}

###
POST http://exp-crp.woa.com/cloud-demand-app/ppl13week/sent2done?api_key=no
Cookie: OUTFOX_SEARCH_USER_ID_NCOO=571398844.6106354; _ga=GA1.2.1471584386.1655295578; pgv_pvid=462467818; t_uid=fireflychen; t_u=87e402cd39dc12e2|e5cafb1332ba53b0; _ra=_ra1666614582608.0.0874914660663515; _gcl_au=1.1.686072155.1666696468; tapdsession=2f5ddbf1b777026d1f4e9c5fd79b2aa8; sensorsdata2015jssdkcross={"distinct_id":"fireflychen","first_id":"1813c08db272a4-0309c32d94d2fb-367a6700-2007040-1813c08db282a6","props":{"$latest_traffic_source_type":"直接流量","$latest_search_keyword":"未取到值_直接打开","$latest_referrer":""},"$device_id":"1813c08db272a4-0309c32d94d2fb-367a6700-2007040-1813c08db282a6"}; km_u=27e32eb5c9ecb488ce52614e24c84fe6d218486f31ad513995b4697f032ba7d50bc02f7010ffdce9; km_uid=fireflychen; x_host_key_access_https=d479fad21fdfc6760244ffb85bf1f4c3f5d13163_s; x-client-ssid=185735347ed-3cdcf565c53fefdb4c28c640cc2580bf1104157a; x-tofapi-host-key=185735348cc-c7a280f27d9396ce09f64f245c611776e3efc97c; ERP_USERNAME=fireflychen; ERP_REAL_USERNAME=fireflychen; DiggerTraceId=8551af80-8b24-11ed-bc26-abc0df04ac46; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IkFnWmpCNVRna2k1OEVJdlp4Y3FKa2RkemNIenpwRUxHIiwiaXNzIjoiMTAuOTkuMjA4LjM4IiwiaWF0IjoiMjAyMy0wMS0wNFQwOTo1NjoxNi44NjY4MjcxNzgrMDg6MDAiLCJhdWQiOiIzMC4yMC4xOTkuMjEiLCJoYXNoIjoiNjNBMEFEQjYxQTk3ODdBQjA0QzlCMTYwMzNDNjJCQUQ5MDdGQjQ0NTk5QjdGMDczRDk5RkJGOTcyMDgwODY3NSIsIm5oIjoiMDU3RERDMTM4RkRDRTRCODMwQ0FFQUY2MUQwRUI5NEZCOEFCQUMyRTQ0ODlENUQzMjNCOTU2Mzc2NEZDODM5RCJ9; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IkFnWmpCNVRna2k1OEVJdlp4Y3FKa2RkemNIenpwRUxHIiwiaXNzIjoiMTAuOTkuMjA4LjM4IiwiaWF0IjoiMjAyMy0wMS0wNFQwOTo1NjoxNi44NjY4MjcxNzgrMDg6MDAiLCJhdWQiOiIzMC4yMC4xOTkuMjEiLCJoYXNoIjoiNjNBMEFEQjYxQTk3ODdBQjA0QzlCMTYwMzNDNjJCQUQ5MDdGQjQ0NTk5QjdGMDczRDk5RkJGOTcyMDgwODY3NSIsIm5oIjoiMDU3RERDMTM4RkRDRTRCODMwQ0FFQUY2MUQwRUI5NEZCOEFCQUMyRTQ0ODlENUQzMjNCOTU2Mzc2NEZDODM5RCJ9; x_host_key_access=d479fad21fdfc6760244ffb85bf1f4c3f5d13163_s

{
  "method": "sent2done",
  "params": {

  },
  "jsonrpc": "2.0",
  "id": "16728023920852113"
}

###
POST {{commonUrl}}

{
  "method": "sendCvm",
  "jsonrpc": "2.0",
  "params": {
    "stockId": 76
  },
  "id": "666666"
}




###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16708302818630.7084885432766013",
  "method": "queryStockSupplyStatus",
  "params": {
    "versionCode": "录入测试"
  }
}

###

###
POST {{commonUrl}}

{
  "method": "syncCvmStockData",
  "jsonrpc": "2.0",
  "params": {
    "stockId": 80
  },
  "id": "666666"
}


###
curl --request POST \
  --url 'http://*************:80/cloud-demand-app/ppl13week?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"method": "syncCvmStockData","jsonrpc": "2.0","params": {"stockId": 5},"id": "666666"}'




###
curl --request POST \
  --url 'http://*************:80/cloud-demand-app/ppl13week?api_key=no' \
  --header 'content-type: application/json' \
  --data '{"method": "syncCbsStockData","jsonrpc": "2.0","params": {"stockId": 24},"id": "666666"}'

  ###
###
http://{{local}}/cloud-demand-app/ppl13week/queryVersionList



###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16709072554860.41073358681646854",
  "method": "queryStockSupplyGroupView",
  "params": {
    "versionCode" : "录入测试"
  }
}


###
POST http://localhost:80/cloud-demand-app/ppl13week?api_key=no
user-agent: vscode-restclient
Content-Type: application/json
Cookie: JSESSIONID=E1DF67261A998A5EE1DB3C2B0981FDDF; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6Ill1SEZDQW9DbmFXQVF6eDhOMDg0S1hpVHhIZ3I5dEhTIiwiaXNzIjoiMTAuOTkuMTUuMzYiLCJpYXQiOiIyMDIyLTEyLTE1VDE1OjAyOjEwLjA4MjI1NDEyNCswODowMCIsImF1ZCI6IjEwLjkxLjQwLjcxIiwiaGFzaCI6Ijc5MDMxRkU5NEY3RTUyQzM3M0ZDQTUyOTUxQzYwMkM1OTVDMzdCOEU3OUQyNjU5NzhGODIwMEZGNzUyNTJERTciLCJuaCI6IjAyMzM1QzJBMUVCMEY2NEQwNDVBMDYyNDdBMDU4NkEwREUxRDY1NDY1M0E1QzdDMDc0MUVCNDM1MkUyQUQyMTUifQ; ERP_USERNAME=oliverychen; ERP_REAL_USERNAME=oliverychen

{
  "method": "completeCvmStockSupply",
  "jsonrpc": "2.0",
  "params": {
    "planId": "plan-63ad8015e2",
    "results": [
      {
        "planId": "plan-63ad8015e2",
        "matchType": "buy",
        "matchCount": 4,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2212210003-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT5",
        "hostCount": 1
      },
      {
        "planId": "plan-63ad8015e2",
        "matchType": "move",
        "matchCount": 2,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2212210003-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT6",
        "hostCount": 2
      },
      {
        "planId": "plan-63ad8015e2",
        "matchType": "satisfy",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2212210003-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3
      },
      {
        "planId": "plan-63ad8015e2",
        "matchType": "fail",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2212210003-001",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3,
        "remark": "test remark"
      }

      ,
      {
        "planId": "plan-63ad8015e2",
        "matchType": "satisfy",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2212210003-002",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3
      },
      {
        "planId": "plan-63ad8015e2",
        "matchType": "fail",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2212210003-002",
        "region": "ap-guangzhou",
        "zone": "ap-guangzhou-01",
        "hostType": "IT3",
        "hostCount": 3,
        "remark": "test remark"
      }
    ]
  },
  "id": "666666"
}



###
POST {{commonUrl}}

{
  "method": "queryStockSupplyGroupView",
  "jsonrpc": "2.0",
  "params": { 
    "versionCode": "test-cjy-1216-1"
  },
  "id": "666666"
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16714519374880.17347601617324493",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 69,
    "versionCode": "test-ff",
    "resourceType": "CBS",
    "regionNames": ["广州"],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}
###
{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16714224561000.7790960092917909",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 65,
    "versionCode": "TEST-firefly",
    "resourceType": "CBS",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16714224948740.11939915321229888",
  "method": "exportPplVersionItemExcel",
  "params": {
    "data": [
      {
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2211071330",
        "year": 2022,
        "month": 11,
        "yearMonth": "2022-11",
        "submitUser": "erickssu",
        "industry": "技术服务",
        "industryDept": "云智研发中心",
        "customerShortName": "李易沾",
        "customerUin": "100000677658",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2211071330-002",
        "status": "VALID",
        "statusName": "未预约",
        "demandScene": "存量扩容",
        "projectName": null,
        "billType": "按量计费",
        "winRate": null,
        "beginBuyDate": "2022-11-01",
        "endBuyDate": "2022-11-23",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "随机地域",
        "zoneName": "随机可用区",
        "instanceType": "BF1",
        "isRecommendedInstanceType": false,
        "instanceModel": "BF1.2XLARGE16",
        "instanceNum": 33,
        "instanceModelCoreNum": 8,
        "coreNum": 8,
        "instanceModelRamNum": 16,
        "alternativeInstanceType": [],
        "totalCoreNum": 264,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "cvmSupply": [],
        "cbsSupply": [],
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": 0,
        "type": "not-edit"
      }
      
    ],
    "startYearMonth": "202202",
    "endYearMonth": "202305"
  }
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16717007758510.43935736087343313",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 72,
    "versionCode": "wml",
    "resourceType": "CBS",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}

###
{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16716911331960.532501169456866",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 72,
    "versionCode": "wml",
    "resourceType": "CORE",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}


###
POST {{commonUrl}}

{
  "method": "syncCvmStockData",
  "jsonrpc": "2.0",
  "params": { 
    "stockId": 72
  },
  "id": "666666"
}


###
POST http://localhost:80/cloud-demand-app/ppl13week?api_key=no
user-agent: vscode-restclient
Content-Type: application/json
Cookie: JSESSIONID=E1DF67261A998A5EE1DB3C2B0981FDDF; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6Ill1SEZDQW9DbmFXQVF6eDhOMDg0S1hpVHhIZ3I5dEhTIiwiaXNzIjoiMTAuOTkuMTUuMzYiLCJpYXQiOiIyMDIyLTEyLTE1VDE1OjAyOjEwLjA4MjI1NDEyNCswODowMCIsImF1ZCI6IjEwLjkxLjQwLjcxIiwiaGFzaCI6Ijc5MDMxRkU5NEY3RTUyQzM3M0ZDQTUyOTUxQzYwMkM1OTVDMzdCOEU3OUQyNjU5NzhGODIwMEZGNzUyNTJERTciLCJuaCI6IjAyMzM1QzJBMUVCMEY2NEQwNDVBMDYyNDdBMDU4NkEwREUxRDY1NDY1M0E1QzdDMDc0MUVCNDM1MkUyQUQyMTUifQ; ERP_USERNAME=oliverychen; ERP_REAL_USERNAME=oliverychen

{
  "method": "completeCvmStockSupply",
  "jsonrpc": "2.0",
  "params": {
    "planId": "plan-63a9571591",
    "results": [
      {
        "planId": "plan-63a9571591",
        "matchType": "buy",
        "matchCount": 4,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211100001-001",
        "region": "ap-shenyang-ec",
        "zone": "ap-shenyang-ec-1",
        "hostType": "Y0-MS52-25G",
        "hostCount": 1
      },
      {
        "planId": "plan-63a9571591",
        "matchType": "stock",
        "matchCount": 2,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211100001-001",
        "region": "ap-shenyang-ec",
        "zone": "ap-shenyang-ec-1",
        "hostType": "Y0-MS52-25G",
        "hostCount": 2
      },
      {
        "planId": "plan-63a9571591",
        "matchType": "satisfy",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211100001-001",
        "region": "ap-shenyang-ec",
        "zone": "ap-shenyang-ec-1",
        "hostType": "Y0-MS52-25G",
        "hostCount": 3
      },
      {
        "planId": "plan-63a9571591",
        "matchType": "fail",
        "matchCount": 5,
        "matchInstanceType": "S5.4XLARGE64",
        "label": "PN2211100001-001",
        "region": "ap-shenyang-ec",
        "zone": "ap-shenyang-ec-1",
        "hostType": "Y0-MS52-25G",
        "hostCount": 3
      }
    ]
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16721112811360.39299455031372355",
  "method": "queryVersionGroupItem",
  "params": {
    "groupId": 92
  }
}

###
POST {{commonUrl}}

{
    "jsonrpc": "2.0",
    "id": "cloud-demand-app-16721526474730.6844619785814969",
    "method": "importPplVersionItem",
    "params": {
        "groupId": 102,
        "versionCode": "test-27-1",
        "industryDept": "港澳台及国际业务部",
        "product": "CVM&CBS",
        "versionStatus": "PROCESS",
        "isAllowEdit": true,
        "demandBeginYear": 2022,
        "demandBeginMonth": 12,
        "demandEndYear": 2023,
        "demandEndMonth": 1,
        "data": [
            {
                "customerType": "EXISTING",
                "yearMonth": "2022-12",
                "customerUin": "1521488775",
                "product": "CVM&CBS",
                "demandType": "NEW",
                "demandScene": "存量扩容",
                "billType": "包年包月",
                "beginBuyDate": "2022-12-14",
                "endBuyDate": "2022-12-29",
                "regionName": "随机地域",
                "zoneName": "随机可用区",
                "type": "insert",
                "instanceType": "C3",
                "instanceModel": "C3.4XLARGE64",
                "instanceNum": 1,
                "alternativeInstanceType": [],
                "coreNum": 16,
                "totalCoreNum": 16,
                "totalDiskNum": 0,
                "year": 2022,
                "month": 12
            }
        ]
    }
}

### 
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16728385128460.15336524578057897",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 232,
    "versionCode": "test-control-product",
    "resourceType": "CORE",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}

###

###

POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16732507323870.13835949323524366",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 367,
    "versionCode": "vs_PAAS测试",
    "resourceType": "CORE",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}



### 
### 导入cvm 对冲
POST /cloud-demand-app/ppl13week/uploadAndSaveCvmSupply?api_key=no HTTP/1.1
Host: localhost
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="params"

{"versionCode":"测试版本-5-6"}
--boundary
Content-Disposition: form-data; name="file"; filename="t.xlsx"

< /Users/<USER>/Desktop/cvm-tt.xlsx
--boundary--


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16862110236610.16975717377251653",
  "method": "finishStockSupply",
  "params": {
    "versionCode": "测试版本-5-6"
  }
}