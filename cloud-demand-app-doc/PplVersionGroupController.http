




@commonUrl = {{local}}/cloud-demand-app/ppl13week?api_key=no\n{{contentType}}


###
POST /cloud-demand-app/ppl13week/uploadPplItemDetailExcel?api_key=no HTTP/1.1
Host: localhost
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="params"

{"startYearMonth":"202112", "endYearMonth":"202212"}
--boundary
Content-Disposition: form-data; name="file"; filename="pplorder.csv"

< /Users/<USER>/Downloads/云运营管理部-13周PPL数据导出-20230103-000011.xlsx
--boundary--

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16716298082490.650246228262503",
  "method": "saveVersionGroupItem",
  "params": {
    "groupId": 73,
    "editItems": [
      {
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "PN2212210001",
        "year": 2023,
        "month": 2,
        "yearMonth": "2023-02",
        "submitUser": null,
        "industry": "电商",
        "industryDept": "云运营管理部",
        "customerShortName": "拼多多",
        "customerUin": "2678475016",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212210001-001",
        "status": "VALID",
        "statusName": "未预约",
        "demandScene": "winback项目",
        "projectName": "离线计算",
        "billType": "按量计费",
        "winRate": null,
        "beginBuyDate": "2023-01-28",
        "endBuyDate": "2023-03-02",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "上海",
        "zoneName": "上海五区",
        "instanceType": "SA3",
        "isRecommendedInstanceType": true,
        "instanceModel": "SA3.8XLARGE64",
        "instanceNum": 4,
        "instanceModelCoreNum": 32,
        "coreNum": 32,
        "instanceModelRamNum": 64,
        "alternativeInstanceType": [
          "SA2"
        ],
        "totalCoreNum": 128,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 100,
        "dataDiskNum": 1,
        "cvmSupply": [],
        "cbsSupply": [],
        "supplyData": null,
        "product": "CVM&CBS",
        "note": "录入测试，页面添加",
        "affinityType": "母机",
        "affinityValue": 100,
        "type": "update",
        "editType": "update"
      }
    ]
  }
}



###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16699585399430.6542904499467639",
  "method": "importPplVersionItem",
  "params": {
    "groupId": 29,
    "versionCode": "<br><a>test<a/><br/>",
    "industryDept": "云智研发中心",
    "product": "CVM&CBS",
    "versionStatus": "PROCESS",
    "isAllowEdit": true,
    "demandBeginYear": 2022,
    "demandBeginMonth": 1,
    "demandEndYear": 2022,
    "demandEndMonth": 2,
    "data": [
      {
        "customerType": "EXISTING",
        "customerTypeName": "存量客户",
        "warZone": "中长尾企业",
        "customerName": "杭州永卓电子商务有限公司",
        "customerSource": "中长尾",
        "pplOrder": null,
        "year": 2022,
        "month": 3,
        "yearMonth": "2022-02-28T16:00:00.000Z",
        "submitUser": null,
        "industry": "电商",
        "industryDept": "test_industry",
        "customerShortName": "杭州永卓电子商务有限公司",
        "customerUin": "100010274760",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": null,
        "status": "VERSION_IMPORT",
        "statusName": "版本提交导入",
        "demandScene": "存量扩容",
        "projectName": "项目类型",
        "billType": "包年包月",
        "winRate": 12,
        "beginBuyDate": "2022-03-07",
        "endBuyDate": "2022-03-07",
        "beginElasticDate": "03:04",
        "endElasticDate": "05:06",
        "regionName": "上海",
        "zoneName": "上海三区",
        "instanceType": "IT5nt",
        "instanceModel": "IT5nt.21XLARGE208",
        "instanceNum": 2,
        "alternativeInstanceType": [],
        "totalCoreNum": 168,
        "totalDiskNum": 159,
        "systemDiskType": "高性能",
        "systemDiskStorage": 2,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 2,
        "dataDiskNum": 2,
        "product": "CVM&CBS",
        "note": "testnote",
        "affinityType": "test",
        "affinityValue": 12
      }
    ]
  }
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16700582505170.7780221157405525",
  "method": "queryVersionGroupItem",
  "params": {
    "groupId": 29
  }
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16702071872910.5841599268599871",
  "method": "importPplVersionItem",
  "params": {
    "groupId": 29,
    "versionCode": "<br><a>test<a/><br/>",
    "industryDept": "云智研发中心",
    "product": "CVM&CBS",
    "versionStatus": "PROCESS",
    "isAllowEdit": true,
    "demandBeginYear": 2022,
    "demandBeginMonth": 1,
    "demandEndYear": 2022,
    "demandEndMonth": 2,
    "data": [
      {
        "customerType": null,
        "customerTypeName": null,
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "",
        "year": 0,
        "month": 0,
        "yearMonth": null,
        "submitUser": "",
        "industry": "",
        "industryDept": "",
        "customerShortName": "",
        "customerUin": "",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212020005-001",
        "status": "",
        "statusName": "",
        "demandScene": "存量扩容",
        "projectName": null,
        "billType": "包年包月",
        "winRate": null,
        "beginBuyDate": "2022-03-07",
        "endBuyDate": "2022-03-07",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "上海",
        "zoneName": "上海三区",
        "instanceType": "IT5nt",
        "instanceModel": "IT5nt.21XLARGE208",
        "instanceNum": 2,
        "instanceModelCoreNum": null,
        "instanceModelRamNum": null,
        "alternativeInstanceType": [
          "[]"
        ],
        "totalCoreNum": 168,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 2,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 2,
        "dataDiskNum": 2,
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "demandYearMonth": null,
        "type": "not-edit"
      },
      {
        "customerType": null,
        "customerTypeName": null,
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "",
        "year": 0,
        "month": 0,
        "yearMonth": null,
        "submitUser": "",
        "industry": "",
        "industryDept": "",
        "customerShortName": "",
        "customerUin": "",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212020007-001",
        "status": "",
        "statusName": "",
        "demandScene": "存量扩容",
        "projectName": null,
        "billType": "包年包月",
        "winRate": null,
        "beginBuyDate": "2022-03-07",
        "endBuyDate": "2022-03-07",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "上海",
        "zoneName": "上海三区",
        "instanceType": "IT5nt",
        "instanceModel": "IT5nt.21XLARGE208",
        "instanceNum": 2,
        "instanceModelCoreNum": null,
        "instanceModelRamNum": null,
        "alternativeInstanceType": [
          "[]"
        ],
        "totalCoreNum": 168,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 2,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 2,
        "dataDiskNum": 2,
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "demandYearMonth": null,
        "type": "not-edit"
      },
      {
        "customerType": null,
        "customerTypeName": null,
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "",
        "year": 0,
        "month": 0,
        "yearMonth": null,
        "submitUser": "",
        "industry": "",
        "industryDept": "",
        "customerShortName": "",
        "customerUin": "",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212020008-001",
        "status": "",
        "statusName": "",
        "demandScene": "存量扩容",
        "projectName": null,
        "billType": "包年包月",
        "winRate": null,
        "beginBuyDate": "2022-03-07",
        "endBuyDate": "2022-03-07",
        "beginElasticDate": null,
        "endElasticDate": null,
        "regionName": "上海",
        "zoneName": "上海三区",
        "instanceType": "IT5nt",
        "instanceModel": "IT5nt.21XLARGE208",
        "instanceNum": 2,
        "instanceModelCoreNum": null,
        "instanceModelRamNum": null,
        "alternativeInstanceType": [
          "[]"
        ],
        "totalCoreNum": 168,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 2,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 2,
        "dataDiskNum": 2,
        "product": "CVM&CBS",
        "note": null,
        "affinityType": null,
        "affinityValue": null,
        "demandYearMonth": null,
        "type": "not-edit"
      },
      {
        "customerType": null,
        "customerTypeName": null,
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "",
        "year": 0,
        "month": 0,
        "yearMonth": null,
        "submitUser": "",
        "industry": "",
        "industryDept": "",
        "customerShortName": "",
        "customerUin": "",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212020009-001",
        "status": "",
        "statusName": "",
        "demandScene": "存量扩容",
        "projectName": "项目类型",
        "billType": "包年包月",
        "winRate": null,
        "beginBuyDate": "2022-03-07",
        "endBuyDate": "2022-03-07",
        "beginElasticDate": "03:04:00",
        "endElasticDate": "05:06:00",
        "regionName": "上海",
        "zoneName": "上海三区",
        "instanceType": "IT5nt",
        "instanceModel": "IT5nt.21XLARGE208",
        "instanceNum": 2,
        "instanceModelCoreNum": null,
        "instanceModelRamNum": null,
        "alternativeInstanceType": [
          "[]"
        ],
        "totalCoreNum": 168,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 2,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 2,
        "dataDiskNum": 2,
        "product": "CVM&CBS",
        "note": "testnote",
        "affinityType": "test",
        "affinityValue": 12,
        "demandYearMonth": null,
        "type": "not-edit"
      },
      {
        "customerType": null,
        "customerTypeName": null,
        "warZone": null,
        "customerName": null,
        "customerSource": null,
        "pplOrder": "",
        "year": 0,
        "month": 0,
        "yearMonth": null,
        "submitUser": "",
        "industry": "",
        "industryDept": "",
        "customerShortName": "",
        "customerUin": "",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "pplId": "PN2212020010-001",
        "status": "",
        "statusName": "",
        "demandScene": "存量扩容",
        "projectName": "项目类型",
        "billType": "包年包月",
        "winRate": 12,
        "beginBuyDate": "2022-03-07",
        "endBuyDate": "2022-03-07",
        "beginElasticDate": "03:04:00",
        "endElasticDate": "05:06:00",
        "regionName": "上海",
        "zoneName": "上海三区",
        "instanceType": "IT5nt",
        "instanceModel": "IT5nt.21XLARGE208",
        "instanceNum": 2,
        "instanceModelCoreNum": null,
        "instanceModelRamNum": null,
        "alternativeInstanceType": [
          "[]"
        ],
        "totalCoreNum": 168,
        "totalDiskNum": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 2,
        "systemDiskNum": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 2,
        "dataDiskNum": 2,
        "product": "CVM&CBS",
        "note": "testnote",
        "affinityType": "test",
        "affinityValue": 12,
        "demandYearMonth": null,
        "type": "not-edit"
      }
    ]
  }
}


###

POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16702075572390.36072067253045614",
  "method": "approveVersionGroup",
  "params": {
    "groupId": 46,
    "approveResult": "PASS",
    "approveNote": "approve"
  }
}





###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16709072554860.41073358681646854",
  "method": "queryVersionGroupList",
  "params": {
    "page": {
      "start": 0,
      "size": 999999
    }
  }
}


####

POST {{commonUrl}}

{
  "method": "getNoSuccessUin",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}




###

POST http://localhost/cloud-demand-app/ppl13week?api_key=no
{{contentType}}

{
  "method": "queryVersionGroupStat",
  "jsonrpc": "2.0",
  "params": {
      "groupId": 1074,
      "versionCode": "V022201",
      "resourceType": "GPU",
      "regionNames": [],
      "warZoneNames": [],
      "instanceTypes": [],
      "demandScenes": [],
      "customerShortNames": [],
      "customerUins": []
   },
  "id": "666666"
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16823072687180.17065292764388174",
  "method": "queryStockSupplyGroupView",
  "params": {
    "groupId": 2103,
    "versionCode": "演示本本01",
    "resourceType": "CORE",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}


###
POST {{commonUrl}}

{
  "method": "updateYunXiaoApplyData",
  "jsonrpc": "2.0",
  "params": {

    "groupId": 1292

  },
  "id": "666666"
}

###
curl 'http://localhost/cloud-demand-app/ppl13week-forecast/createTransForm?api_key=no' \
  -H 'authority: exp-crp.woa.com' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json; charset=UTF-8' \
  -H 'pragma: no-cache' \
  -H 'referer: https://exp-crp.woa.com/forecast-model/long/manage' \
  -H 'sec-ch-ua: "Chromium";v="116", "Not)A;Brand";v="24", "Google Chrome";v="116"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-16921001427710.41594867985608874","method":"createTransForm","params":{"outputVersionIds":[28],"startYearMonth":"2023-07","endYearMonth":"2023-12","versionCode":"V_20230816","isAppend":false,"desc":"dsf"}}' \
  --compressed


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16778280197300.9434773153420128",
  "method": "startApprove",
  "params": {
    "id": 124
  }
}
curl --request POST \
  --url 'http://localhost:80/cloud-demand-app/ppl13week?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"jsonrpc": "2.0","id": "cloud-demand-app-16778280197300.9434773153420128","method": "startApprove","params": {"id": 124}}'
###
POST {{commonUrl}}

###
POST http://localhost:80/cloud-demand-app/ppl13week-forecast?api_key=no

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16921533641530.9601482051901806",
  "method": "createTransForm",
  "params": {
    "outputVersionIds": [
      28
    ],
    "startYearMonth": "2023-09",
    "endYearMonth": "2023-09",
    "versionCode": "V_20230818",
    "isAppend": false,
    "desc": "6546545"
  }
}


###
POST {{commonUrl}}

{
  "method": "updateYunXiaoApplyData",
  "jsonrpc": "2.0",
  "params": {
    "groupId": 1832
  },
  "id": "666666"
}



###
POST https://crp.woa.com/cloud-demand-app/ppl13week
cookie: OUTFOX_SEARCH_USER_ID_NCOO=571398844.6106354; _ga=GA1.2.1471584386.1655295578; pgv_pvid=462467818; t_uid=fireflychen; _ra=_ra1667291314685.0.30496167518646344; x_host_key_access_https=61c5a9d455264b3eadd3c340468867482539bdfb_s; x-client-ssid=186816c03c8-227607ca45f2c5b5419be5cb607830efbeb8c76c; x-tofapi-host-key=186816c044e-3bae2dcf4e3412cd9acfcc3577c5e84f1291a80f; DiggerTraceId=925fd6e0-b3f4-11ed-ae4e-9f654b01794f; x_host_key_access=61c5a9d455264b3eadd3c340468867482539bdfb_s; ERP_REAL_USERNAME=fireflychen; km_uid=fireflychen; km_u=27e32eb5c9ecb488ac52001d127a557920b04f5875437085d2d2acbf89165a81d3725c8612f33bc7; fileDownload=true; Tencent.OA.MyWorkspace.MyOAV2=80C3006FFD8BD604D6363E08B77EEC26594B90FEFC8006F949B6D3BDA8D0AFEEFBB6922D474290B4B990EF09843126B68A7056745C76BFE6AC819D5600673BDE1AC917DC034FE6B3C27D0E19156A43B6B33B7D3649548CEFFE29034DB38B966E2FA89D05057437FA3B6A9ADCACA230878884ECD6; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6Ik1NVzVuQndIdGxMYnNkM2drWnppaTBsczdvdFg3SEV4IiwiaXNzIjoiMTAuOTkuMTUuNDkiLCJpYXQiOiIyMDIzLTAzLTA4VDIyOjM4OjA5LjY2MzE3OTEzOSswODowMCIsImF1ZCI6IjExMy45Ny4zMy4xNzAiLCJoYXNoIjoiQTM3OEEwOTMzNjc0NjM3QzY1QTExOTU3NzQ4RUNDNTQzNENDQTc2ODE5MzhEODc5RDlDRTc3OTgyRjgwMERDNCIsIm5oIjoiMDc2MTkwQUQxMTlEN0NGMjE3QTM5RDdFRDMyOEE3NzM5MENBQzBCOUIyQUU0NTkyM0QwODNFMjY1RkMyQkJCMyJ9; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6Ik1NVzVuQndIdGxMYnNkM2drWnppaTBsczdvdFg3SEV4IiwiaXNzIjoiMTAuOTkuMTUuNDkiLCJpYXQiOiIyMDIzLTAzLTA4VDIyOjM4OjA5LjY2MzE3OTEzOSswODowMCIsImF1ZCI6IjExMy45Ny4zMy4xNzAiLCJoYXNoIjoiQTM3OEEwOTMzNjc0NjM3QzY1QTExOTU3NzQ4RUNDNTQzNENDQTc2ODE5MzhEODc5RDlDRTc3OTgyRjgwMERDNCIsIm5oIjoiMDc2MTkwQUQxMTlEN0NGMjE3QTM5RDdFRDMyOEE3NzM5MENBQzBCOUIyQUU0NTkyM0QwODNFMjY1RkMyQkJCMyJ9; ERP_USERNAME=kaijiazhang
{{contentType}}

{
  "method": "updateYunXiaoApplyData",
  "jsonrpc": "2.0",
  "params": {
    "groupId" : 171
  },
  "id": "666666"
}
