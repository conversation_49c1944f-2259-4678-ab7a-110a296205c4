POST https://prom-efxjoxw7.grafana.sh-vip.prom.tencent-cloud.com/api/datasources/proxy/1/api/v1/query_range
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer eyJrIjoiME9OQjBBUHlkRkhJMkNrY1RGMW56NjhSd1dmbk43c3EiLCJuIjoiZW1yIiwiaWQiOjF9

query=sum by (parentSpec, zone) (increase(emrcc_spec_sellout_count[1d] @ 1720022400))






###

# curl -XPOST http://jkt.es.tencentyun.com:5021/interface -H "Content-Type: application/json" -d '{
#  "interface": {
#    "interfaceName": "qcloud.elasticsearch.admin.queryESServiceQualityReporting",
#    "para": {
#		"Type": "ZoneSellingRate",
#        "StartTime": "2024-07-03 02:57:26",
#        "TimeSpan": 24
#    }
#  }
#}'
POST http://jkt.es.tencentyun.com:5021/interface
Content-Type: application/json

{
  "interface": {
    "interfaceName": "qcloud.elasticsearch.admin.queryESServiceQualityReporting",
    "para": {
      "Type": "ZoneSellingRate",
      "StartTime": "2024-07-03 02:57:26",
      "TimeSpan": 24
    }
  }
}

###



# curl -XPOST http://kr.es.tencentyun.com:5021/interface -H "Content-Type: application/json" -d '{
#  "interface": {
#    "interfaceName": "qcloud.elasticsearch.admin.queryESServiceQualityReporting",
#    "para": {
#	 "Type": "EsCreatData",
#        "StartTime": "2024-07-02 16:37:18",
#        "TimeSpan": 24
#    }
#  }
#}
POST http://bj.es.tencentyun.com:5021/interface
Content-Type: application/json

{
  "interface": {
    "interfaceName": "qcloud.elasticsearch.admin.queryESServiceQualityReporting",
    "para": {
      "Type": "EsCreatData",
      "StartTime": "2024-07-02 16:37:18",
      "TimeSpan": 24
    }
  }
}

###

POST https://prom-efxjoxw7.grafana.sh-vip.prom.tencent-cloud.com/api/datasources/proxy/1/api/v1/query
Accept: application/json
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer eyJrIjoiME9OQjBBUHlkRkhJMkNrY1RGMW56NjhSd1dmbk43c3EiLCJuIjoiZW1yIiwiaWQiOjF9

query=sum by (parentSpec, zone) (increase(emrcc_spec_sellout_count{sellout="1"}[1d] offset 900m))


###

# curl -XPOST http://kr.es.tencentyun.com:5021/interface -H "Content-Type: application/json" -d '{
#  "interface": {
#    "interfaceName": "qcloud.elasticsearch.admin.queryESServiceQualityReporting",
#    "para": {
#	 "Type": "EsCreatData",
#        "StartTime": "2024-07-02 16:37:18",
#        "TimeSpan": 24
#    }
#  }
#}'
POST http://bj.es.tencentyun.com:5021/interface
Content-Type: application/json

{
  "interface": {
    "interfaceName": "qcloud.elasticsearch.admin.queryESServiceQualityReporting",
    "para": {
      "Type": "EsCreatData",
      "StartTime": "2024-12-30 00:00:00",
      "TimeSpan": 24
    }
  }
}

###

# curl -d '{"Action":"DescribeCvmApiRecords", "Region":"ap-guangzhou", "Limit":1, "Offset":0, "StartTime":"2024-07-10 00:00:00", "EndTime":"2024-07-16 00:00:00"}' http://127.0.0.1:4021/api/v3
POST http://************:4021/api/v3
Content-Type: application/json

{"Action":"DescribeInstancesV2", "Limit":100, "Offset":0}

###

# curl  http://localhost:9102/data -d '{"startTime": "2024-12-29 00:00:00", "endTime": "2024-12-30 00:00:00"}'
POST http://************:9102/data
Content-Type: application/json

{"startTime": "2024-12-29 00:00:00", "endTime": "2024-12-30 00:00:00"}

###

