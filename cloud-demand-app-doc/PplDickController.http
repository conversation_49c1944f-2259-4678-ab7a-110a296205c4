


@commonUrl = {{local}}/cloud-demand-app/ppl13week?api_key=no\n{{contentType}} 



###
###
POST http://exp-crp.woa.com:80/cloud-demand-app/ppl13week?api_key=no
{{commonUrl}}

{
  "method": "baseGetAppId2UinMapData",
  "jsonrpc": "2.0",
  "params": { 

    "appid": [ "1251018908", "1251026709"]
    
  },
  "id": "666666"
}


####
POST {{commonUrl}}

{
    "method": "queryDemandScene",
    "jsonrpc": "2.0",
    "params": { 
        "customerType":"新增客户",
        "demandType": "新增"
    },
    "id": "666666"
}


###
POST {{commonUrl}}

{
    "method": "queryInstanceTypeWithRecommendedType",
    "jsonrpc": "2.0",
    "params": { 
        # "zoneName": "广州一区"
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "queryInstanceModel",
    "jsonrpc": "2.0",
    "params": { 
               "zoneName": "广州一区",
               "InstanceType": "S4m"
 
    },
    "id": "666666"
}


###
POST {{commonUrl}}

{
    "method": "queryAllCityName",
    "jsonrpc": "2.0",
    "params": { 
        "cityName": "广州"
        
    },
    "id": "666666"
}


###
POST {{commonUrl}}

{"jsonrpc":"2.0","id":"cloud-demand-app-16667843899760.15532042519784817","method":"queryPplList","params":{"pplOrder":[],"yearMonth":[],"source":[],"industry":[],"customerShortName":[],"submitUser":[],"createTimeStart":"2022-10-24","createTimeEnd":"2022-12-26","status":"VALID","page":{"start":0,"size":10}}}


####
POST http://biz-auth-app/biz-auth/api?api_key=no
{{contentType}}

{
    "jsonrpc": "2.0",
    "id": "1234",
    "method": "getBaseStaffInfo",
    "params": {
		// 员工英文名和员工id二选一填写
        "staffEngName":"psionli"
    }
}


###
POST {{commonUrl}}

{
  "method": "queryInfoByUin",
  "jsonrpc": "2.0",
  "params": { 
    "uin": "3287248762"
  },
  "id": "666666"
}


###
POST {{commonUrl}}

{
    "method": "queryIndustryDept",
    "jsonrpc": "2.0",
    "params": { 
        
    },
    "id": "666666"
}



###
POST {{commonUrl}}

{
    "method": "queryMainInstanceType",
    "jsonrpc": "2.0",
    "params": { 
        "zoneName": "北京七区"
    },
    "id": "666666"
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16668557336950.8581558087790737",
  "method": "queryInfoByUin",
  "params": {
    "uin": "100000677658"
  }
}
###
{"jsonrpc":"2.0","id":"cloud-demand-app-16668528103800.2386715590839532","method":"queryDemandScene","params":{"customerType":"EXISTING","demandType":"新增需求"}}
###
POST {{commonUrl}}

###
POST http://11.134.157.75/cloud-demand-app/ppl13week?api_key=no
{{contentType}}

{
  "method": "baseGetAppId2UinMapData",
  "jsonrpc": "2.0",
  "params": { 

    "appid": ["1316213718"]
    
  },
  "id": "666666"
}

###

{"jsonrpc":"2.0","id":"cloud-demand-app-16668574867920.8960215240090406","method":"queryInfoByUin","params":{"uin":"100000677658"}}
###
POST {{commonUrl}}

{
    "method": "queryInfoByUin",
    "jsonrpc": "2.0",
    "params": { 
        "uin": "100000677658"
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{"jsonrpc":"2.0","id":"cloud-demand-app-16668586363430.8521149917995781","method":"queryResourceInfo","params":{"pplOrder":"PN2210251041"}}

###
{"jsonrpc":"2.0","id":"cloud-demand-app-16668553953790.46050759987387124","method":"queryResourceInfo","params":{"pplOrder":"PN2210261057"}}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16668719795310.13272301037685752",
  "method": "submitPpl",
  "params": {
    "pplOrder": "PN2210271069",
    "resources": [
      {
        "product": "CVM&CBS",
        "demandType": "新增需求",
        "demandScene": "winback新增",
        "projectName": null,
        "billType": "包年包月",
        "winRate": 12,
        "beginBuyDate": "2022-10-07",
        "endBuyDate": "2022-10-08",
        "note": "12",
        "regionName": "随机地域",
        "zoneName": "随机可用区",
        "type": "update",
        "instanceType": "BMD3c",
        "instanceModel": "BMD3c.24XLARGE384",
        "instanceNum": 3,
        "alternativeInstanceType": null,
        "systemDiskType": "SSD",
        "systemDiskStorage": 1,
        "dataDiskType": "SSD",
        "dataDiskStorage": 1,
        "dataDiskNum": 1,
        "affinityType": "母机",
        "affinityValue": 1,
        "pplId": "PN2210271069-001"
      }
    ],
    "submitType": "meddle"
  }
}



###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16668725446560.8474565775362282",
  "method": "updatePplItem",
  "params": {
    "pplOrder": "PN2210251041",
    "resources": [
      {
        "product": "CVM&CBS",
        "demandType": "NEW",
        "demandScene": "新项目",
        "projectName": "某项目",
        "billType": "包年包月",
        "winRate": 88,
        "beginBuyDate": "2022-10-20",
        "endBuyDate": "2022-10-21",
        "note": "备注1",
        "regionName": "广州",
        "zoneName": "广州三区",
        "type": "update",
        "instanceType": "S5",
        "instanceModel": "S5.2XLARGE16",
        "instanceNum": 11,
        "alternativeInstanceType": null,
        "systemDiskType": "SSD",
        "systemDiskStorage": 100,
        "dataDiskType": "SSD",
        "dataDiskStorage": 1000,
        "dataDiskNum": 2,
        "affinityType": "亲和度1",
        "affinityValue": 11,
        "pplId": "PN2210251041-001"
      }
    ]
  }
}

####
POST {{commonUrl}}

{
    "method": "queryDemandType",
    "jsonrpc": "2.0",
    "params": { 
        
    },
    "id": "666666"
}



### 
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16669390149550.017201493113213173",
  "method": "queryPplInfo",
  "params": {
    "pplOrder": "PN2210281010"
  }
}

###
POST {{commonUrl}}

{
    "method": "queryProcessInfoByPplOrderId",
    "jsonrpc": "2.0",
    "params": { 
        "pplOrder" : "PN2210281010"
    },
    "id": "666666"
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16669484772060.8187229233778648",
  "method": "queryPplList",
  "params": {
    "pplOrder": [],
    "source": [],
    "industry": [],
    "customerShortName": [],
    "submitUser": [],
    "createTimeStart": "2022-10-28",
    "createTimeEnd": "2022-10-28",
    "status": "WAIT_MEDDLE",
    "page": {
      "start": 0,
      "size": 10
    }
  }
}



###
POST {{commonUrl}}

{
    "method": "queryIndustryName",
    "jsonrpc": "2.0",
    "params": { 
        
    },
    "id": "666666"
}




###
POST /cloud-demand-app/ppl13week/uploadPplOrderDetailExcel?api_key=no HTTP/1.1
Host: localhost
Content-Type: multipart/form-data; boundary=boundary


--boundary
Content-Disposition: form-data; name="customerType"

EXISTING
--boundary
Content-Disposition: form-data; name="startYearMonth"

202211
--boundary
Content-Disposition: form-data; name="file"; filename="pplorder.csv"

< /Users/<USER>/Desktop/13周PPL数据导出-20221111-103925.xlsx
--boundary--




###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16678251632610.8809141292185372",
  "method": "submitPpl",
  "params": {
    "customerType": "EXISTING",
    "customerUin": "100010274760",
    "industryDept": "运营管理部",
    "industry": "电商",
    "warZone": "中长尾企业",
    "customerName": "杭州永卓电子商务有限公司",
    "customerSource": "中长尾",
    "customerShortName": "杭州永卓电子商务有限公司",
    "year": 2022,
    "month": 11,
    "submitType": "submit",
    "resources": [
      {
        "product": "CVM&CBS",
        "demandType": "NEW",
        "demandScene": "存量扩容",
        "projectName": "热水",
        "billType": "按量计费",
        "winRate": 1,
        "beginBuyDate": "2022-11-11",
        "endBuyDate": "2022-11-30",
        "note": "123",
        "regionName": "广州",
        "zoneName": "广州四区",
        "type": "insert",
        "instanceType": "BMG5t",
        "instanceModel": "BMG5t.24XLARGE384",
        "instanceNum": 12,
        "alternativeInstanceType": [],
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "affinityType": "母机",
        "affinityValue": 1
      }
    ]
  }
}


###
POST {{commonUrl}}

{
  "method": "queryParams",
  "jsonrpc": "2.0",
  "params": { 

    "columnName": "INDUSTRY_DEPT"
    
  },
  "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16680472563970.7639406486668485",
  "method": "queryPplResourceDetail",
  "params": {
    "pplOrder": "PN2211090002"
  }
}



###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16680646166810.4052398107128652",
  "method": "queryAllZoneName",
  "params": {
    "cityName": "广州"
  }
}


###
POST {{commonUrl}}

{
  "method": "queryBillType",
  "jsonrpc": "2.0",
  "params": { 
    "demandType": "NEW"
  },
  "id": "666666"
}


###
POST {{commonUrl}}

{
  "method": "queryRecommendCityAndZone",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16696203953600.03985702732698626",
  "method": "updatePplItem",
  "params": {
    "pplOrder": "PN2211110002",
    "resources": [
      {
        "product": "CVM&CBS",
        "demandType": "NEW",
        "demandScene": "新项目上线",
        "projectName": "23",
        "billType": "按量计费",
        "winRate": 1,
        "beginBuyDate": "2022-11-05",
        "endBuyDate": "2022-11-09",
        "note": "我去问",
        "regionName": "重庆",
        "zoneName": "重庆一区",
        "type": "update",
        "instanceType": "C2",
        "instanceModel": "C2.LARGE16",
        "instanceNum": 11,
        "alternativeInstanceType": null,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "affinityType": "母机",
        "affinityValue": 1,
        "coreNum": 60,
        "totalCoreNum": 16,
        "totalDiskNum": 400,
        "pplId": "PN2211110002-005"
      }
    ]
  }
}


###
POST {{commonUrl}}

{
  "method": "queryAllProductEnum",
  "jsonrpc": "2.0",
  "params": { 
    
  },
  "id": "666666"
}