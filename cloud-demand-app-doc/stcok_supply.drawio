<mxfile host="65bd71144e">
    <diagram id="UE2sNSJjZVmgO7Tupwvl" name="Page-1">
        <mxGraphModel dx="2257" dy="854" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="INIT" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="90" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="2">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="420" y="330" as="sourcePoint"/>
                        <mxPoint x="400" y="270" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="&lt;span style=&quot;color: rgba(0 , 0 , 0 , 0) ; font-family: monospace ; font-size: 0px&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22CVMCBS%E4%B8%A4%E4%B8%AA%E9%83%BD%E4%B8%BASENDING%22%20style%3D%22edgeLabel%3Bhtml%3D1%3Balign%3Dcenter%3BverticalAlign%3Dmiddle%3Bresizable%3D0%3Bpoints%3D%5B%5D%3B%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22462%22%20y%3D%22348.57142857142867%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="4">
                    <mxGeometry x="-0.1639" y="-1" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" value="CVMCBS都为SENDING" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="4">
                    <mxGeometry x="-0.3143" y="2" relative="1" as="geometry">
                        <mxPoint x="24" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="SENDING" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="SENT" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="810" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="DONE" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1140" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="6">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="770" y="340" as="sourcePoint"/>
                        <mxPoint x="820" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="48" value="CVMCBS都为SENT" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8">
                    <mxGeometry x="-0.22" y="6" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="7">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="930.0000000000002" y="269.29" as="sourcePoint"/>
                        <mxPoint x="1020" y="269" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="CVMCBS都为DONE" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="9">
                    <mxGeometry x="-0.366" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="cloud_demand.ppl_stock_supply 创建一条记录初始化为 Init" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-300" y="250" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="&lt;span style=&quot;text-align: center ; text-indent: 0px ; background-color: rgb(42 , 42 , 42) ; display: inline ; float: none&quot;&gt;&lt;font face=&quot;helvetica&quot;&gt;&lt;br&gt;&lt;/font&gt;&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="200" y="340" width="280" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="主status为SENDING&lt;br&gt;构造请求发送到 CVMCBS团队" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="560" y="365" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="29" style="edgeStyle=none;html=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="310" y="570" as="targetPoint"/>
                        <mxPoint x="310" y="480" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="失败" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="29">
                    <mxGeometry x="-0.157" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="INIT" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="90" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="15" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="调用成功并返回TASKID" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="24">
                    <mxGeometry x="-0.2692" y="3" relative="1" as="geometry">
                        <mxPoint x="360" y="-7" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" style="edgeStyle=none;html=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="310" y="580" as="targetPoint"/>
                        <mxPoint x="310" y="670" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="失败" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="30">
                    <mxGeometry x="0.062" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="INIT" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="90" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="CVM" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="-10" y="460" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="CBS" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="-10" y="660" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="33" style="edgeStyle=none;html=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="640" y="560" as="targetPoint"/>
                        <mxPoint x="640" y="480" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="失败" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="33">
                    <mxGeometry x="-0.1958" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="18" target="37">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="Text" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="44">
                    <mxGeometry x="-0.334" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="SENDING" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=none;html=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="640" y="580" as="targetPoint"/>
                        <mxPoint x="640" y="660" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="失败" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="34">
                    <mxGeometry x="0.1096" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="19" target="38">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="SENDING" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="14" target="18">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="470" y="330" as="sourcePoint"/>
                        <mxPoint x="520" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="调用成功并返回PlanId" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="23">
                    <mxGeometry x="-0.1048" y="2" relative="1" as="geometry">
                        <mxPoint x="344" y="2" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="" style="endArrow=none;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="80" y="570" as="sourcePoint"/>
                        <mxPoint x="1400" y="570" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="全流程终止" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="-30" y="560" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="55" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="37" target="53">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="成功调用并返回数据" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="55">
                    <mxGeometry x="-0.2027" y="5" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="SENT" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="810" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="56" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="38" target="54">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="58" value="成功调用并返回数据" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="56">
                    <mxGeometry x="-0.1483" y="-1" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="SENT" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="810" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="主status" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="-50" y="260" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="主status为 SENT&amp;nbsp;&lt;br&gt;定期轮训CVMCBS接口直到有数据返回" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="925" y="360" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="DONE" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1140" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="DONE" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1145" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="59" style="edgeStyle=none;html=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1010" y="560" as="targetPoint"/>
                        <mxPoint x="1010" y="480" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="60" value="失败" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="59">
                    <mxGeometry x="-0.1958" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="61" style="edgeStyle=none;html=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1010" y="585" as="targetPoint"/>
                        <mxPoint x="1010" y="665" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="失败" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="61">
                    <mxGeometry x="0.1096" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="64" value="主status为 INIT&#10;cloud_demand.ppl_version_group_record_item &#10;copy to cloud_demand.ppl_stock_supply_req" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="190" y="340" width="280" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>