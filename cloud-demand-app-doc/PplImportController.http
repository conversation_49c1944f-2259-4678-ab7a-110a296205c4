


@commonUrl = {{local}}/cloud-demand-app/ppl13week?api_key=no\n{{contentType}} 



POST {{commonUrl}}

{
    "method": "querySavedPplOrder",
    "jsonrpc": "2.0",
    "params": { 
        "year": 2022,
        "month": 11,
        "industry": "行业测试",
        "warZone": "战区测试",
        "customerType": "EXISTING",
        "customerUin": "customerUinTeset",
        "customerShortName": "customerShortName",
        "customerName": "customerName"
    },
    "id": "666666"
}



### 
POST {{commonUrl}}

{
    "method": "queryPplResourceDetail",
    "jsonrpc": "2.0",
    "params": { 
        "pplOrder": "PN2210211025"
    },
    "id": "666666"
}


### 
POST {{commonUrl}}

{
    "method": "submitPpl",
    "jsonrpc": "2.0",
    "params": { 
        # "pplOrder": "PN2210211025",
        "year": 2022,
        "month": 11,
        "industry": "行业测试",
        "warZone": "战区测试",
        "customerType": "EXISTING",
        "customerUin": "customerUinTeset",
        "customerShortName": "customerShortName",
        "customerName": "customerName",

        "resources":[
            {
                "pplId": "insert",
                "type": "insert",
                "product":"productTEST1",
                "demandType":"demandTypeTEST1"
            }
        ]
        
    },
    "id": "666666"
}



### 
POST {{commonUrl}}

{
    "method": "queryPplList",
    "jsonrpc": "2.0",
    "params": { 
        "status": "VALID"
        
    },
    "id": "666666"
}


###
POST {{commonUrl}}

{
    "method": "queryPplInfo",
    "jsonrpc": "2.0",
    "params": { 
        "pplOrder": "PN2210211027"
        
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16672160232460.7692477121571955",
  "method": "queryPplInfo",
  "params": {
    "pplOrder": "PN2210310009"
  }
}


###
POST {{commonUrl}}

{
    "method": "queryAllCustomShortName",
    "jsonrpc": "2.0",
    "params": {  
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "queryPplItem",
    "jsonrpc": "2.0",
    "params": { 

        # "pplOrder": "PN2210251041",
        "pplId": "PN2210251041-001"
        
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16672179688160.42985136761900744",
  "method": "queryResourceInfo",
  "params": {
    "pplOrder": "PN2210310010"
  }
}

####
POST {{commonUrl}}

{
    "method": "queryAllWarZoneName",
    "jsonrpc": "2.0",
    "params": { 
        
    },
    "id": "666666"
}




###
POST {{commonUrl}}

{
    "method": "confirmIntervene",
    "jsonrpc": "2.0",
    "params": { 
        "pplOrder": "PN2210261054"
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{"jsonrpc":"2.0","id":"cloud-demand-app-16667710566660.5545823931576599","method":"submitPpl","params":{"customerType":"EXISTING","customerUin":"888","industryDept":"战略客户部","industry":"外部行业","warZone":"华南","customerName":"美团集团","customerSource":"外部客户","year":2022,"month":10,"submitType":"save","resources":[{"product":"CVM&CBS","demandType":"新增需求","demandScene":"新项目","projectName":null,"billType":"按需计费","winRate":null,"beginBuyDate":"2022-10-19","endBuyDate":"2022-10-29","note":"brightwwu edit","regionName":"随机地域","zoneName":"随机可用区","type":"insert","instanceType":"S1","instanceModel":"S3.MEDIUM4","instanceNum":2,"alternativeInstanceType":["s1","s2"],"systemDiskType":"SSD","systemDiskStorage":40,"dataDiskType":"SSD","dataDiskStorage":30,"dataDiskNum":1,"affinityType":"无","affinityValue":1}]}}


###
POST {{commonUrl}}

{
    "method": "queryInstanceFamily",
    "jsonrpc": "2.0",
    "params": { 
        "zoneName" : "ap-guangzhou-3"
    },
    "id": "666666"
} 



###
POST {{commonUrl}}

{
    "method": "queryPplRecord",
    "jsonrpc": "2.0",
    "params": { 

        "pplId": "PN2210310014-001"
        
    },
    "id": "666666"
}

####
curl --request POST \
  --url 'http://localhost:80/cloud-demand-app/ppl13week?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"method": "queryPplRecord","jsonrpc": "2.0","params": {"pplId": "PN2210261054-002"},"id": "666666"}'



### 
POST {{commonUrl}}

{
    "method": "queryPplResourceDetail",
    "jsonrpc": "2.0",
    "params": { 
        "pplOrder": "PN2210311019"
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16672251668500.28947071440288474",
  "method": "queryPplRecord",
  "params": {
    "pplId": "PN2210310017-004"
  }
}
###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16672845231430.07753205366252569",
  "method": "queryPplRecord",
  "params": {
    "pplId": "PN2210310017-004"
  }
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16673060800740.6944042833779085",
  "method": "submitPpl",
  "params": {
    "customerType": "EXISTING",
    "customerUin": "100010274760",
    "industryDept": "运营管理部",
    "industry": "电商",
    "warZone": "中长尾企业",
    "customerName": "杭州永卓电子商务有限公司",
    "customerSource": "中长尾",
    "customerShortName": "杭州永卓电子商务有限公司",
    "year": 2022,
    "month": 11,
    "submitType": "submit",
    "resources": [
      {
        "product": "CVM&CBS",
        "demandType": "NEW",
        "demandScene": "存量扩容",
        "billType": "按量计费",
        "beginBuyDate": "2022-11-01",
        "endBuyDate": "2022-11-01",
        "regionName": "北京",
        "zoneName": "北京七区",
        "type": "insert",
        "instanceType": "BMDK1",
        "instanceModel": "BMDK1.32XLARGE512",
        "instanceNum": 1,
        "alternativeInstanceType": [],
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 0
      }
    ]
  }
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16678794285010.674944929499363",
  "method": "submitPpl",
  "params": {
    "customerType": "EXISTING",
    "customerUin": "100010274760",
    "industryDept": "运营管理部",
    "industry": "电商",
    "warZone": "中长尾企业",
    "customerName": "杭州永卓电子商务有限公司",
    "customerSource": "中长尾",
    "customerShortName": "杭州永卓电子商务有限公司",
    "year": 2022,
    "month": 11,
    "pplOrder": "PN2211080003",
    "submitType": "save",
    "resources": [
      {
        "recordVersion": 10303,
        "pplOrder": "PN2211080003",
        "product": "CVM&CBS",
        "demandType": "NEW",
        "demandTypeName": "新增需求",
        "demandScene": "存量扩容",
        "projectName": null,
        "billType": "按量计费",
        "winRate": null,
        "beginBuyDate": "2022-11-01",
        "endBuyDate": "2022-11-01",
        "beginElasticDate": "",
        "endElasticDate": "",
        "note": null,
        "regionName": "石家庄",
        "zoneName": "石家庄一区",
        "instanceType": "S5",
        "instanceModel": "S5.4XLARGE32",
        "instanceNum": 1,
        "alternativeInstanceType": null,
        "affinityType": "母机",
        "affinityValue": 1,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "changeJson": null,
        "operateType": "insert",
        "type": "insert"
      },
      {
        "statusName": null,
        "status": null,
        "recordVersion": null,
        "pplOrder": null,
        "pplId": null,
        "product": "CVM&CBS",
        "demandType": "ELASTIC",
        "demandTypeName": "弹性需求",
        "demandScene": "其他",
        "projectName": "23",
        "billType": "按量计费",
        "winRate": 1,
        "beginBuyDate": "2022-11-01",
        "endBuyDate": "2022-11-09",
        "beginElasticDate": "00:03",
        "endElasticDate": "00:05",
        "note": "12",
        "regionName": "北京",
        "zoneName": "随机可用区",
        "instanceType": "BMG5i",
        "instanceModel": "BMG5i.24XLARGE384",
        "instanceNum": 1,
        "alternativeInstanceType": [
          "IT5",
          "S5",
          "S6",
          "SA2",
          "SA3"
        ],
        "affinityType": "母机",
        "affinityValue": 1,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "changeJson": null,
        "operateType": null,
        "type": "insert"
      },
      {
        "statusName": null,
        "status": null,
        "recordVersion": null,
        "pplOrder": null,
        "pplId": null,
        "product": "CVM&CBS",
        "demandType": "ELASTIC",
        "demandTypeName": "弹性需求",
        "demandScene": "其他",
        "projectName": "23",
        "billType": "按量计费",
        "winRate": null,
        "beginBuyDate": "2022-11-01",
        "endBuyDate": "2022-11-09",
        "beginElasticDate": "00:03",
        "endElasticDate": "00:05",
        "note": "12",
        "regionName": "北京",
        "zoneName": "随机可用区",
        "instanceType": "BMG5i",
        "instanceModel": "BMG5i.24XLARGE384",
        "instanceNum": 1,
        "alternativeInstanceType": null,
        "affinityType": "母机",
        "affinityValue": 1,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "changeJson": null,
        "operateType": null,
        "type": "insert"
      },
      {
        "statusName": null,
        "status": null,
        "recordVersion": null,
        "pplOrder": null,
        "pplId": null,
        "product": "CVM&CBS",
        "demandType": "ELASTIC",
        "demandTypeName": "弹性需求",
        "demandScene": "其他",
        "projectName": "23",
        "billType": "按量计费",
        "winRate": 1,
        "beginBuyDate": "2022-11-01",
        "endBuyDate": "2022-11-09",
        "beginElasticDate": "00:03",
        "endElasticDate": "00:05",
        "note": "12",
        "regionName": "北京",
        "zoneName": "随机可用区",
        "instanceType": "BMG5i",
        "instanceModel": "BMG5i.24XLARGE384",
        "instanceNum": 1,
        "alternativeInstanceType": [
          "IT5",
          "S5",
          "S6",
          "SA2",
          "SA3"
        ],
        "affinityType": "母机",
        "affinityValue": 1,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "changeJson": null,
        "operateType": null,
        "type": "insert"
      },
      {
        "statusName": null,
        "status": null,
        "recordVersion": null,
        "pplOrder": null,
        "pplId": null,
        "product": "CVM&CBS",
        "demandType": "ELASTIC",
        "demandTypeName": "弹性需求",
        "demandScene": "其他",
        "projectName": "23",
        "billType": "按量计费",
        "winRate": null,
        "beginBuyDate": "2022-11-01",
        "endBuyDate": "2022-11-09",
        "beginElasticDate": "00:03",
        "endElasticDate": "00:05",
        "note": "12",
        "regionName": "北京",
        "zoneName": "随机可用区",
        "instanceType": "BMG5i",
        "instanceModel": "BMG5i.24XLARGE384",
        "instanceNum": 1,
        "alternativeInstanceType": null,
        "affinityType": "母机",
        "affinityValue": 1,
        "systemDiskType": "高性能",
        "systemDiskStorage": 50,
        "systemDiskNum": 1,
        "dataDiskType": "高性能",
        "dataDiskStorage": 50,
        "dataDiskNum": 1,
        "changeJson": null,
        "operateType": null,
        "type": "insert"
      }
    ]
  }
}


###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16679618546660.5184718466595548",
  "method": "queryResourceInfo",
  "params": {
    "pplOrder": "PN2211090001"
  }
}


###
http://localhost:80/cloud-demand-app/ops/ppl13_total_amount

curl --request POST \
  --url 'http://localhost:80/cloud-demand-app/ppl13week?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"jsonrpc": "2.0","id": "cloud-demand-app-16679618546660.5184718466595548","method": "queryResourceInfo","params": {"pplOrder": "PN2211090001"}}'


###
# WAR_ZONE CUSTOMER_NAME REGION INSTANCE_TYPE
POST  {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-16720474392860.10953580772920946",
  "method": "queryVersionGroupView",
  "params": {
    "groupId": 69,
    "type": "INSTANCE_TYPE",
    "startDate": "",
    "endDate": "",
    # "demandType": "NEW",
    "resourceType": "CORE",
    "regionNames": [],
    "warZoneNames": [],
    "instanceTypes": [],
    "demandScenes": [],
    "customerShortNames": [],
    "customerUins": []
  }
}

###
POST http://*************/cloud-demand-app/industry/annual/api/appendSyncRZIDict?api_key=no


###
GET http://localhost/cloud-demand-app/ops/sendPplMeddleMail



###
POST {{commonUrl}}

{
    "jsonrpc": "2.0",
    "id": "cloud-demand-app-16697247709700.1415258803724917",
    "method": "updatePplItem",
    "params": {
        "pplOrder": "PN2211110002",
        "resources": [
            {
                "product": "CVM&CBS",
                "demandType": "NEW",
                "demandScene": "新项目上线",
                "projectName": "23",
                "billType": "按量计费",
                "winRate": 1,
                "beginBuyDate": "2022-11-05",
                "endBuyDate": "2022-11-09",
                "note": "我去问",
                "regionName": "重庆",
                "zoneName": "重庆一区",
                "type": "update",
                "instanceType": "C2",
                "instanceModel": "C2.LARGE16",
                "instanceNum": 14,
                "alternativeInstanceType": null,
                "systemDiskType": "高性能",
                "systemDiskStorage": 50,
                "dataDiskType": "高性能",
                "dataDiskStorage": 50,
                "dataDiskNum": 1,
                "affinityType": "母机",
                "affinityValue": 1,
                "coreNum": 4,
                "totalCoreNum": 56,
                "totalDiskNum": 1400,
                "pplId": "PN2211110002-005"
            },
            {
                "product": "CVM&CBS",
                "demandType": "NEW",
                "demandScene": "新项目上线",
                "projectName": "23",
                "billType": "按量计费",
                "winRate": 1,
                "beginBuyDate": "2022-11-05",
                "endBuyDate": "2022-11-09",
                "note": "我去问",
                "regionName": "重庆",
                "zoneName": "重庆一区",
                "type": "insert",
                "instanceType": "C2",
                "instanceModel": "C2.LARGE16",
                "instanceNum": 14,
                "alternativeInstanceType": null,
                "systemDiskType": "高性能",
                "systemDiskStorage": 50,
                "dataDiskType": "高性能",
                "dataDiskStorage": 50,
                "dataDiskNum": 1,
                "affinityType": "母机",
                "affinityValue": 1,
                "coreNum": 4,
                "totalCoreNum": 56,
                "totalDiskNum": 1400
            }
        ]
    }
}



