
###
###
POST http://localhost/cloud-demand-app/ppl13week-forecast/?api_key=no
{{contentType}}

{
    "method": "queryRateViewFittingPlot",
    "jsonrpc": "2.0",
    "params": { 
        # "taskIds": [848],
        "taskIds": [861],
        # 1,2,3,532,
        "predictIndex":"3",
        "startYearMonth": "2023-09",
        "endYearMonth": "2024-11",

        # ACCURACY_TREND("准确率趋势图"),
        # MODEL_RATIO("机型占比图"),
        # REGION_RATIO("地域占比图"),
        # REGION_MODEL_RATIO("地域占比图");
        "plotType": "ACCURACY_TREND",

        "ginsFamilies": [],
        "regionNames": []
    },
    "id": "666666"
}


###

###
POST http://localhost/cloud-demand-app/ppl13week-forecast/?api_key=no
{{contentType}}

{
  "method": "queryForecastCategory",
  "jsonrpc": "2.0",
  "params": { 
    "serialInterval": "WEEK"
  },
  "id": "666666"
}

### 给行业数据看板的接口
POST http://localhost/cloud-demand-app/ppl13week-forecast/?api_key=no
{{contentType}}

{
    "method": "queryRateViewFittingPlotForMrp",
    "jsonrpc": "2.0",
    "params": { 
        # 1,2,3,532,
        "predictIndex":"532",
        "startYearMonth": "2023-09",
        "endYearMonth": "2024-01",
        "plotType": "ACCURACY_TREND"

        # "ginsFamilies": ["S5"],
        # "regionNames": ["广州"]
    },
    "id": "666666"
}


### rsp
POST ************:9102

{
  "jsonrpc": "2.0",
  "id": "666666",
  "x_trace_id": "4cd50995a0bed35b",
  "transToPplVersionData": {
    "newDetail": [
      {
        "yearMonth": "202309",
        "predictCoreNum": 455584.1419,
        "realCoreNum": 480640.40917,
        "accuracyRate": 0.744737947221
      },
      {
        "yearMonth": "202310",
        "predictCoreNum": 472495.73292,
        "realCoreNum": 455855.97357,
        "accuracyRate": 0.80838498588
      }
    ],
    "retDetail": [
      {
        "yearMonth": "202309",
        "predictCoreNum": 422136.14445,
        "realCoreNum": 429188.20402,
        "accuracyRate": 0.793872000286
      },
      {
        "yearMonth": "202310",
        "predictCoreNum": 412816.24945,
        "realCoreNum": 386168.32064,
        "accuracyRate": 0.781358537864
      }
    ]
  }
}



###
POST http://localhost:80/cloud-demand-app/ppl13week-forecast/?api_key=no
{{contentType}}

{
    "method": "queryRateViewDetail",
    "jsonrpc": "2.0",
    "params": { 
        # "taskIds": [848],
        "taskIds": [861],

        "startYearMonth": "2023-12",
        "endYearMonth": "2024-02",
        "ginsFamilies": [],
        "regionNames": []
    },
    "id": "666666"
}

###
curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17037443630550.3133552098136769","method":"queryRateViewFittingPlot","params":{"taskIds":[861],"startStatTime":"2023-12-18","endStatTime":"2023-12-24","plotType":"ACCURACY_TREND","predictIndex":1,"ginsFamilies":[],"regionNames":[]}}' \
  --compressed


####
  curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17037484792030.8045902019426092","method":"queryRateViewFittingPlot","params":{"taskIds":[861],"startStatTime":"2023-12-11","endStatTime":"2024-02-11","plotType":"ACCURACY_TREND","predictIndex":1,"ginsFamilies":[],"regionNames":[]}}' \
  --compressed

###
curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryForecastCategory?api_key=no' \
  -H 'authority: exp-crp.woa.com' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json; charset=UTF-8' \
  -H 'cookie: ERP_REAL_USERNAME=fireflychen; _ra=_ra1701246366180.0.23722681351472485; PAAS_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJwYWFzIiwic3ViIjoiMjAzOTMzIiwibmFtZSI6ImZpcmVmbHljaGVuIiwiZW5jIjoiK2tVS3ZVb0FFeWlpNmFlNFFjdTBqZnd3Wm85bklWNmIvQm9XbWFoSDZFeE4xM2RQd3E2MDFRMkFJdkl4RTVaa09pQ2M2enBmSnRQQ3JSZGNxNkpzSkx5RzBSS0d4TFlNOEt3bDl5dTd6akE9IiwianRpIjoiMDd0cXNuZ3o3bXkwdm4wb3l3a3RsbyIsImlhdCI6MTcwMzYwMDIyNSwiZXhwIjoxNzAzNjQzNDI1LCJ2IjoiMjAyMjEwIn0.TNrZWpZ2TViWv_qF7ksYHJtQ3f3HkZvQuPKUrAaNBv0; PAAS_TOKEN_HTTPS=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJwYWFzIiwic3ViIjoiMjAzOTMzIiwibmFtZSI6ImZpcmVmbHljaGVuIiwiZW5jIjoiK2tVS3ZVb0FFeWlpNmFlNFFjdTBqZnd3Wm85bklWNmIvQm9XbWFoSDZFeE4xM2RQd3E2MDFRMkFJdkl4RTVaa09pQ2M2enBmSnRQQ3JSZGNxNkpzSkx5RzBSS0d4TFlNOEt3bDl5dTd6akE9IiwianRpIjoiMDd0cXNuZ3o3bXkwdm4wb3l3a3RsbyIsImlhdCI6MTcwMzYwMDIyNSwiZXhwIjoxNzAzNjQzNDI1LCJ2IjoiMjAyMjEwIn0.TNrZWpZ2TViWv_qF7ksYHJtQ3f3HkZvQuPKUrAaNBv0; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6Imp3RG5HdDFjR2RaaFgySmFvV0tkcDJOOUVoMkJ4RGtiIiwiaXNzIjoiMTAuOTkuMTUuNDMiLCJpYXQiOiIyMDIzLTEyLTI2VDIyOjE3OjA1LjkxNTU0MjU0MiswODowMCIsImF1ZCI6IjEwLjIxLjE4LjUwIiwiaGFzaCI6IkI0MzI4NzREQjI4QzgyMjIzRkFENUFBRjFGM0VDOTNGMTk5MDJDMkUyOUQ2Q0E3QzQyRURGMzk3NTZDQzA5OTciLCJuaCI6IkY5NjM3QTMxOThEMzkwNTVEODc4MzY2NzI5MEE2MkQzRTk4QUEwRTA1NzM4NTI5OUExQURGOUFFNDI3MTE5MkMifQ; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6Imp3RG5HdDFjR2RaaFgySmFvV0tkcDJOOUVoMkJ4RGtiIiwiaXNzIjoiMTAuOTkuMTUuNDMiLCJpYXQiOiIyMDIzLTEyLTI2VDIyOjE3OjA1LjkxNTU0MjU0MiswODowMCIsImF1ZCI6IjEwLjIxLjE4LjUwIiwiaGFzaCI6IkI0MzI4NzREQjI4QzgyMjIzRkFENUFBRjFGM0VDOTNGMTk5MDJDMkUyOUQ2Q0E3QzQyRURGMzk3NTZDQzA5OTciLCJuaCI6IkY5NjM3QTMxOThEMzkwNTVEODc4MzY2NzI5MEE2MkQzRTk4QUEwRTA1NzM4NTI5OUExQURGOUFFNDI3MTE5MkMifQ; x_host_key_access_https=a2b636dc3c2daf4fe5d7961c7cf90df506943f53_s; x-client-ssid=1a11630a:018ca67d4fc3:0dfeb5; x-tofapi-host-key=18ca67d50f2-cd48ae581664c57502e9730c110119347f69b580; ERP_USERNAME=fireflychen; RIO_TOKEN=0.s6ctbk.3jaiT2O8eJA3AHD98hpohsNzI7aijrMp0pZn4utMFRETCF_jPK6GuLe60DxcUJt398liiI1F9ziXQFENNlm2l_hMjXrtBlt8a5qp81jr8kX0eLKVDvc6n4CxMe8Q_H4gM-xHvYBF0jvaQVLlGaRPrUiJ34I2vjoaOG4Pzi9UCn-th-mCO4zAjugn4IjIGS9CjKsyF78-otjQ0dfm5qM6G3LcYtdebsQkhibY7P5Y3g6MEAyCIeNv_2DM4_VjhB0WhF0iP6KvIjQpi-pwLyfIw4f-CxnshxOxXzJZxF4JylQSeXbQUN25ld4QWJV1Q2b925sVXDxNuFBQ51PL8_lWEOVFbJjiADglzm2ovgugrWjN-gpUvgGdUHK0NQx4IjIt209yDIebCmzmg4--KgJS7TK2JSJ0NphrUuhKla3Md_tWS5KXdSnGK0UWMuvnI2MRii1NupD_3ddR68mGGNqn1-_vCKEpYXY9HrJNgvhHQT6jU9rgmF-_S_6BNpbDhfol-o2boJCMPW26AMEAnn8Z0ijgowmqVPjGzMGnhe7qwPs1E4YU8wdtjsncuwTLlLdaYKz7Yw.KKB2vjtz8UDNmqvHAe3oJw; RIO_TOKEN_HTTPS=0.s6ctbk.3jaiT2O8eJA3AHD98hpohsNzI7aijrMp0pZn4utMFRETCF_jPK6GuLe60DxcUJt398liiI1F9ziXQFENNlm2l_hMjXrtBlt8a5qp81jr8kX0eLKVDvc6n4CxMe8Q_H4gM-xHvYBF0jvaQVLlGaRPrUiJ34I2vjoaOG4Pzi9UCn-th-mCO4zAjugn4IjIGS9CjKsyF78-otjQ0dfm5qM6G3LcYtdebsQkhibY7P5Y3g6MEAyCIeNv_2DM4_VjhB0WhF0iP6KvIjQpi-pwLyfIw4f-CxnshxOxXzJZxF4JylQSeXbQUN25ld4QWJV1Q2b925sVXDxNuFBQ51PL8_lWEOVFbJjiADglzm2ovgugrWjN-gpUvgGdUHK0NQx4IjIt209yDIebCmzmg4--KgJS7TK2JSJ0NphrUuhKla3Md_tWS5KXdSnGK0UWMuvnI2MRii1NupD_3ddR68mGGNqn1-_vCKEpYXY9HrJNgvhHQT6jU9rgmF-_S_6BNpbDhfol-o2boJCMPW26AMEAnn8Z0ijgowmqVPjGzMGnhe7qwPs1E4YU8wdtjsncuwTLlLdaYKz7Yw.KKB2vjtz8UDNmqvHAe3oJw; P_RIO_TOKEN=0.s6ctbk.3lejB9O1etubXt1buAIUzU1I7pAQj-YbprZtl8OFAiZim4N2ZP0cyUvCplnJwf1mVWmpsBwNuUR1W8UjNdlchgQJ5aMe8R0gpeUMJbagGW55zqon-zzPBgBDZTWwXFG4_poMsEb8E1Nhh0Xp80jHhWScNH7KYiowdA2DqOXFEAPSeKtvmUHuVu0J109RPhZEDzUQbxyKX52i9GA9FCCltf2OHs5tLGTG9ECyAFbrDNL3I1SNR215K2NMdXs56mscs39VPE2051S_ErpHeXSc9Xd9Bhap2lMsjt0UW1rDZ7PUwxQK9OldGy2fF_40YThNBKGyje_TI8DRoEKnit1ttWwTNllO7WuwSfShc5ARI-N_nAza7irn6AwLfeWH6ecXkyjLF3KcfTqNkX-0Ev8Xez-O5oNDrCliJ4cOI1AXPENVSY_VWnK4VfGQ2D32hWP0daHDGVfxHt6QmtCqkYQK6vhMRu7QNVeqDU1U70NURVbuOuBPKpmMP9FSBArCVNB47hxupHda1MjidBTUDQP20AKc8h3lqyPMT6R397l5ZijFyD2WN48AC9DEZa74s3IklWbQmJ2x6NsaWRgh9yDWFF7jjBj5h0c9A9LTrY10LuAfIFnZmVyS4_F1OxeL2jbYPqDcXjPaqWUI9Rtx3BHcN1-bpZKyZ6TQ1VHMC_xuH5HkdlXA97F5b7KdSOJVE9JkPgLPtUqOA_NPgGF5Vx6fog1SSfy6fCTQ2X15l0l-MF4NEqq1uTTBONIWNnraAZ-NftYBjQ.7WZDDwS9DUqVcCF6qcfV7Q' \
  -H 'origin: https://exp-crp.woa.com' \
  -H 'pragma: no-cache' \
  -H 'referer: https://exp-crp.woa.com/forecast-model/long/manageWeek' \
  -H 'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"' \
  -H 'sec-ch-ua-mobile: ?1' \
  -H 'sec-ch-ua-platform: "Android"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17037316186760.26762691759225565","method":"queryForecastCategory","params":{"serialInterval":"WEEK"}}' \
  --compressed



{
  "jsonrpc": "2.0",
  "id": "666666",
  "x_trace_id": "8b0ab418a84bcb38",
  "transToPplVersionData": {
    "detail": [
      {
        # 年月
        "yearMonth": "202310",
        # 测算维度
        "key": "[弗吉尼亚,IT5]",
        #  地域
        "regionName": "弗吉尼亚",
        #  机型
        "instanceType": "IT5",
        #  存量值
        "curCoreNum": 340,
        "newDetail": {
        #  提前1期新增预测值
          "predictCoreNum1": 65.333,
          "predictCoreNum2": 51.556,
          "predictCoreNum3": 72.889,
          "predictCoreNum4": 67.643,
          "predictCoreNum5": 77.812,
          "predictCoreNum6": 65.799,
        #  532新增预测值
          "predictCoreNum532": 64.9779,
        #  提前1期新增加权准确率
          "weightRate1": 0,
          "weightRate2": 0,
          "weightRate3": 0,
          "weightRate4": 0,
          "weightRate5": 0,
          "weightRate6": 0,
          "weightRate532": 0,
        #  提前1期新增minMax
          "minMax1": 0,
          "minMax2": 0,
          "minMax3": 0,
          "minMax4": 0,
          "minMax5": 0,
          "minMax6": 0,
          "minMax532": 0,
        #   新增实际值
          "realCoreNum": 0,
        #   新增实际值占比
          "weight": 0
        },
        #  退回同上，把 新增 -> 退回
        "retDetail": {
          "predictCoreNum1": 53.333,
          "predictCoreNum2": 71.111,
          "predictCoreNum3": 44.542,
          "predictCoreNum4": 9.112,
          "predictCoreNum5": 10.812,
          "predictCoreNum6": 12.797,
          "predictCoreNum532": 54.2709,
          "weightRate1": 0,
          "weightRate2": 0,
          "weightRate3": 0,
          "weightRate4": 0,
          "weightRate5": 0,
          "weightRate6": 0,
          "weightRate532": 0,
          "minMax1": 0,
          "minMax2": 0,
          "minMax3": 0,
          "minMax4": 0,
          "minMax5": 0,
          "minMax6": 0,
          "minMax532": 0,
          "realCoreNum": 0,
          "weight": 0
        }
      }
      ]}}



###

# curl  -v --request POST
#  --url 'http://crp.woa.com:80/cloud-demand-app/ppl13week-forecast/?api_key=no'
#  --header 'content-type: application/json'
#  --header 'cookie:RIO_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI2NiJ9.eyJpc3MiOiJ0b2Y0YXV0aCIsInN1YiI6IjIwMzkzMyIsImV4cCI6MTcwMjYwNjQ2NSwibmFtZSI6ImZpcmVmbHljaGVuIiwiZW5jIjoiRFFid2NsQjhEOUJkSzR3SnlGcTZRYVhta2J1eDladk5TdnpJSHFxS1hLNWJVTkp2NlhjK1U2Q282dzVWeVJ3NlVOS0p3cjg3NlFaOUpQU2VjbDRubXkwQnNBdWxwMEMwU1k2L2xFM3A0dkNIZ0Z4UFc1aDBZeTVmdlpEK2w1NGFFME1iTWlaZUs3NVYyK1Rrbk83bFc3V08rQWpJTGVDMlhIS2JIbUZzcUNMakNHNCtBdFNRa1hCWkNZZ2wrL1BTQWtTbjRVTFA2eTMyTXNURXYyVDIyTUx4K1JNWHNkWTRlM3hFNjBIdyt2aVBDWGI5SnRzQXljZlY3K1BmL1pIaEVYSFg0NDZYZnA4QnU1ZDY2RElWQTFQZ2owQkxxcHJhTithd2JITlM4akplaW9oWjFKdUc1NHhHUG9Hb285bGRWekloTHYzTElZakRrak0wZTRNVzdSRkFQQTNNWjlMc0cwbDRUM0lpU3RQKythdnBaRjBWa05IL1J5UHRLbDZkUGFBelVLby80dkhtTDRlenNqODdvL1YwR2w3R09aQW9iVTk2dmF1WnptMXJIM0dhTWwxUGhWaC9jVzZzZkZjci8zNzRLaGhuNDQvaU93eEQxWWZ4U3FLSzk5QnZkV2lsZlVaV2JuQ2gzU0NvQzFERjEvRmU2Qm56d3JSRTdSSGYvUE56RzVjK2oxVk4vdVhuWUpBc1ppbmxQeE5nSHdFVWE4dUNxb2c1ZERTSEdidTBHTWJ5Qy9nZndUb2trMG9rTGZmSFRDeWVub25teURPOEhCYklYMTZkRW1VTjZYZEYxSFFkZFk0alJZZFpnRXFBWDFHb1RvVi9MaEgyaGV3S2pSRzFPOFp0RlRRaDQyYis5NlJWTVhuRzdrOG1ySUw2UFl6M2paOWgxS1REcjY3UE1KVEE2WFk4N2hvWWVCQ0RLeWlqQU9HeG4ycVlvQnBkME1GUkVNR082eWliNEVuVVNadmZHR1BsMHd0S2dzeWl3bXpsYi9hbkdKaW1yRzF5NTJhUmxOTkJtQnFGWmJQdWM4ZWdNK0F2emZocHZQMFRONTFiQW9LZ29ucGYxNk1nSGI2eXM2dWt0ampabGRNbzljVXBBWHdqSUpTSjZQaHhLTURmd2NxdU05ZWNaaktHTjg2RmZVMGhTZnZTMlJIMDFDUGZaZElGQzh5NEpQWEl2ZzYxeW1DdHA1MG9ENHNxVEhtYk5LR1NKclBZNkFhNlNyTzVRdGdsYWhHMmVKSm5abWxRaUNLb2NIRGVvR2E5WDN4TldzRERrQW80NnhlTG5jRVRkWUhpbVBISVNpQWpaaHpBK1hpbzlnZWpPNE1MTVplR0tQUlBxRDdKbmFDOXBuaEVwNnpKT2t1bmJiZkVGR3YyR0M5UVd4dmFSUmw1YjREWHlaay90am1PTFVWUVcvS3JBQWRwTkNsdE1FaVdwNk5yMXhybXJkVklIY1NZdnFxTG9zZjJDQ21yWGM2T3pwRG9WWWZKcTNueFA5bTFWTEk5Zk16UUtnOUlnNkZrc0llN1dRVTRneUI1QW5acVNDVnJiRXJ2dmZic3ZDWGxnRXZuNEFJTDh3akpsVVB6Ymg4ZjB4dkY1VFF5Y0c5S3JScVBBQytEQ0FLM0lsbUxraUN4N2c9PSIsImp0aSI6IjA3dGtkNHdmNnlia2xxMGhoam1sdm4iLCJpYXQiOjE3MDI1MjAwNjUsInYiOiIyMDIyMTAifQ.vJ_Yr3i3taIGpByJiwKSttmxk9Wp5ZKgbAlXd3322eU'
#  --header 'user-agent: vscode-restclient'
#  --data '{"method": "queryRateViewFittingPlot","jsonrpc": "2.0","params": {"taskIds": [848],"predictIndex":"532","startYearMonth": "2023-09","endYearMonth": "2023-10","plotType": "ACCURACY_TREND","ginsFamilies": [],"regionNames": []},"id": "666666"}'

###

curl 'https://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
  -H 'accept: application/json, text/plain, */*' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17026121745970.4527375773958786","method":"queryRateViewFittingPlot","params":{"taskIds":[848],"startYearMonth":"2021-12","endYearMonth":"2024-05","plotType":"ACCURACY_TREND","predictIndex":1,"ginsFamilies":[],"regionNames":[]}}' \
  --compressed


###
  curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewDetail?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17038370284580.5175646317522384","method":"queryRateViewDetail","params":{"taskIds":[861],"startStatTime":"2023-12-04","endStatTime":"2024-02-18","plotType":"ACCURACY_TREND","predictIndex":1,"ginsFamilies":["S4"],"regionNames":["上海"]}}' \
  --compressed



###
  curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17042472083330.009064502661196627","method":"queryRateViewFittingPlot","params":{"taskIds":[971],"startYearMonth":"2023-11","endYearMonth":"2023-11","plotType":"ACCURACY_TREND","predictIndex":55,"ginsFamilies":["S4"],"regionNames":[]}}' \
  --compressed



###

curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17048552391370.00915531281648918","method":"queryRateViewFittingPlot","params":{"taskIds":[1020],"startYearMonth":"2023-07","endYearMonth":"2024-06","plotType":"ACCURACY_TREND","predictIndex":55,"ginsFamilies":[],"regionNames":[]}}' \
  --compressed




### 
curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17048861365680.24855253015576073","method":"queryRateViewFittingPlot","params":{"taskIds":[981],"startStatTime":"2023-10-09","endStatTime":"2024-02-25","plotType":"ACCURACY_TREND","predictIndex":55,"ginsFamilies":[],"regionNames":[]}}' \
  --compressed


###
  curl 'https://crp.woa.com/cloud-demand-app/ppl13week-forecast/queryForecastCategory' \
  -H 'content-type: application/json; charset=UTF-8' \
  -H 'cookie: ERP_REAL_USERNAME=fireflychen; _ra=_ra1701934369232.0.778671108070929; x_host_key_access_https=a2b636dc3c2daf4fe5d7961c7cf90df506943f53_s; x-client-ssid=5e11630a:018cd7ad62e0:01d0e3; x-tofapi-host-key=18cd7ad632c-65b3f8fc33fa31cd634466eb56ecab88ab398600; _DiggerUserClientId=911253022; uname=fireflychen; pgv_info=ssid=s1265296160; pgv_pvid=2974269696; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6ImliNzdxSWZveUNDaWl3dFJ1QXhzRWFWem5iME10Z3VEIiwiaXNzIjoiMTAuOTkuMjA4LjU3IiwiaWF0IjoiMjAyNC0wMS0wOVQxMDo1MTo0Ny4zMTIyNDYxMjUrMDg6MDAiLCJhdWQiOiIxMC4yMS4xOC40MCIsImhhc2giOiIzMUQ2RTU4OUJGNkY5RDI3MTQyQzVCNENGQ0RBNjg5QTRCQjAxNzk2RENDMkZEQUU3RjlFMzA2OUYxRTc3RUNBIiwibmgiOiIwOTZDNzUyRDVBRDczMTQ2RkVBRjlBQzRCMEUzRDhERThDRTA0QTFDOTk0QjU0RjAyRjA3RUE1RDlGMTkzRUE2In0; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22fireflychen%22%2C%22first_id%22%3A%2218cd7ad8ab23ee-03668a5c016798-1f525637-2007040-18cd7ad8ab3d08%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218cd7ad8ab23ee-03668a5c016798-1f525637-2007040-18cd7ad8ab3d08%22%7D; tapdsession=16898b640586d465394e2ba266428bc7; RIO_TOKEN=0.s70yot.oZjwQwgzTk3h6GtnXZK55zLH0sEvBV1XWyMSggzXogBiCDBC1d2Fz8Ab46CpPoc3Pp5BJvY2WZ55FP9PpSG8Nx9huKipUqOc3O7pt46MOzFpcXYgz5mlS0TTFfF4x4ot2jQyITmmoKo6cKF2Zdms0cmoe720k975RtOS8ci2FCqg7g3KAgQWImou2hGdLuWJltdqlIFLouNoQxlWaxjnh397AayWS2q_xrwUTclDJO8wmZhTTcfGdSOeRstF2AmyCTyYLs1gWZJjKQVbXM_it8eZJdlv_LYXC0Es8F5uEbb1_lEKEVx51M7qD0tTcXVBk4eFkZ9qgHxYYnvx2rX5OCqgLfRZe-pv0uojCPSYvAop-nan-eC_-FxwTWxonF4OEwsletJ4-PYtOwRXV8ubt862sLOmgBHMeiC4nLeVCKTkWkl43xjA0Rr9J-FRsDNIdRqqPl8cFqsWp2N5LhK2tkf543VQAtnsKUcXVQcezxE79vxE0Ewb9HjK0IZHYyzWweLN7PVk0Fq_GcU6dDJ2NKwPQZdMLpvmKTczIpP5CP1aV7y0BvATbczRjctZ.6ZaEqkWHUK7UVFQedthcJg; RIO_TOKEN_HTTPS=0.s70yot.oZjwQwgzTk3h6GtnXZK55zLH0sEvBV1XWyMSggzXogBiCDBC1d2Fz8Ab46CpPoc3Pp5BJvY2WZ55FP9PpSG8Nx9huKipUqOc3O7pt46MOzFpcXYgz5mlS0TTFfF4x4ot2jQyITmmoKo6cKF2Zdms0cmoe720k975RtOS8ci2FCqg7g3KAgQWImou2hGdLuWJltdqlIFLouNoQxlWaxjnh397AayWS2q_xrwUTclDJO8wmZhTTcfGdSOeRstF2AmyCTyYLs1gWZJjKQVbXM_it8eZJdlv_LYXC0Es8F5uEbb1_lEKEVx51M7qD0tTcXVBk4eFkZ9qgHxYYnvx2rX5OCqgLfRZe-pv0uojCPSYvAop-nan-eC_-FxwTWxonF4OEwsletJ4-PYtOwRXV8ubt862sLOmgBHMeiC4nLeVCKTkWkl43xjA0Rr9J-FRsDNIdRqqPl8cFqsWp2N5LhK2tkf543VQAtnsKUcXVQcezxE79vxE0Ewb9HjK0IZHYyzWweLN7PVk0Fq_GcU6dDJ2NKwPQZdMLpvmKTczIpP5CP1aV7y0BvATbczRjctZ.6ZaEqkWHUK7UVFQedthcJg; P_RIO_TOKEN=0.s70yot.oefxU7g6TAZNtsbBF4qhMFrRW1zf1MyT6RytdkZNLixwubhiUye_8oUbLEYPGsiLkB-5zUrsSjwkIvf--VJngPRYRT-VQPa0d9c9K5Y-SY25yBkMpTJn8BTzzyo4abdQvA9ZJ4agrmtHtCPyvpG-p0am3SB-17UYJDNaicJf06_RWch6yMPWGhQ3DbspsdbucsnXb1YoIY51pt1Ax-Nu3sA76grrdqQE7IZLn3DNM2mGncgPRY72ipvTsthNv5j_Pv50qHQZPjkxkbXNTyHNEPUfYRDByVTnBAudskXHLIYVebZMyb245jZ4p0pxcS0uRKzz0-0D3AucI8Go49MDgJm8xMBTKuUGTBMJZoCP6rmGSFfb6GhcogEO8mUBpZyb9wmTD1oZURhX_WhjFdq2vxD0O18NfSyy3HMbxGQrNtxhIaXx-uMkKFsXwo5NOFVFZc2t7mrVhpy6GO3qwvClLkoQKvtG3GdPwDVVij4POUfZM2uCeD0wFFOWwrS3DiM5IYPrhBACqjzZnvhq3Cd8-bmZuDPgVvS_VT4-C66gPy31E9dqvXv_gV89xe0_UiOW-8gEiMPy9TMvfwXYGGBinaRhevid9lQ6fpBe90mimQ-f29bzcA5AVTWBGgjB9JzXXx7wUmcbk8psemgSEwh8YUZwDVoZruhejcA3WsNjVfaFvxoQB8ANuJulWwsndbOzWg7EF6RFpFiuPaxRxD_V1E-Zjedbp5QYGR73JnTjkASlVMmqhFvM.f4w6kdLZeQGf1_H7aADL7g; tkex_csrf_token=grk9t9wrr0; DiggerTraceId=3836fd33-e83e-4032-8dc0-2bc5ad6d0dd0; DiggerTraceIdTs=1704871514115; bk_uid=fireflychen; bk_ticket=bIqqm5Pd6hu8vZN-TVjZB-jHmKPrzROfIJyvaD90xM4; ERP_USERNAME=kaijiazhang' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17049382857130.42440107392340165","method":"queryForecastCategory","params":{"serialInterval":"WEEK","resourcePool":"PUBLIC"}}' \
  --compressed


###
curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryAdjustDetail?api_key=no' \
  -H 'Content-Type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17053086584910.10625005633967444","method":"queryAdjustDetail","params":{"taskId":248,"outputVersionIds":[69],"timestamp":1705308658490,"sourceType":["全量(不含运营资源中心和算力中心)"]}}' \
  --compressed



### queryVersionGroupSpikeView




  curl 'https://exp-crp.woa.com/cloud-demand-app/ppl13week/comdPplExport' \
  -H 'authority: exp-crp.woa.com' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json' \
  -H 'origin: https://exp-crp.woa.com' \
  -H 'pragma: no-cache' \
  -H 'referer: https://exp-crp.woa.com/13ppl/view-approval/10201' \
  -H 'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"' \
  -H 'sec-ch-ua-mobile: ?1' \
  -H 'sec-ch-ua-platform: "Android"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  --compressed


###
curl 'http://localhost/cloud-demand-app/ppl13week/queryVersionGroupSpikeView?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17060070810070.6825623076216774","method":"queryVersionGroupSummary","params":{"groupId":10300,"versionCode":"V_20240126","resourceType":"CORE","regionNames":[],"warZoneNames":[],"instanceTypes":[],"demandScenes":[],"customerShortNames":[],"customerUins":[]}}' \
  --compressed


###
  curl 'http://localhost/cloud-demand-app/ppl13week/queryVersionGroupItem?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \


###
curl 'http://localhost/cloud-demand-app/ppl13week/queryVersionGroupItem?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17060097758450.5391769490055531","method":"queryVersionGroupItem","params":{"groupId":10300}}' \
  --compressed

###
  curl 'http://localhost/cloud-demand-app/ppl13week/comdPplExport?api_key=no' \
  -H 'authority: exp-crp.woa.com' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json' \
  -H 'origin: https://exp-crp.woa.com' \
  -H 'pragma: no-cache' \
  -H 'referer: https://exp-crp.woa.com/13ppl/view-approval/10201' \
  -H 'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"' \
  -H 'sec-ch-ua-mobile: ?1' \
  -H 'sec-ch-ua-platform: "Android"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36' \
  --compressed



  curl 'https://exp-crp.woa.com/cloud-demand-app/ppl13week/comdPplExport' \
  -H 'authority: exp-crp.woa.com' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json' \
  -H 'origin: https://exp-crp.woa.com' \
  -H 'pragma: no-cache' \
  -H 'referer: https://exp-crp.woa.com/13ppl/view-approval/10201' \
  -H 'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"' \
  -H 'sec-ch-ua-mobile: ?1' \
  -H 'sec-ch-ua-platform: "Android"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36' \
  --compressed


###
curl 'http://localhost/cloud-demand-app/ppl13week/queryVersionGroupSpikeView?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17060856284380.6304459363030406","method":"queryVersionGroupSpikeView","params":{"groupId":10242,"versionCode":"V_20240123","resourceType":"CORE","regionNames":[],"warZoneNames":[],"instanceTypes":["S5","SA3"],"demandScenes":[],"customerShortNames":[],"customerUins":[],"isComd":false}}' \
  --compressed
  


### queryVersionGroupForecastResultView

POST http://localhost/cloud-demand-app/ppl13week/queryVersionGroupForecastResultView?api_key=no
{{contentType}}

{
  "method": "queryVersionGroupForecastResultView",
  "jsonrpc": "2.0",
  "params": { 
    "groupId": 10918,
    "isKeepUin": true
  },
  "id": "666666"
}


### querySpikeExecuteDetail
POST http://localhost/cloud-demand-app/ppl13week/querySpikeExecuteDetail?api_key=no
{{contentType}}

{
  "method": "querySpikeExecuteDetail",
  "jsonrpc": "2.0",
  "params": { 
    "industryDept": "智慧行业一部"
  },
  "id": "666666"
}



###
curl 'http://localhost/cloud-demand-app/ppl13week/queryVersionGroupForecastResultView?api_key=no' \
  -H 'accept: application/json, text/plain, */*' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17083323265280.8594694797904665","method":"queryVersionGroupForecastResultView","params":{"groupId":10918,"versionCode":"V_20240223","resourceType":"CORE","regionNames":[],"warZoneNames":[],"instanceTypes":[],"demandScenes":[],"customerShortNames":[],"customerUins":[],"isKeepUin":true}}' \
  --compressed

###
curl 'http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17084164520500.31490324195122144","method":"queryRateViewFittingPlot","params":{"taskIds":[1899],"startYearMonth":"2023-08","endYearMonth":"2024-07","plotType":"ACCURACY_TREND","predictIndex":1,"ginsFamilies":[],"regionNames":[]}}' \
  --compressed


###
POST  http://localhost/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no
{{contentType}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-17116179204560.13716328500519892",
  "method": "queryRateViewFittingPlot",
  "params": {
    "taskIds": [
      2312
    ],
    "startYearMonth": "2023-09",
    "endYearMonth": "2024-08",
    "plotType": "ACCURACY_TREND",
    "predictIndex": 55,
    "ginsFamilies": [],
    "regionNames": []
  }
}



curl 'http://localhost/cloud-demand-app/ppl13week-forecast/createSplitVersion?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17124920041860.2719618732515463","method":"createSplitVersion","params":{"outputVersionName":"拆分一下","outputVersionType":"SPLIT_FOR_MIDDLE","taskIds":[2648]}}'


###
curl 'http://localhost/cloud-demand-app/ppl13week/queryVersionGroupForecastResultView?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data '{"jsonrpc":"2.0","id":"cloud-demand-app-17125477263550.834549941053077","method":"queryVersionGroupForecastResultView","params":{"groupId":12783,"versionCode":"V_20240514","resourceType":"CORE","regionNames":[],"warZoneNames":[],"instanceTypes":[],"demandScenes":[],"customerShortNames":[],"customerUins":[],"isKeepUin":false,"bizRangeType":"外部业务"}}'



###
curl 'http://lh/cloud-demand-app/ppl13week/queryVersionGroupForecastResultView?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data '{"jsonrpc":"2.0","id":"cloud-demand-app-17126532303060.982195421907303","method":"queryVersionGroupForecastResultView","params":{"groupId":12783,"versionCode":"V_20240514","resourceType":"CORE","regionNames":[],"warZoneNames":[],"instanceTypes":[],"demandScenes":[],"customerShortNames":[],"customerUins":[],"isKeepUin":false,"bizRangeType":"外部业务"}}'


### queryVersionGroupSpikeView
curl 'http://lh/cloud-demand-app/ppl13week/queryVersionGroupSpikeView?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17128072965930.7313479101571285","method":"queryVersionGroupSpikeView","params":{"groupId":12873,"versionCode":"V_20240521","resourceType":"CORE","regionNames":[],"warZoneNames":[],"instanceTypes":[],"demandScenes":[],"customerShortNames":[],"customerUins":[],"isComd":true,"isKeepUin":false,"bizRangeType":"外部业务"}}'





###
curl 'http://lh/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no' \
   -H 'content-type: application/json; charset=UTF-8' \   
   -d '{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-17154102915330.34249148033359433",
  "method": "queryRateViewFittingPlot",
  "params": {
    "taskIds": [
      2886
    ],
    "startYearMonth": "2023-10",
    "endYearMonth": "2024-09",
    "plotType": "ACCURACY_TREND",
    "predictIndex": 3,
    "ginsFamilies": [],
    "regionNames": []
  }
}'





['超参数', 'Bybit', 'VNG SINGAPORE', '完美世界征奇(上海)多媒体科技有限公司', '北京嘀嘀无限科技发展有限公司', '作业帮', '腾讯游戏(IEG CDN)', '京东云', 'Futu JP', '小红书', 'GGG', '上海游族互娱网络科技有限公司', '拼多多', 'PROXIMA BETA PTE. LIMITED', '乐读-好未来K9', '广州佰特', '富途网络科技（深圳）有限公司', '荣耀终端', '虎扑', '益世界集团', '广州库洛科技有限公司', '贪玩游戏', '超参数子公司', '完美世界', '北京算力大陆科技有限公司', '猿题库', '腾讯互动娱乐事业群', '快手', '欧派', '美团点评', '臻乐尔', '猫眼', '中央广播电视总台', '北京值得买科技股份有限公司', '深圳美西西餐饮管理有限公司']




###
POST http://lh/cloud-demand-app/ppl13week-forecast/createTransForm?api_key=no
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-17168631133420.4396017691196956",
  "method": "createTransForm",
  "params": {
    "outputVersionIds": [
      2088
    ],
    "startYearMonth": "2025-04",
    "endYearMonth": "2025-07",
    "versionCode": "V_20250512",
    "isAppend": false,
    "desc": "下发测试"
  }
}

###
POST http://crp.woa.com/cloud-demand-app/ppl13week-forecast/createTransForm?api_key=no
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-17168631133420.4396017691196956",
  "method": "createTransForm",
  "params": {
    "outputVersionIds": [
      13695
    ],
    "startYearMonth": "2025-04",
    "endYearMonth": "2025-07",
    "versionCode": "V_20250415",
    "isAppend": false,
    "desc": "生产下发EMR净增数据"
  }
}

###
curl 'http://localhost/cloud-demand-app/ppl13week/queryVersionGroupItem?api_key=no' \
  -H 'content-type: application/json; charset=UTF-8' \
  --data-raw '{"jsonrpc":"2.0","id":"cloud-demand-app-17447205373850.11572628930469131","method":"queryVersionGroupItem","params":{"groupId":18414}}'


###
{"jsonrpc":"2.0","id":"cloud-demand-app-17167922051650.31520570435772166","method":"createTransForm","params":{"outputVersionIds":[1697],"startYearMonth":"2024-07","endYearMonth":"2024-08","versionCode":"V_20240603","isAppend":false,"desc":"测试下发覆盖"}}


###
POST http://lh/cloud-demand-app/ppl13week-forecast/createSplitVersion?api_key=no
{{contentType}}

{"jsonrpc":"2.0","id":"cloud-demand-app-17168032071690.660407408835288","method":"createSplitVersion","params":{"outputVersionName":"兜底拆分","outputVersionType":"NEW_SPLIT_FOR_PPL","taskIds":[2880]}}





####
POST http://lh/cloud-demand-app/ppl13week-forecast/queryRateViewFittingPlot?api_key=no
{{contentType}}

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-17169520971220.3114403159275181",
  "method": "queryRateViewFittingPlot",
  "params": {
    "taskIds": [
      2886
    ],
    "startYearMonth": "2023-11",
    "endYearMonth": "2024-10",
    "plotType": "ACCURACY_TREND",
    "predictIndex": 532,
    "ginsFamilies": [],
    "regionNames": []
  }
}



### 提供给ppl行业录入中长尾的数据
###
POST http://lh/cloud-demand-app/ppl13week-forecast/querySplitDetailForPpl?api_key=no
content-type: application/json; charset=UTF-8

{
  "jsonrpc": "2.0",
  "id": "cloud-demand-app-17169520971220.3114403159275181",
  "method": "querySplitDetailForPpl",
  "params": {
    "product": "EKS"
  }
}




