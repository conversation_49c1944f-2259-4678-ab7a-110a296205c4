


@commonUrl = {{local}}/cloud-demand-app/repo-show?api_key=no\n{{contentType}} 




#  queryAllProductSummary



####
POST {{commonUrl}}

{
    "method": "queryAllProductSummary",
    "jsonrpc": "2.0",
    "params": { 

        "date": "2022-09-28",
        "monthAvgRange": "LAST_3_MONTH"
        
    },
    "id": "666666"
}
curl --request POST \
  --url 'http://localhost:80/cloud-demand-app/repo-show?api_key=no' \
  --header 'content-type: application/json' \
  --header 'user-agent: vscode-restclient' \
  --data '{"method": "queryAllProductSummary","jsonrpc": "2.0","params": {"date": "2022-09-28","monthAvgRange": "LAST_3_MONTH"},"id": "666666"}'


###
POST {{commonUrl}}
###
POST http://************:80/cloud-demand-app/repo-show?api_key=no
{{contentType}}

{
    "method": "syncYgInventory",
    "jsonrpc": "2.0",
    "params": { 
        
    },
    "id": "666666"
}



###
POST {{commonUrl}}

{
    "method": "isContainSupplyChainDate",
    "jsonrpc": "2.0",
    "params": { 
        "date": "2022-10-13"
    },
    "id": "666666"
}

###
curl --request POST \
  --url http://api.tcres.woa.com/api/module/get_area_region_zone_campus \
  --header 'Cookie: userName=fireflychen;  DiggerTraceId=8c463b40-290d-11ed-aaed-a5efa0780f17; x-imp-host-key=183125311a9-92d8dbcd429196d2c8d7031fbe1e7d3022b41d79; x-host-key-ngn=18316ad2ce8-1b10b35a99c3891ddecddd66b4a8f64baa1c4d4a; t_uid=fireflychen; x-host-key-front=1833f1ae5a2-990f876fcc053f13d89f34ac709f305b576e3179; tkex_csrf_token=e5kle3qfav; tapdsession=8fb9b9b87dbdc6b9f44ae7a343292b63; km_u=27e32eb5c9ecb4884e79bc3cfd4c0171a48bff2bda0bbdcb607ffd5d4c9c4b4fd4d9ee452559556b; km_uid=fireflychen; bk_uid=fireflychen; bk_ticket=BvqTGmOtkLmKXTS2-QUDAAW73c1xmcC9fS0vXazhYcg; beacon_uin=fireflychen; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IkdOa0tkMUljNzVpQkJZUjhQbWlQRWd2eHl3T1ZNWVBuIiwiaXNzIjoiMTAuOTkuMjA4LjU2IiwiaWF0IjoiMjAyMi0xMC0xMVQwOToyNzozOC4xNjQ4MjU0MzgrMDg6MDAiLCJhdWQiOiIxMC44OS4zLjQwIiwiaGFzaCI6Ijg4RjA5NTE5NThGODMyNDFEQjk1QzJDNjlGOUJGNEVCNUI0OUI0REI2NDM4RUI1NTk0MkZEMDhGQjIyNEFDMDAiLCJuaCI6IjNGREQ4NkRFRTdGQkEwQUIwODQ1NkNGOEUxNEExRDNDRThGQ0I5NkExQkNEODgwNTU0MzJGNEVGNjgwOTMxMDQifQ; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6IkdOa0tkMUljNzVpQkJZUjhQbWlQRWd2eHl3T1ZNWVBuIiwiaXNzIjoiMTAuOTkuMjA4LjU2IiwiaWF0IjoiMjAyMi0xMC0xMVQwOToyNzozOC4xNjQ4MjU0MzgrMDg6MDAiLCJhdWQiOiIxMC44OS4zLjQwIiwiaGFzaCI6Ijg4RjA5NTE5NThGODMyNDFEQjk1QzJDNjlGOUJGNEVCNUI0OUI0REI2NDM4RUI1NTk0MkZEMDhGQjIyNEFDMDAiLCJuaCI6IjNGREQ4NkRFRTdGQkEwQUIwODQ1NkNGOEUxNEExRDNDRThGQ0I5NkExQkNEODgwNTU0MzJGNEVGNjgwOTMxMDQifQ;' \
  --header 'staffname: fireflychen' \
  --header 'user-agent: vscode-restclient' \
  --data '{"params": {}}'
