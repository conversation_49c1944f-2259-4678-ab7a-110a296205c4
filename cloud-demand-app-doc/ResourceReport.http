

@commonUrl = {{local}}/cloud-demand-app/resource-report?api_key=no\n{{contentType}} 


POST http://xor.oa.com/cloud-demand-app/resource-report?api_key=no

###
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "tcres-vue316623468522070.6931607145940695",
  "method": "queryResourceReportItem",
  "params": {
    "startVersion": "20220828",
    "endVersion": "20220904",
    "startMonth": "202201",
    "endMonth": "202212"
  }
}

###

{
  "jsonrpc": "2.0",
  "id": "tcres-vue316612546529210.7147812322657858",
  "method": "queryResourceReportItem",
  "params": {
    "startVersion": "20220816",
    "endVersion": "20220823",
    "startMonth": "202201",
    "endMonth": "202212",
    "dimBigClass": []
  }
}

###
{
    "method": "queryResourceReportItem",
    "jsonrpc": "2.0",
    "params": { 
        "startVersion": "20220818",
        "endVersion": "20220819",
        "startMonth": "202202",
        "endMonth": "202208",
        "dim": [
            "dimBigClass",
            "dimProductClass",
            "dimInstanceType",
            "dimRegionClass"
        ]
    },
    "id": "666666"
}


###

POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "tcres-vue316618626958990.12640776066849413",
  "method": "queryIndexDetails",
  "params": {
    "startVersion": "20220822",
    "endVersion": "20220829",
    "startMonth": "202201",
    "endMonth": "202212",
    "dim": [
      "yearMonth"
    ],
    "dimBigClass": [
      "计算"
    ],
    "dimProductClass": [
      "CVM"
    ],
    "filterIndexValue": [
      "全年需求预测量"
    ]
  }
}
###
{
    "method": "queryParams",
    "jsonrpc": "2.0",
    "params": { 
        "paramType" : "dimDeviceType"
    },
    "id": "666666"
}




####
POST {{commonUrl}}

{
    "method": "queryMinVersion",
    "jsonrpc": "2.0",
    "params": { 
        
    },
    "id": "666666"
}


###
POST {{commonUrl}}

{
    "method": "queryIndexDetails",
    "jsonrpc": "2.0",
    "params": { 
        "startVersion": "20220818",
        "endVersion": "20220819",
        "startMonth": "202202",
        "endMonth": "202208",
        "dimProductClass": ["CVM"],
        "dim" : ["yearMonth"],
        "filterIndexValue": ["ERP采购下单量"]
        
    },

    "id": "666666"
}



### note 相关的笔记

POST {{commonUrl}}

{
    "method": "insertNote",
    "jsonrpc": "2.0",
    "params": { 
        
        "startVersion": "2022-08-08",
        "endVersion": "2022-08-08",
        "startMonth" : "20220808",
        "endMonth" : "20220808",


        "dimName" : ["dimBigClass","dimProductClass"],
        "dimValue": ["计算","CVM"],

        "filter": {
          "dimBigClass": ["test filter", "test filter"]
        },

        "filterIndexValue": ["testIndex"],
        "note": "sdftest data test data "
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "queryNote",
    "jsonrpc": "2.0",
    "params": { 
        
        "startVersion": "2022-08-08",
        "endVersion": "2022-08-08",
        "startMonth" : "20220808",
        "endMonth" : "20220808",
        "filterIndexValue": ["testIndex"],
        "note": "sdftest data test data "
    },
    "id": "666666"
}



####
POST {{commonUrl}}

{
    "method": "updateNoteById",
    "jsonrpc": "2.0",
    "params": { 
       "id" : 1,
       "note": "update update update  sdfadfdsafasdf"
    },
    "id": "666666"
}

###
POST {{commonUrl}}

{
    "method": "deleteNoteById",
    "jsonrpc": "2.0",
    "params": { 
        "id": 2
    },
    "id": "666666"
}


#### 
POST {{commonUrl}}

{
  "jsonrpc": "2.0",
  "id": "tcres-vue316623519356840.312855376422843",
  "method": "getDiffData",
  "params": {
    "startVersion": "20220828",
    "endVersion": "20220904",
    "startMonth": "202201",
    "endMonth": "202212",
    "dim": [
      "dimDeviceType"
    ],
    "dimBigClass": [
      "计算"
    ],
    "dimProductClass": [
      "CVM"
    ],
    "dimDeviceType":[
      "T0-CS69A-100G"
    ],
    "filterIndexValue": [
      "已执行预测"
    ]
  }
}


####


{
  "method": "getDiffData",
  "jsonrpc": "2.0",
  "params": { 
    "startVersion": "20220828",
    "endVersion": "20220901",
    "startMonth": "201801",
    "endMonth": "202212",
    "filterIndexValue": ["已交付量（交付月份）"]
  },
  "id": "666666"
}